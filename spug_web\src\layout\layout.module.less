// H3C异构运维平台 - 科技蓝主题配色
:root {
  --primary-color: #1890ff;
  --secondary-color: #0066cc;
  --accent-color: #00d4ff;
  --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --header-bg: #001529;
  --sider-bg: #001529;
}

.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 12px 0 0;
  height: 48px;
  line-height: 48px;
  background: var(--header-bg);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
  z-index: 2;

  .right {
    display: flex;
    flex-direction: row;
    align-items: center;

    .link {
      color: #ffffff;
      cursor: pointer;
      padding: 0 16px;
      transition: all 0.3s;

      &:hover {
        background: rgba(24, 144, 255, 0.2);
        color: var(--accent-color);
      }
    }
  }

  .terminal {
    padding: 0 12px;
    cursor: pointer;
    line-height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffffff;

    &:hover {
      background: rgba(24, 144, 255, 0.2);
      color: var(--accent-color);
    }
  }

  .user {
    .action {
      cursor: pointer;
      padding: 0 12px;
      display: inline-block;
      transition: all 0.3s;
      height: 100%;
      color: #ffffff;
    }

    .action:hover {
      background: rgba(24, 144, 255, 0.2);
      color: var(--accent-color);
    }
  }

  .trigger {
    cursor: pointer;
    transition: all 0.3s;
    padding: 0 12px;
    color: #ffffff;
  }

  .trigger:hover {
    background: rgba(24, 144, 255, 0.2);
    color: var(--accent-color);
  }
}

.notify {
  width: 350px;
  padding: 0;

  .item {
    align-items: center;
    cursor: pointer;
    padding: 12px 24px;
  }

  .item:hover {
    background-color: rgb(233, 247, 254);
  }

  .btn {
    line-height: 46px;
    text-align: center;
    cursor: pointer;
    border-top: 1px solid #e8e8e8;
  }
}

.notify :global(.ant-dropdown-menu-item:hover) {
  background-color: #fff;
}

.content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 24px;
  overflow-y: scroll;
}

.content::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.sider {
  height: 100%;
  width: 208px;
  min-height: 100vh;
  background: var(--sider-bg) !important;
  box-shadow: 2px 0 8px 0 rgba(24, 144, 255, 0.1);
  overflow: hidden;

  .menus {
    overflow: auto;
    background: var(--sider-bg);
  }

  .menus::-webkit-scrollbar {
    width: 4px;
  }

  .menus::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }

  .menus::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 2px;
  }

  .logo {
    height: 64px;
    line-height: 64px;
    overflow: hidden;
    text-align: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  }

  .logo img {
    height: 30px;
    filter: brightness(1.1);
  }
}

.footer {
  width: 100%;
  padding: 20px;
  font-size: 14px;
  text-align: center;
  display: flex;
  flex-direction: column;

  .links {
    margin-bottom: 7px;

    .item {
      margin-right: 40px;
    }
  }
}