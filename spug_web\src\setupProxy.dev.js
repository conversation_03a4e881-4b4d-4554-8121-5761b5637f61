/**
 * 开发环境代理配置
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
const proxy = require('http-proxy-middleware');

module.exports = function (app) {
  app.use(proxy('/api/', {
    target: 'http://127.0.0.1:9999',  // 开发环境API端口
    changeOrigin: true,
    ws: true,
    headers: {'X-Real-IP': '*******'},
    pathRewrite: {
      '^/api': ''
    }
  }));
};
