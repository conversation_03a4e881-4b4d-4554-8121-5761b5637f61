import React, { useState, useEffect } from 'react';
import { Card, Button, Spin, Statistic } from 'antd';
import { AreaChartOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import http from 'libs/http';
import styles from './index.module.less';

function GpuManagementCard() {
  const [loading, setLoading] = useState(true);
  const [gpuCount, setGpuCount] = useState(0);

  useEffect(() => {
    http.get('/api/model-storage/gpus/')
      .then(res => setGpuCount(res.length))
      .finally(() => setLoading(false));
  }, []);

  return (
    <Card
      className={styles['glass-card']}
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <AreaChartOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          GPU设备管理
        </div>
      }
      extra={<Link to="/model-storage/gpu">更多</Link>}
    >
      <Spin spinning={loading}>
        <Statistic title="已纳管GPU设备总数" value={gpuCount} />
        <p style={{ marginTop: 16, color: '#8c8c8c' }}>
          点击“更多”以管理GPU设备，查看详情及相关测试任务。
        </p>
      </Spin>
    </Card>
  );
}

export default GpuManagementCard; 