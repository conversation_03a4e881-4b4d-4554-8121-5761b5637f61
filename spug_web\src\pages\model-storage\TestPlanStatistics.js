import React, { useEffect } from 'react';
import { observer } from 'mobx-react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Progress, 
  List, 
  Tag,
  Typography,
  Space,
  Avatar,
  Divider
} from 'antd';
import {
  DashboardOutlined,
  TeamOutlined,
  ThunderboltOutlined,
  ExclamationCircleOutlined,
  FireOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  Bar<PERSON><PERSON>Outlined,
  WarningOutlined,
  DatabaseOutlined
} from '@ant-design/icons';
import store from './store';
import styles from './TestPlanStatistics.module.less';

const { Title, Text } = Typography;

const TestPlanStatistics = observer(() => {
  useEffect(() => {
    store.fetchTestPlanStatistics();
  }, []);

  const { overview, personnel, gpu_resources, risk_alerts } = store.testPlanStats;

  const getStatusColor = (status) => {
    const colorMap = {
      'completed': '#52c41a',
      'in_progress': '#1890ff',
      'pending': '#faad14',
      'delayed': '#ff4d4f',
      'blocked': '#f5222d',
      'cancelled': '#8c8c8c'
    };
    return colorMap[status] || '#d9d9d9';
  };

  const getPriorityColor = (priority) => {
    const colorMap = {
      'p1': '#ff4d4f',
      'p2': '#faad14',
      'p3': '#52c41a'
    };
    return colorMap[priority] || '#d9d9d9';
  };

  const getVendorIcon = (vendor) => {
    const iconMap = {
      'NVIDIA': '🔥',
      'AMD': '⚡',
      'KUNLUNXIN': '🚀',
      'Intel': '💎',
    };
    return iconMap[vendor] || '🖥️';
  };



  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Title level={3} className={styles.title}>
          <DashboardOutlined /> 测试计划统计概览
        </Title>
        <Text className={styles.subtitle}>实时监控测试计划执行情况和资源使用状态</Text>
      </div>
      
      <Row gutter={[24, 24]} className={styles.statsGrid}>
        {/* 总体概况卡片 */}
        <Col xs={24} sm={12} lg={6}>
          <Card className={styles.overviewCard} bordered={false}>
            <div className={styles.cardHeader}>
              <div className={styles.iconWrapper}>
                <DashboardOutlined className={styles.cardIcon} />
              </div>
              <div className={styles.cardTitle}>总体概况</div>
            </div>
            
            <div className={styles.statsRow}>
              <div className={styles.statItem}>
                <div className={styles.statValue}>{overview?.total_plans || 0}</div>
                <div className={styles.statLabel}>发布计划</div>
              </div>
              <div className={styles.statItem}>
                <div className={styles.statValue}>{overview?.total_tasks || 0}</div>
                <div className={styles.statLabel}>测试任务</div>
              </div>
            </div>
            
            <div className={styles.progressSection}>
              <Progress
                type="circle"
                percent={Math.round(overview?.completion_rate || 0)}
                size={70}
                strokeWidth={8}
                strokeColor={{
                  '0%': '#667eea',
                  '100%': '#764ba2'
                }}
                trailColor="#f0f2f5"
                format={percent => (
                  <span className={styles.progressText}>
                    {percent}%
                  </span>
                )}
              />
              <div className={styles.progressLabel}>完成率</div>
            </div>
          </Card>
        </Col>

        {/* 人员效率卡片 */}
        <Col xs={24} sm={12} lg={6}>
          <Card className={styles.personnelCard} bordered={false}>
            <div className={styles.cardHeader}>
              <div className={styles.iconWrapper}>
                <TeamOutlined className={styles.cardIcon} />
              </div>
              <div className={styles.cardTitle}>人员效率</div>
            </div>
            
            <div className={styles.mainStat}>
              <div className={styles.mainStatValue}>{personnel?.total_testers || 0}</div>
              <div className={styles.mainStatLabel}>测试人员</div>
            </div>
            
            <Divider className={styles.divider} />
            
            <div className={styles.rankingSection}>
              <div className={styles.sectionTitle}>
                <TrophyOutlined className={styles.sectionIcon} />
                任务数
              </div>
              <div className={styles.rankingList}>
                {(personnel?.tester_stats?.slice(0, 3) || []).map((item, index) => (
                  <div key={index} className={styles.rankingItem}>
                    <div className={styles.rankingLeft}>
                      <div className={`${styles.rankingBadge} ${styles[`rank${index + 1}`]}`}>
                        {index + 1}
                      </div>
                      <span className={styles.testerName}>{item.tester || '未知'}</span>
                    </div>
                    <div className={styles.completedCount}>
                      {item.completed_tasks || 0}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </Col>

        {/* GPU资源卡片 */}
        <Col xs={24} sm={12} lg={6}>
          <Card className={styles.gpuCard} bordered={false}>
             <div className={styles.cardHeader}>
               <div className={styles.iconWrapper}>
                 <DatabaseOutlined className={styles.cardIcon} />
               </div>
               <div className={styles.cardTitle}>GPU资源</div>
             </div>
            
            <div className={styles.gpuStats}>
              <div className={styles.gpuStatItem}>
                <div className={styles.gpuStatValue}>{gpu_resources?.total_devices || 0}</div>
                <div className={styles.gpuStatLabel}>总设备</div>
              </div>
              <div className={styles.gpuStatItem}>
                <div className={styles.gpuStatValue}>{gpu_resources?.active_devices || 0}</div>
                <div className={styles.gpuStatLabel}>使用中</div>
              </div>
            </div>
            
            <Divider className={styles.divider} />
            
            <div className={styles.modelsSection}>
              <div className={styles.sectionTitle}>
                <FireOutlined className={styles.sectionIcon} />
                热门型号
              </div>
              <div className={styles.modelsList}>
                {(gpu_resources?.popular_models?.slice(0, 3) || []).map((item, index) => (
                  <div key={index} className={styles.modelItem}>
                    <div className={styles.modelLeft}>
                       <div className={styles.modelIcon}>
                         {item.gpu_model?.charAt(0) || 'G'}
                       </div>
                       <span className={styles.modelName}>{item.gpu_model || '未知型号'}</span>
                     </div>
                    <div className={styles.modelCount}>
                      {item.usage_count || 0}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </Col>

        {/* 风险预警卡片 */}
        <Col xs={24} sm={12} lg={6}>
          <Card className={styles.riskCard} bordered={false}>
            <div className={styles.cardHeader}>
              <div className={styles.iconWrapper}>
                <ExclamationCircleOutlined className={styles.cardIcon} />
              </div>
              <div className={styles.cardTitle}>风险预警</div>
            </div>
            
            <div className={styles.mainStat}>
              <div className={styles.mainStatValue}>{risk_alerts?.total_risk_tasks || 0}</div>
              <div className={styles.mainStatLabel}>风险任务</div>
            </div>
            
            <Divider className={styles.divider} />
            
            <div className={styles.riskSection}>
              <div className={styles.sectionTitle}>
                <ExclamationCircleOutlined className={styles.sectionIcon} />
                风险详情
              </div>
              <div className={styles.riskList}>
                <div className={styles.riskItem}>
                  <div className={styles.riskLeft}>
                    <div className={`${styles.riskIndicator} ${styles.highPriority}`}>
                    </div>
                    <span className={styles.riskLabel}>高优先级</span>
                  </div>
                  <div className={styles.riskCount}>
                    {risk_alerts?.high_priority_tasks || 0}
                  </div>
                </div>
                <div className={styles.riskItem}>
                  <div className={styles.riskLeft}>
                    <div className={`${styles.riskIndicator} ${styles.delayed}`}>
                    </div>
                    <span className={styles.riskLabel}>延期任务</span>
                  </div>
                  <div className={styles.riskCount}>
                    {risk_alerts?.delayed_tasks || 0}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
});

export default TestPlanStatistics;
