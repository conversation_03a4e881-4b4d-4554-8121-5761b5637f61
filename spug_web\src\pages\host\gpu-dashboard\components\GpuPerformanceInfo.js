/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React from 'react';
import { Card, Progress, Tag, Space, Row, Col, Statistic, Descriptions, Alert, Empty } from 'antd';
import { LineChartOutlined } from '@ant-design/icons';

function GpuPerformanceInfo({ data = {}, loading }) {
  console.log('[DEBUG GpuPerformanceInfo] 接收到的数据:', data);
  
  // 测试用模拟数据
  const mockData = {
    gpu_performance: Array.from({ length: 8 }, (_, i) => ({
      index: String(i),
      temperature: `${38 + i % 3}°C`,
      gpu_utilization: '0%',
      power_draw: `${86 + i % 5}W`,
      memory_clock: '1450 MHz'
    })),
    memory_usage: Array.from({ length: 8 }, (_, i) => ({
      index: String(i),
      used: '0 MB',
      free: '98304 MB',
      total: '98304 MB'
    }))
  };
  
  // 如果没有数据，使用模拟数据进行测试
  const effectiveData = data?.gpu_performance?.length > 0 ? data : mockData;
  console.log('[DEBUG GpuPerformanceInfo] 使用的数据:', effectiveData);

  const getTemperatureStatus = (temp) => {
    if (!temp) return { color: 'default', level: 'unknown' };
    if (temp > 85) return { color: 'error', level: 'critical' };
    if (temp > 75) return { color: 'warning', level: 'high' };
    if (temp > 65) return { color: 'orange', level: 'normal' };
    return { color: 'success', level: 'cool' };
  };

  const getPowerStatus = (power, maxPower) => {
    if (!power || !maxPower) return { color: 'default', level: 'unknown' };
    const percentage = (power / maxPower) * 100;
    if (percentage > 90) return { color: 'error', level: 'high' };
    if (percentage > 75) return { color: 'warning', level: 'medium' };
    return { color: 'success', level: 'normal' };
  };

  const getUtilizationColor = (utilization) => {
    if (utilization > 90) return '#ff4d4f';
    if (utilization > 70) return '#faad14';
    if (utilization > 50) return '#52c41a';
    return '#40a9ff';
  };

  const formatFrequency = (freq) => {
    if (!freq) return '未知';
    return freq >= 1000 ? `${(freq / 1000).toFixed(1)} GHz` : `${freq} MHz`;
  };

  const hasHighTemperature = data?.gpus?.some(gpu => gpu.temperature > 80);
  const hasHighPowerUsage = data?.gpus?.some(gpu => 
    gpu.power_draw && gpu.power_limit && (gpu.power_draw / gpu.power_limit) > 0.9
  );

  const getUtilizationStatus = (utilization) => {
    if (utilization > 90) return { color: 'error', level: 'high' };
    if (utilization > 70) return { color: 'warning', level: 'medium' };
    if (utilization > 50) return { color: 'success', level: 'normal' };
    return { color: 'success', level: 'cool' };
  };

  return (
    <Card
      title={
        <Space>
          <LineChartOutlined style={{ color: '#10b981' }} />
          <span>GPU性能状态</span>
        </Space>
      }
      extra={
        <Space>
          <Tag color="blue">实时数据</Tag>
        </Space>
      }
      loading={loading}
      style={{ height: '100%' }}
    >
      {!effectiveData?.gpu_performance || effectiveData.gpu_performance.length === 0 ? (
        <Empty description="暂无GPU性能数据" />
      ) : (
        <Row gutter={[16, 16]}>
          {/* GPU性能卡片 */}
          {effectiveData.gpu_performance.map((gpu, index) => {
            const tempStatus = getTemperatureStatus(gpu.temperature);
            const utilStatus = getUtilizationStatus(gpu.gpu_utilization);
            
            return (
              <Col key={gpu.index} xs={24} sm={12} md={8} lg={6}>
                <Card 
                  size="small" 
                  title={
                    <Space>
                      <span>GPU {gpu.index}</span>
                      <Tag color={utilStatus.color}>{utilStatus.level}</Tag>
                    </Space>
                  }
                  style={{ marginBottom: 16 }}
                >
                  <Descriptions size="small" column={1}>
                    <Descriptions.Item label="温度">
                      <Space>
                        <span>{gpu.temperature}</span>
                        <Tag color={tempStatus.color}>{tempStatus.level}</Tag>
                      </Space>
                    </Descriptions.Item>
                    <Descriptions.Item label="利用率">
                      <Progress 
                        percent={parseInt(gpu.gpu_utilization)} 
                        size="small"
                        status={
                          parseInt(gpu.gpu_utilization) > 80 ? 'exception' : 
                          parseInt(gpu.gpu_utilization) > 50 ? 'normal' : 'success'
                        }
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="功耗">
                      {gpu.power_draw}
                    </Descriptions.Item>
                    <Descriptions.Item label="显存频率">
                      {gpu.memory_clock}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>
            );
          })}
          
          {/* 显存使用情况 */}
          {effectiveData.memory_usage && effectiveData.memory_usage.map((mem, index) => (
            <Col key={`mem-${mem.index}`} xs={24} sm={12} md={8} lg={6}>
              <Card 
                size="small" 
                title={`GPU ${mem.index} 显存`}
                style={{ marginBottom: 16 }}
              >
                <Statistic
                  title="已用/总量"
                  value={`${mem.used} / ${mem.total}`}
                  valueStyle={{ fontSize: '14px' }}
                />
                <Progress 
                  percent={Math.round((parseInt(mem.used) / parseInt(mem.total)) * 100) || 0} 
                  size="small"
                  status={
                    Math.round((parseInt(mem.used) / parseInt(mem.total)) * 100) > 80 ? 'exception' : 
                    Math.round((parseInt(mem.used) / parseInt(mem.total)) * 100) > 50 ? 'normal' : 'success'
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      )}
    </Card>
  );
}

export default GpuPerformanceInfo; 