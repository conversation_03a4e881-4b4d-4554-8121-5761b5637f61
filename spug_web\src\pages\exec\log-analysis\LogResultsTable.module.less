.logResultsTable {
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;
    
    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      
      .ant-card-head-title {
        font-weight: 600;
        color: #262626;
      }
    }
  }

  .ant-statistic {
    .ant-statistic-title {
      color: #666;
      font-size: 14px;
      margin-bottom: 8px;
    }
    
    .ant-statistic-content {
      color: #262626;
      font-weight: 600;
      
      .ant-statistic-content-value {
        font-size: 24px;
      }
      
      .ant-statistic-content-prefix {
        color: #1890ff;
        margin-right: 8px;
      }
      
      .ant-statistic-content-suffix {
        color: #666;
        font-size: 16px;
        margin-left: 4px;
      }
    }
  }

  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #262626;
      border-bottom: 2px solid #f0f0f0;
    }
    
    .ant-table-tbody > tr > td {
      border-bottom: 1px solid #f5f5f5;
      
      &:hover {
        background: #f9f9f9;
      }
    }
    
    .ant-table-tbody > tr:hover > td {
      background: #f0f5ff;
    }
  }

  .ant-tag {
    border-radius: 4px;
    font-weight: 500;
    
    &.ant-tag-green {
      background: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
    }
    
    &.ant-tag-orange {
      background: #fff7e6;
      border-color: #ffd591;
      color: #fa8c16;
    }
    
    &.ant-tag-red {
      background: #fff2f0;
      border-color: #ffccc7;
      color: #ff4d4f;
    }
    
    &.ant-tag-blue {
      background: #f0f5ff;
      border-color: #adc6ff;
      color: #1890ff;
    }
  }

  .ant-btn {
    border-radius: 6px;
    font-weight: 500;
    
    &.ant-btn-text {
      color: #1890ff;
      
      &:hover {
        background: #f0f5ff;
        color: #40a9ff;
      }
    }
  }

  .ant-pagination {
    margin-top: 16px;
    text-align: right;
    
    .ant-pagination-total-text {
      color: #666;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .logResultsTable {
    .ant-row {
      .ant-col {
        margin-bottom: 16px;
      }
    }
    
    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
        font-size: 12px;
      }
    }
    
    .ant-statistic {
      .ant-statistic-content {
        .ant-statistic-content-value {
          font-size: 20px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .logResultsTable {
    .ant-card {
      .ant-card-head {
        .ant-card-head-title {
          font-size: 14px;
        }
        
        .ant-card-extra {
          .ant-btn {
            font-size: 12px;
            padding: 4px 8px;
            height: auto;
          }
        }
      }
    }
    
    .ant-statistic {
      text-align: center;
      
      .ant-statistic-title {
        font-size: 12px;
      }
      
      .ant-statistic-content {
        .ant-statistic-content-value {
          font-size: 18px;
        }
        
        .ant-statistic-content-suffix {
          font-size: 14px;
        }
      }
    }
  }
}
