/* 变量配置模态框样式 */
.container {
  max-height: 600px;
  overflow-y: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  h4 {
    margin: 0;
  }
}

.variableList {
  min-height: 200px;
}

.variableItem {
  margin-bottom: 16px;
  transition: all 0.3s ease;
  
  &.dragging {
    transform: rotate(5deg);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
  
  :global(.ant-card) {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
    
    .ant-card-head {
      background: linear-gradient(135deg, #f6f9fc 0%, #e9f4ff 100%);
      border-bottom: 1px solid #e8f4fd;
      border-radius: 12px 12px 0 0;
      
      .ant-card-head-title {
        padding: 12px 0;
        font-size: 14px;
      }
    }
    
    .ant-card-body {
      padding: 16px;
    }
  }
}

.formItem {
  margin-bottom: 12px;
  
  label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #262626;
    font-size: 13px;
  }
  
  :global(.ant-input),
  :global(.ant-select-selector),
  :global(.ant-input-affix-wrapper) {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    
    &:hover {
      border-color: #667eea;
    }
    
    &:focus,
    &.ant-input-focused,
    &.ant-select-focused .ant-select-selector {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }
  }
  
  :global(.ant-switch) {
    &.ant-switch-checked {
      background-color: #667eea;
    }
  }
}

.emptyState {
  text-align: center;
  padding: 60px 0;
  color: #8c8c8c;
  
  :global(.anticon) {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }
}

/* 拖拽相关样式 */
:global(.react-beautiful-dnd-dragging) {
  .variableItem {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
  }
}

:global(.react-beautiful-dnd-drag-handle) {
  color: #8c8c8c;
  
  &:hover {
    color: #667eea;
  }
}

/* 模态框样式覆盖 */
:global(.ant-modal) {
  .ant-modal-content {
    border-radius: 16px;
    overflow: hidden;
  }
  
  .ant-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    padding: 20px 24px;
    
    .ant-modal-title {
      color: white;
      font-weight: 600;
      font-size: 16px;
    }
  }
  
  .ant-modal-close {
    color: white;
    top: 20px;
    right: 24px;
    
    &:hover {
      color: rgba(255, 255, 255, 0.8);
    }
  }
  
  .ant-modal-body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;
    
    .ant-btn {
      border-radius: 8px;
      font-weight: 500;
      
      &.ant-btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        
        &:hover {
          background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
      }
    }
  }
}

/* 标签样式 */
:global(.ant-tag) {
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  border: none;
  
  &.ant-tag-blue {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1976d2;
  }
  
  &.ant-tag-green {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #388e3c;
  }
}

/* 警告框样式 */
:global(.ant-alert) {
  border-radius: 12px;
  border: none;
  
  &.ant-alert-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-left: 4px solid #2196f3;
    
    .ant-alert-icon {
      color: #2196f3;
    }
  }
}

/* 按钮样式 */
:global(.ant-btn) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &.ant-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.4);
    
    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      box-shadow: 0 4px 8px rgba(102, 126, 234, 0.6);
      transform: translateY(-1px);
    }
  }
  
  &.ant-btn-text {
    &:hover {
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
    }
    
    &.ant-btn-dangerous:hover {
      background: rgba(255, 77, 79, 0.1);
      color: #ff4d4f;
    }
  }
}

/* 选择器样式 */
:global(.ant-select) {
  .ant-select-dropdown {
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border: none;
  }
  
  .ant-select-item {
    border-radius: 6px;
    margin: 2px 8px;
    
    &.ant-select-item-option-selected {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      color: #1976d2;
    }
  }
}

/* 输入框样式 */
:global(.ant-input),
:global(.ant-input-affix-wrapper) {
  transition: all 0.3s ease;
  
  &:focus {
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  }
}

/* 开关样式 */
:global(.ant-switch) {
  &.ant-switch-checked {
    background-color: #667eea;
    
    &:hover:not(.ant-switch-disabled) {
      background-color: #5a6fd8;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    max-height: 500px;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .variableItem {
    :global(.ant-card-body) {
      padding: 12px;
    }
  }
  
  .formItem {
    margin-bottom: 8px;
    
    label {
      font-size: 12px;
    }
  }
  
  :global(.ant-modal) {
    margin: 16px;
    max-width: calc(100vw - 32px);
    
    .ant-modal-body {
      padding: 16px;
      max-height: 60vh;
    }
  }
}

/* 自定义滚动条 */
.container {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

/* 动画效果 */
.variableItem {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
