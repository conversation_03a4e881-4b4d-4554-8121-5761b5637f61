.remoteExtractionModal {
  .container {
    padding: 20px 0;
  }

  .steps {
    margin-bottom: 24px;
  }

  .progress {
    margin-bottom: 20px;
  }

  .stepCard {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-head {
      background: linear-gradient(135deg, #f0f2f5, #fafafa);
    }
  }

  .summaryCard {
    margin-bottom: 16px;
    border-radius: 8px;
    border: 1px solid #722ed1;

    .ant-card-head {
      background: linear-gradient(135deg, #722ed1, #9254de);
      color: white;

      .ant-card-head-title {
        color: white;
      }
    }
  }

  .metricsCard {
    margin-bottom: 20px;
    border-radius: 8px;

    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
    }
  }

  .stepActions {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
  }

  .logPreview {
    background: #f6f8fa;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    padding: 16px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
  }

  .metricItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 8px;
    background: #fafafa;
    border-radius: 6px;
    border-left: 3px solid #722ed1;

    .metricLabel {
      font-weight: 500;
      color: #262626;
    }

    .metricValue {
      color: #722ed1;
      font-weight: 600;
    }

    .metricMeta {
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .analysisInfo {
    background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
    border: 1px solid #91d5ff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;

    .infoTitle {
      color: #1890ff;
      font-weight: 600;
      margin-bottom: 8px;
    }
  }

  .errorState {
    text-align: center;
    padding: 40px 20px;
    color: #8c8c8c;
  }

  .successState {
    text-align: center;
    padding: 40px 20px;

    .successIcon {
      font-size: 48px;
      color: #52c41a;
      margin-bottom: 16px;
    }

    .successTitle {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 8px;
    }

    .successDesc {
      color: #8c8c8c;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .remoteExtractionModal {
    .summaryCard {
      .ant-row {
        flex-direction: column;

        .ant-col {
          margin-bottom: 16px;
        }
      }
    }

    .stepActions {
      flex-direction: column;
      align-items: stretch;

      .ant-btn {
        margin-bottom: 8px;
      }
    }
  }
}

// 深色主题支持
.dark {
  .remoteExtractionModal {
    .stepCard {
      background: #1f1f1f;
      border-color: #303030;

      .ant-card-head {
        background: linear-gradient(135deg, #262626, #1f1f1f);
        border-color: #303030;
      }
    }

    .logPreview {
      background: #141414;
      border-color: #303030;
      color: #fff;
    }

    .metricItem {
      background: #262626;
      color: #fff;

      .metricLabel {
        color: #fff;
      }
    }
  }
} 