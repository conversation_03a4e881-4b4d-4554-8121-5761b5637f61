import React from 'react';
import { Card, Button, Typography, Space } from 'antd';
import { FolderOutlined, DiffOutlined, ExportOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

export default function FileTree() {
  const handleFileCompare = () => {
    // 获取当前域名和端口
    const baseUrl = window.location.origin;
    // 构建文件对比页面的完整URL
    const compareUrl = `${baseUrl}/model-storage/file-compare?basePath=${encodeURIComponent('/HDD_Raid/SVN_MODEL_REPO')}&remoteUrl=${encodeURIComponent('http://10.63.30.93/GPU_MODEL_REPO/01.DEV/')}`;
    
    // 在新标签页打开文件对比页面
    window.open(compareUrl, '_blank');
  };

  return (
    <Card 
      title={
        <div style={{ 
          display: 'flex', 
          alignItems: 'center',
          fontSize: '16px',
          fontWeight: '600',
          color: '#1f1f1f'
        }}>
          <FolderOutlined style={{ 
            marginRight: 8, 
            color: '#1890ff',
            fontSize: '18px'
          }} />
          <span>文件树</span>
        </div>
      }
      style={{ 
        height: 'calc(100vh - 280px)',
        borderRadius: '16px',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(10px)',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        border: 'none'
      }}
      bodyStyle={{ 
        padding: '24px',
        height: 'calc(100% - 64px)',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}
    >
      <Space direction="vertical" size="large" style={{ textAlign: 'center' }}>
        <div>
          <DiffOutlined style={{ 
            fontSize: '48px', 
            color: '#1890ff',
            marginBottom: '16px'
          }} />
        </div>
        
        <div>
          <Title level={4} style={{ margin: 0, color: '#1f1f1f' }}>
            文件树对比
          </Title>
          <Text type="secondary" style={{ fontSize: '14px' }}>
            对比本地仓库与远程仓库的文件差异
          </Text>
        </div>

        <Button 
          type="primary" 
          size="large"
          icon={<ExportOutlined />}
          onClick={handleFileCompare}
          style={{
            borderRadius: '8px',
            height: '48px',
            padding: '0 24px',
            fontSize: '16px',
            fontWeight: '500',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            boxShadow: '0 4px 15px rgba(102, 126, 234, 0.4)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.target.style.transform = 'translateY(-2px)';
            e.target.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.6)';
          }}
          onMouseLeave={(e) => {
            e.target.style.transform = 'translateY(0)';
            e.target.style.boxShadow = '0 4px 15px rgba(102, 126, 234, 0.4)';
          }}
        >
          开始文件对比
        </Button>

        <div style={{ marginTop: '16px' }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            • 本地仓库：/HDD_Raid/SVN_MODEL_REPO
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            • 远程仓库：http://10.63.30.93/GPU_MODEL_REPO/01.DEV/
          </Text>
        </div>
      </Space>
    </Card>
  );
} 