<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spug系统 - 用户手册总览</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: 4rem;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto 30px;
        }

        .version-info {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 1rem;
        }

        /* 主要内容区域 */
        .main-content {
            padding: 80px 0;
        }

        .section-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 60px;
        }

        /* 手册卡片网格 */
        .manuals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .manual-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
            position: relative;
            overflow: hidden;
        }

        .manual-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color, #667eea);
        }

        .manual-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .manual-icon {
            font-size: 4rem;
            margin-bottom: 25px;
            color: var(--card-color, #667eea);
        }

        .manual-title {
            font-size: 1.8rem;
            margin-bottom: 15px;
            color: #2c3e50;
            font-weight: 600;
        }

        .manual-description {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .manual-features {
            list-style: none;
            text-align: left;
            margin-bottom: 30px;
        }

        .manual-features li {
            padding: 5px 0;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .manual-features li::before {
            content: '✓';
            color: var(--card-color, #667eea);
            font-weight: bold;
            margin-right: 8px;
        }

        .manual-link {
            display: inline-block;
            background: var(--card-color, #667eea);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .manual-link:hover {
            background: var(--card-hover-color, #5a67d8);
            transform: translateY(-2px);
        }

        /* 不同卡片的颜色主题 */
        .manual-card.model-storage {
            --card-color: #667eea;
            --card-hover-color: #5a67d8;
        }

        .manual-card.document-management {
            --card-color: #764ba2;
            --card-hover-color: #6a4190;
        }

        .manual-card.gpu-management {
            --card-color: #f093fb;
            --card-hover-color: #e081e9;
        }

        .manual-card.test-case-sets {
            --card-color: #06b6d4;
            --card-hover-color: #0891b2;
        }

        .manual-card.test-plan {
            --card-color: #10b981;
            --card-hover-color: #059669;
        }

        /* 统计信息 */
        .stats-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 60px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            text-align: center;
        }

        .stat-item {
            padding: 20px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1rem;
        }

        /* 底部 */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .manuals-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .manual-card {
                padding: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1><i class="fas fa-book"></i> Spug系统用户手册</h1>
                <p>全面的系统功能指南与操作教程</p>
                <div class="version-info">
                    <i class="fas fa-tag"></i> 版本 v2.0
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 系统概述 -->
            <section class="overview-section">
                <h2 class="section-title">系统概述</h2>
                <p class="section-subtitle">Spug是一个现代化的运维管理平台，提供模型存储、文档管理、GPU监控、测试管理等全方位功能</p>
                
                <!-- 统计信息 -->
                <div class="stats-section">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">5</div>
                            <div class="stat-label">功能模块</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">核心功能</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">100+</div>
                            <div class="stat-label">操作指南</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">24</div>
                            <div class="stat-label">系统监控</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 用户手册列表 -->
            <section class="manuals-section">
                <h2 class="section-title">用户手册</h2>
                <p class="section-subtitle">选择相应的模块查看详细的使用指南</p>
                
                <div class="manuals-grid">
                    <!-- 模型存储管理 -->
                    <div class="manual-card model-storage">
                        <div class="manual-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h3 class="manual-title">模型存储管理</h3>
                        <p class="manual-description">
                            专业的模型文件存储与管理系统，提供文件浏览、版本控制、测试计划和统计分析功能。
                        </p>
                        <ul class="manual-features">
                            <li>远程服务器文件管理</li>
                            <li>测试计划与任务跟踪</li>
                            <li>文件比较与差异分析</li>
                            <li>统计报表与数据可视化</li>
                            <li>HTML周报自动生成</li>
                        </ul>
                        <a href="model-storage-user-manual.html" class="manual-link">
                            <i class="fas fa-arrow-right"></i> 查看手册
                        </a>
                    </div>

                    <!-- 文档管理系统 -->
                    <div class="manual-card document-management">
                        <div class="manual-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h3 class="manual-title">文档管理系统</h3>
                        <p class="manual-description">
                            智能化的Word文档模板管理与自动生成平台，支持变量配置和批量生成。
                        </p>
                        <ul class="manual-features">
                            <li>Word模板管理与版本控制</li>
                            <li>灵活的变量配置系统</li>
                            <li>自动文档生成与预览</li>
                            <li>文档实例管理</li>
                            <li>富文本编辑与导出</li>
                        </ul>
                        <a href="document-management-user-manual.html" class="manual-link">
                            <i class="fas fa-arrow-right"></i> 查看手册
                        </a>
                    </div>

                    <!-- GPU设备管理 -->
                    <div class="manual-card gpu-management">
                        <div class="manual-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <h3 class="manual-title">GPU设备管理</h3>
                        <p class="manual-description">
                            专业的GPU设备监控与测试任务管理平台，支持多厂商GPU设备的统一管理。
                        </p>
                        <ul class="manual-features">
                            <li>多厂商GPU设备注册</li>
                            <li>实时性能监控</li>
                            <li>测试任务分配与跟踪</li>
                            <li>设备分组管理</li>
                            <li>告警通知与统计报表</li>
                        </ul>
                        <a href="gpu-management-user-manual.html" class="manual-link">
                            <i class="fas fa-arrow-right"></i> 查看手册
                        </a>
                    </div>

                    <!-- 测试用例集管理 -->
                    <div class="manual-card test-case-sets">
                        <div class="manual-icon">
                            <i class="fas fa-list-check"></i>
                        </div>
                        <h3 class="manual-title">测试用例集管理</h3>
                        <p class="manual-description">
                            专业的测试用例集合管理与执行跟踪平台，支持结构化的测试用例组织。
                        </p>
                        <ul class="manual-features">
                            <li>测试用例集创建与编辑</li>
                            <li>结构化用例信息管理</li>
                            <li>执行状态跟踪</li>
                            <li>灵活的排序管理</li>
                            <li>协作编辑与统计分析</li>
                        </ul>
                        <a href="test-case-sets-user-manual.html" class="manual-link">
                            <i class="fas fa-arrow-right"></i> 查看手册
                        </a>
                    </div>

                    <!-- 测试计划管理 -->
                    <div class="manual-card test-plan">
                        <div class="manual-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h3 class="manual-title">测试计划管理</h3>
                        <p class="manual-description">
                            全面的测试计划制定与执行管理系统，提供甘特图可视化和进度跟踪功能。
                        </p>
                        <ul class="manual-features">
                            <li>测试计划创建与编辑</li>
                            <li>甘特图可视化展示</li>
                            <li>任务依赖关系管理</li>
                            <li>进度跟踪与状态更新</li>
                            <li>资源分配与团队协作</li>
                        </ul>
                        <a href="test-plan-user-manual.html" class="manual-link">
                            <i class="fas fa-arrow-right"></i> 查看手册
                        </a>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 底部 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Spug系统. 版权所有.</p>
            <p style="margin-top: 10px; opacity: 0.8;">
                如有问题请联系系统管理员 | 
                <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a>
            </p>
        </div>
    </footer>

    <script>
        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.manual-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // 统计数字动画
        function animateNumbers() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const target = parseInt(stat.textContent.replace(/\D/g, ''));
                const suffix = stat.textContent.replace(/\d/g, '');
                let current = 0;
                const increment = target / 50;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    stat.textContent = Math.floor(current) + suffix;
                }, 30);
            });
        }

        // 当统计区域进入视口时触发动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateNumbers();
                    observer.unobserve(entry.target);
                }
            });
        });

        const statsSection = document.querySelector('.stats-section');
        if (statsSection) {
            observer.observe(statsSection);
        }
    </script>
</body>
</html>
