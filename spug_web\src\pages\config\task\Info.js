/**
 * 任务配置管理
 */
import React from 'react';
import { observer } from 'mobx-react';
import { Modal } from 'antd';
import store from './store';
import history from 'libs/history';

@observer
class ComInfo extends React.Component {
  
  handleViewConfig = () => {
    const task = store.record;
    // 跳转到任务配置页面
    history.push(`/config/setting/task/${task.id}`);
    store.infoVisible = false;
  };

  render() {
    const task = store.record;
    return (
      <Modal
        visible
        width={600}
        maskClosable={false}
        title="任务信息"
        onCancel={() => store.infoVisible = false}
        onOk={this.handleViewConfig}
        okText="进入配置管理">
        <div style={{padding: '20px 0'}}>
          <p><strong>任务名称：</strong>{task.name}</p>
          <p><strong>任务标识：</strong>{task.key}</p>
          <p><strong>描述信息：</strong>{task.desc || '无'}</p>
          <p><strong>创建时间：</strong>{task.created_at}</p>
          <div style={{marginTop: 20, padding: 16, backgroundColor: '#f0f8ff', border: '1px solid #d1e7dd', borderRadius: 4}}>
            <p style={{margin: 0, color: '#0d6efd'}}>
              💡 点击"进入配置管理"可以为该任务配置版本和分配配套文件
            </p>
          </div>
        </div>
      </Modal>
    )
  }
}

export default ComInfo 