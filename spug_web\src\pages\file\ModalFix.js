/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React from 'react';
import { Modal, Button, Form, Input, message } from 'antd';
import { ACEditor } from 'components';

// 使用Modal的静态方法创建Modal
export function showNewFileModal(onSuccess, path) {
  const config = {
    title: '新增文件',
    width: 800,
    content: (
      <div>
        <Form layout="vertical">
          <Form.Item label="文件名称" required>
            <Input placeholder="请输入文件名称" />
          </Form.Item>
          <Form.Item label="文件内容" required>
            <ACEditor 
              mode="sh"
              value="#!/bin/bash\n\n# 这是一个新文件\n\necho 'Hello World!'"
              height="300px"
            />
          </Form.Item>
        </Form>
      </div>
    ),
    onOk: () => {
      message.success('保存成功');
      Modal.destroyAll();
      if (onSuccess) {
        onSuccess();
      }
    },
    okText: '保存文件',
    cancelText: '取消',
    maskClosable: false
  };

  Modal.confirm(config);
}

// 组件供外部引用
function ModalFix() {
  return (
    <Button 
      type="primary" 
      danger
      onClick={() => showNewFileModal(() => message.info('成功回调'))}
    >
      全局Modal测试
    </Button>
  );
}

export default ModalFix; 