/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Tabs, 
  Table, 
  Switch, 
  Button, 
  Space, 
  Input, 
  Select, 
  Form, 
  Tooltip,
  Divider,
  Card,
  Tag
} from 'antd';
import { 
  SettingOutlined, 
  PlusOutlined, 
  DeleteOutlined, 
  QuestionCircleOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { TabPane } = Tabs;
const { TextArea } = Input;

// 默认字段配置
const DEFAULT_FIELD_CONFIG = {
  hostBasic: [
    { key: 'hostname', name: '主机名', command: 'hostname', enabled: true, parser: 'direct' },
    { key: 'os_version', name: '操作系统版本', command: 'lsb_release -d', enabled: true, parser: 'regex', regex: 'Description:\\s*(.+)' },
    { key: 'kernel_version', name: '内核版本', command: 'uname -r', enabled: true, parser: 'direct' },
    { key: 'cpu_info', name: 'CPU信息', command: 'lscpu | grep "Model name"', enabled: true, parser: 'regex', regex: 'Model name:\\s*(.+)' },
    { key: 'memory_info', name: '内存信息', command: 'free -h | grep Mem', enabled: true, parser: 'custom' },
    { key: 'uptime', name: '运行时间', command: 'uptime -p', enabled: true, parser: 'direct' },
  ],
  gpuBasic: [
    { key: 'nvidia_smi', name: 'NVIDIA GPU信息', command: 'nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader', enabled: true, parser: 'csv' },
    { key: 'gpu_count', name: 'GPU数量', command: 'nvidia-smi -L | wc -l', enabled: true, parser: 'direct' },
    { key: 'cuda_version', name: 'CUDA版本', command: 'nvcc --version | grep release', enabled: true, parser: 'regex', regex: 'release ([0-9.]+)' },
  ],
  gpuDriver: [
    { key: 'nvidia_driver', name: 'NVIDIA驱动版本', command: 'nvidia-smi --query-gpu=driver_version --format=csv,noheader', enabled: true, parser: 'direct' },
    { key: 'cuda_driver', name: 'CUDA驱动版本', command: 'cat /proc/driver/nvidia/version', enabled: true, parser: 'regex', regex: 'NVRM version: (.+)' },
    { key: 'gpu_modules', name: 'GPU内核模块', command: 'lsmod | grep nvidia', enabled: true, parser: 'custom' },
  ],
  gpuPerformance: [
    { key: 'gpu_utilization', name: 'GPU利用率', command: 'nvidia-smi --query-gpu=utilization.gpu --format=csv,noheader,nounits', enabled: true, parser: 'direct' },
    { key: 'gpu_temperature', name: 'GPU温度', command: 'nvidia-smi --query-gpu=temperature.gpu --format=csv,noheader', enabled: true, parser: 'direct' },
    { key: 'gpu_power', name: 'GPU功耗', command: 'nvidia-smi --query-gpu=power.draw --format=csv,noheader', enabled: true, parser: 'direct' },
    { key: 'gpu_memory', name: 'GPU显存使用', command: 'nvidia-smi --query-gpu=memory.used,memory.total --format=csv,noheader', enabled: true, parser: 'csv' },
  ],
  systemCompatibility: [
    { key: 'pcie_info', name: 'PCIe信息', command: 'lspci | grep VGA', enabled: true, parser: 'custom' },
    { key: 'kernel_modules', name: '内核模块状态', command: 'lsmod | grep -E "(nvidia|nouveau|amdgpu)"', enabled: true, parser: 'custom' },
    { key: 'opengl_info', name: 'OpenGL信息', command: 'glxinfo | grep "OpenGL version"', enabled: false, parser: 'regex', regex: 'OpenGL version string: (.+)' },
  ],
  monitorDiagnostic: [
    { key: 'dmesg_gpu', name: 'GPU相关系统日志', command: 'dmesg | grep -i gpu | tail -10', enabled: true, parser: 'custom' },
    { key: 'nvidia_errors', name: 'NVIDIA错误日志', command: 'dmesg | grep -i nvidia | grep -i error', enabled: true, parser: 'custom' },
    { key: 'gpu_processes', name: 'GPU进程', command: 'nvidia-smi pmon -c 1', enabled: false, parser: 'custom' },
  ]
};

function FieldConfigModal({ visible, onCancel, onSave }) {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('hostBasic');
  const [fieldConfig, setFieldConfig] = useState(DEFAULT_FIELD_CONFIG);
  const [customFieldVisible, setCustomFieldVisible] = useState(false);
  const [editingField, setEditingField] = useState(null);

  useEffect(() => {
    if (visible) {
      // 从本地存储加载配置
      const savedConfig = localStorage.getItem('gpu_dashboard_field_config');
      if (savedConfig) {
        try {
          setFieldConfig(JSON.parse(savedConfig));
        } catch (error) {
          console.error('Failed to parse field config:', error);
        }
      }
    }
  }, [visible]);

  const fieldColumns = [
    {
      title: '启用',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 60,
      render: (enabled, record, index) => (
        <Switch 
          checked={enabled} 
          onChange={(checked) => handleFieldToggle(activeTab, index, checked)}
          size="small"
        />
      ),
    },
    {
      title: '字段名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '检测命令',
      dataIndex: 'command',
      key: 'command',
      ellipsis: true,
      render: (command) => (
        <Tooltip title={command}>
          <code style={{ fontSize: '12px' }}>{command}</code>
        </Tooltip>
      ),
    },
    {
      title: '解析器',
      dataIndex: 'parser',
      key: 'parser',
      width: 100,
      render: (parser) => (
        <Tag size="small" color={
          parser === 'direct' ? 'blue' :
          parser === 'regex' ? 'orange' :
          parser === 'csv' ? 'green' :
          parser === 'json' ? 'purple' : 'default'
        }>
          {parser}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record, index) => (
        <Space>
          <Button 
            size="small" 
            onClick={() => handleEditField(activeTab, index)}
          >
            编辑
          </Button>
          <Button 
            size="small" 
            danger 
            onClick={() => handleDeleteField(activeTab, index)}
            icon={<DeleteOutlined />}
          />
        </Space>
      ),
    },
  ];

  const handleFieldToggle = (category, index, enabled) => {
    const newConfig = { ...fieldConfig };
    newConfig[category][index].enabled = enabled;
    setFieldConfig(newConfig);
  };

  const handleAddField = () => {
    form.resetFields();
    setEditingField(null);
    setCustomFieldVisible(true);
  };

  const handleEditField = (category, index) => {
    const field = fieldConfig[category][index];
    form.setFieldsValue(field);
    setEditingField({ category, index });
    setCustomFieldVisible(true);
  };

  const handleDeleteField = (category, index) => {
    const newConfig = { ...fieldConfig };
    newConfig[category].splice(index, 1);
    setFieldConfig(newConfig);
  };

  const handleSaveField = async () => {
    try {
      const values = await form.validateFields();
      const newConfig = { ...fieldConfig };
      
      if (editingField) {
        // 编辑现有字段
        newConfig[editingField.category][editingField.index] = { ...values };
      } else {
        // 添加新字段
        if (!newConfig[activeTab]) {
          newConfig[activeTab] = [];
        }
        newConfig[activeTab].push({ ...values, enabled: true });
      }
      
      setFieldConfig(newConfig);
      setCustomFieldVisible(false);
      setEditingField(null);
    } catch (error) {
      console.error('Field validation failed:', error);
    }
  };

  const handleSaveConfig = () => {
    // 保存到本地存储
    localStorage.setItem('gpu_dashboard_field_config', JSON.stringify(fieldConfig));
    onSave(fieldConfig);
  };

  const handleResetConfig = () => {
    setFieldConfig(DEFAULT_FIELD_CONFIG);
    localStorage.removeItem('gpu_dashboard_field_config');
  };

  const tabItems = [
    { key: 'hostBasic', label: '主机基础信息', icon: '🖥️' },
    { key: 'gpuBasic', label: 'GPU基础信息', icon: '🔥' },
    { key: 'gpuDriver', label: 'GPU驱动信息', icon: '🛠️' },
    { key: 'gpuPerformance', label: 'GPU性能信息', icon: '⚡' },
    { key: 'systemCompatibility', label: '系统兼容性', icon: '🔗' },
    { key: 'monitorDiagnostic', label: '监控诊断', icon: '📊' },
  ];

  return (
    <>
      <Modal
        title={
          <Space>
            <SettingOutlined />
            <span>字段配置</span>
          </Space>
        }
        visible={visible}
        onCancel={onCancel}
        width={1200}
        footer={
          <Space>
            <Button onClick={handleResetConfig} icon={<ReloadOutlined />}>
              重置为默认
            </Button>
            <Button onClick={onCancel}>
              取消
            </Button>
            <Button type="primary" onClick={handleSaveConfig} icon={<SaveOutlined />}>
              保存配置
            </Button>
          </Space>
        }
      >
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          tabPosition="left"
          style={{ minHeight: '500px' }}
        >
          {tabItems.map(item => (
            <TabPane 
              tab={
                <Space>
                  <span>{item.icon}</span>
                  <span>{item.label}</span>
                  <Tag size="small" color="blue">
                    {fieldConfig[item.key]?.filter(f => f.enabled).length || 0}
                  </Tag>
                </Space>
              } 
              key={item.key}
            >
              <Card
                title={`${item.label}字段配置`}
                extra={
                  <Button type="primary" icon={<PlusOutlined />} onClick={handleAddField}>
                    添加字段
                  </Button>
                }
              >
                <Table
                  columns={fieldColumns}
                  dataSource={fieldConfig[item.key] || []}
                  pagination={false}
                  size="small"
                  rowKey="key"
                />
              </Card>
            </TabPane>
          ))}
        </Tabs>

        <Divider />
        
        <div style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.6)' }}>
          <h4>说明:</h4>
          <ul>
            <li><strong>direct:</strong> 直接显示命令输出结果</li>
            <li><strong>regex:</strong> 使用正则表达式提取特定内容</li>
            <li><strong>csv:</strong> 按CSV格式解析（逗号分隔）</li>
            <li><strong>json:</strong> 按JSON格式解析</li>
            <li><strong>custom:</strong> 使用自定义解析逻辑</li>
          </ul>
        </div>
      </Modal>

      {/* 自定义字段编辑模态框 */}
      <Modal
        title={editingField ? '编辑字段' : '添加自定义字段'}
        visible={customFieldVisible}
        onCancel={() => setCustomFieldVisible(false)}
        onOk={handleSaveField}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="key"
            label="字段标识"
            rules={[{ required: true, message: '请输入字段标识' }]}
          >
            <Input placeholder="例如: custom_gpu_temp" />
          </Form.Item>
          
          <Form.Item
            name="name"
            label="字段名称"
            rules={[{ required: true, message: '请输入字段名称' }]}
          >
            <Input placeholder="例如: GPU温度检测" />
          </Form.Item>
          
          <Form.Item
            name="command"
            label="检测命令"
            rules={[{ required: true, message: '请输入检测命令' }]}
            extra="支持Linux shell命令，如: nvidia-smi --query-gpu=temperature.gpu --format=csv"
          >
            <TextArea 
              rows={3} 
              placeholder="输入用于获取数据的shell命令..."
            />
          </Form.Item>
          
          <Form.Item
            name="parser"
            label="解析器类型"
            rules={[{ required: true, message: '请选择解析器类型' }]}
          >
            <Select placeholder="选择解析器类型">
              <Select.Option value="direct">直接输出</Select.Option>
              <Select.Option value="regex">正则表达式</Select.Option>
              <Select.Option value="csv">CSV格式</Select.Option>
              <Select.Option value="json">JSON格式</Select.Option>
              <Select.Option value="custom">自定义解析</Select.Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => 
              prevValues.parser !== currentValues.parser
            }
          >
            {({ getFieldValue }) => {
              const parser = getFieldValue('parser');
              return parser === 'regex' ? (
                <Form.Item
                  name="regex"
                  label="正则表达式"
                  rules={[{ required: true, message: '请输入正则表达式' }]}
                  extra="使用括号()捕获需要提取的内容"
                >
                  <Input placeholder="例如: Temperature: ([0-9.]+)" />
                </Form.Item>
              ) : null;
            }}
          </Form.Item>
          
          <Form.Item
            name="description"
            label="字段描述"
          >
            <TextArea 
              rows={2} 
              placeholder="可选: 描述这个字段的用途和含义..."
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}

export default FieldConfigModal; 