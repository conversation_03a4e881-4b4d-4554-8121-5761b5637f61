import React, { useState, useRef, useEffect } from 'react';
import { Upload, Button, message, Space, Divider } from 'antd';
import {
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  PictureOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined
} from '@ant-design/icons';

/**
 * 增强的富文本编辑器组件
 * 支持图片粘贴、拖拽上传、基本格式化功能
 */
export default function EnhancedRichTextEditor({ 
  value = '', 
  onChange, 
  placeholder = '请输入内容...', 
  height = 300,
  disabled = false 
}) {
  const editorRef = useRef(null);
  const [focused, setFocused] = useState(false);

  useEffect(() => {
    if (editorRef.current && value !== editorRef.current.innerHTML) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);

  // 处理内容变化
  const handleInput = () => {
    if (editorRef.current && onChange) {
      const content = editorRef.current.innerHTML;
      onChange(content);
    }
  };

  // 处理粘贴事件
  const handlePaste = async (e) => {
    const items = e.clipboardData.items;
    
    for (let item of items) {
      if (item.type.indexOf('image') !== -1) {
        e.preventDefault();
        const file = item.getAsFile();
        await insertImageFromFile(file);
        message.success('图片粘贴成功');
        break;
      }
    }
  };

  // 处理拖拽
  const handleDrop = async (e) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    
    if (imageFiles.length > 0) {
      for (let file of imageFiles) {
        await insertImageFromFile(file);
      }
      message.success(`成功添加 ${imageFiles.length} 张图片`);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  // 从文件插入图片
  const insertImageFromFile = async (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      insertImage(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  // 插入图片
  const insertImage = (src) => {
    if (editorRef.current) {
      const img = document.createElement('img');
      img.src = src;
      img.style.maxWidth = '100%';
      img.style.height = 'auto';
      img.style.margin = '10px 0';
      img.style.borderRadius = '4px';
      img.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
      
      // 获取当前选区或光标位置
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.insertNode(img);
        range.collapse(false);
      } else {
        editorRef.current.appendChild(img);
      }
      
      handleInput();
    }
  };

  // 执行格式化命令
  const execCommand = (command, value = null) => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      editorRef.current.focus();
    }
    handleInput();
  };

  // 工具栏样式
  const toolbarStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '8px 12px',
    borderBottom: '1px solid #f0f0f0',
    background: '#fafafa',
    borderRadius: '6px 6px 0 0',
    flexWrap: 'wrap'
  };

  // 编辑器样式
  const editorStyle = {
    padding: '12px',
    minHeight: height,
    outline: 'none',
    lineHeight: '1.6',
    fontSize: '14px',
    overflowY: 'auto',
    border: 'none',
    background: disabled ? '#f5f5f5' : '#fff',
    position: 'relative'
  };

  // 检查是否为空内容
  const isEmpty = !value || value.trim() === '' || value === '<br>' || value === '<div><br></div>';

  // 占位符样式
  const placeholderStyle = {
    position: 'absolute',
    top: '12px',
    left: '12px',
    color: '#bfbfbf',
    pointerEvents: 'none',
    fontSize: '14px',
    display: isEmpty && !focused ? 'block' : 'none'
  };

  // 容器样式
  const containerStyle = {
    border: `1px solid ${focused ? '#40a9ff' : '#d9d9d9'}`,
    borderRadius: '6px',
    background: '#fff',
    transition: 'all 0.3s',
    boxShadow: focused ? '0 0 0 2px rgba(24, 144, 255, 0.2)' : 'none',
    opacity: disabled ? 0.6 : 1,
    cursor: disabled ? 'not-allowed' : 'auto'
  };

  return (
    <div style={containerStyle}>
      {/* 工具栏 */}
      <div style={toolbarStyle}>
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<BoldOutlined />}
            onClick={() => execCommand('bold')}
            disabled={disabled}
            title="粗体"
          />
          <Button
            type="text"
            size="small"
            icon={<ItalicOutlined />}
            onClick={() => execCommand('italic')}
            disabled={disabled}
            title="斜体"
          />
          <Button
            type="text"
            size="small"
            icon={<UnderlineOutlined />}
            onClick={() => execCommand('underline')}
            disabled={disabled}
            title="下划线"
          />
        </Space>
        
        <Divider type="vertical" />
        
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<OrderedListOutlined />}
            onClick={() => execCommand('insertOrderedList')}
            disabled={disabled}
            title="有序列表"
          />
          <Button
            type="text"
            size="small"
            icon={<UnorderedListOutlined />}
            onClick={() => execCommand('insertUnorderedList')}
            disabled={disabled}
            title="无序列表"
          />
        </Space>
        
        <Divider type="vertical" />
        
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<AlignLeftOutlined />}
            onClick={() => execCommand('justifyLeft')}
            disabled={disabled}
            title="左对齐"
          />
          <Button
            type="text"
            size="small"
            icon={<AlignCenterOutlined />}
            onClick={() => execCommand('justifyCenter')}
            disabled={disabled}
            title="居中对齐"
          />
          <Button
            type="text"
            size="small"
            icon={<AlignRightOutlined />}
            onClick={() => execCommand('justifyRight')}
            disabled={disabled}
            title="右对齐"
          />
        </Space>
        
        <Divider type="vertical" />
        
        <Upload
          accept="image/*"
          showUploadList={false}
          beforeUpload={(file) => {
            insertImageFromFile(file);
            return false;
          }}
          disabled={disabled}
        >
          <Button
            type="text"
            size="small"
            icon={<PictureOutlined />}
            disabled={disabled}
            title="插入图片"
          />
        </Upload>
      </div>
      
      {/* 编辑区域 */}
      <div style={{ position: 'relative' }}>
        <div style={placeholderStyle}>{placeholder}</div>
        <div
          ref={editorRef}
          contentEditable={!disabled}
          style={editorStyle}
          onInput={handleInput}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          onPaste={handlePaste}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          suppressContentEditableWarning={true}
        />
      </div>
      
      {/* 提示信息 */}
      <div style={{ 
        fontSize: '12px', 
        color: '#999', 
        padding: '8px 12px',
        borderTop: '1px solid #f0f0f0',
        background: '#fafafa',
        borderRadius: '0 0 6px 6px'
      }}>
        💡 支持直接粘贴图片 (Ctrl+V) 或拖拽图片到编辑区域
      </div>
    </div>
  );
}
