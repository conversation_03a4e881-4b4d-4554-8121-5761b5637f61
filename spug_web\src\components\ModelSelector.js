import React, { useState, useEffect, useCallback, useRef } from 'react';
import { AutoComplete, Input } from 'antd';
import { DatabaseOutlined } from '@ant-design/icons';
import http from '../libs/http';

/**
 * 模型选择器组件
 * 支持模糊搜索模型名称，可以根据GPU过滤
 */
export default function ModelSelector({
  value,
  onChange,
  placeholder = "请输入模型名称",
  style,
  disabled = false,
  allowClear = true,
  gpuName = null  // 可选的GPU名称，用于过滤模型
}) {
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const timeoutRef = useRef(null);

  // 使用ref来跟踪上一次的GPU名称，避免无限循环
  const prevGpuNameRef = useRef(gpuName);

  // 当GPU变化时，清空当前选项，但不清空值
  useEffect(() => {
    if (prevGpuNameRef.current !== gpuName) {
      setOptions([]);
      prevGpuNameRef.current = gpuName;

      // 只有在新增模式下（从有GPU变为另一个GPU）才清空值
      // 如果是从null/undefined变为有值，说明是编辑模式的初始化，不清空
      if (prevGpuNameRef.current && prevGpuNameRef.current !== null && prevGpuNameRef.current !== undefined) {
        onChange && onChange('');
      }
    }
  }, [gpuName, onChange]);

  // 简单的防抖函数
  const debounce = (func, delay) => {
    return (...args) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => func(...args), delay);
    };
  };

  // 搜索函数
  const searchModels = async (searchText) => {
    if (!searchText || searchText.trim().length === 0) {
      setOptions([]);
      return;
    }

    setLoading(true);
    try {
      // 如果指定了GPU，只从该GPU获取模型列表
      if (gpuName && gpuName.trim()) {
        const response = await http.get('/api/model-storage/gpus/', {
          params: {
            name: gpuName.trim(),
            token: '1'  // 添加开发者后门token
          }
        });

        const gpus = response.results || response || [];

        if (gpus.length > 0) {
          const gpu = gpus[0];
          const allModels = gpu.tested_models || [];

          if (allModels.length === 0) {
            setOptions([{
              value: '',
              label: (
                <div style={{ color: '#999', fontStyle: 'italic' }}>
                  该GPU暂无关联模型
                </div>
              ),
              key: 'no-models',
              disabled: true
            }]);
            return;
          }

          // 只显示属于该GPU且匹配搜索文本的模型
          const filteredModels = allModels.filter(model =>
            model.toLowerCase().includes(searchText.toLowerCase())
          );

          if (filteredModels.length === 0) {
            setOptions([{
              value: '',
              label: (
                <div style={{ color: '#999', fontStyle: 'italic' }}>
                  该GPU没有匹配"{searchText}"的模型
                </div>
              ),
              key: 'no-match',
              disabled: true
            }]);
            return;
          }

          // 限制最多显示3个候选项
          const limitedModels = filteredModels.slice(0, 3);

          const modelOptions = limitedModels.map(model => ({
            value: model,
            label: (
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <DatabaseOutlined style={{ marginRight: 8, color: '#52c41a' }} />
                  <span style={{ fontWeight: 500 }}>{model}</span>
                </div>
                <span style={{ color: '#999', fontSize: '12px' }}>
                  {gpuName}
                </span>
              </div>
            ),
            key: model
          }));

          setOptions(modelOptions);
        } else {
          setOptions([{
            value: '',
            label: (
              <div style={{ color: '#999', fontStyle: 'italic' }}>
                未找到GPU: {gpuName}
              </div>
            ),
            key: 'gpu-not-found',
            disabled: true
          }]);
        }
      } else {
        // 如果没有指定GPU，提供一些常见的模型选项或从测试任务中搜索

        // 先提供一些常见的模型作为备选
        const commonModels = [
          'Qwen2.5_1B1024_ol31744',
          'DeepSeek-V2',
          'DeepSeek-R1',
          'ChatGLM3-6B',
          'Baichuan2-7B',
          'LLaMA2-7B',
          'GPT-3.5',
          'Claude-3'
        ];

        const filteredCommonModels = commonModels.filter(model =>
          model.toLowerCase().includes(searchText.toLowerCase())
        );

        let uniqueModels = [...filteredCommonModels];

        try {
          // 尝试从测试任务获取更多模型
          const taskResponse = await http.get('/api/model-storage/test-tasks/', {
            params: {
              model_name: searchText.trim(),
              page_size: 20,
              token: '1'  // 添加开发者后门token
            }
          });

          const tasks = taskResponse.results || taskResponse || [];
          const taskModels = [...new Set(tasks.map(task => task.model_name).filter(name => name))];
          uniqueModels = [...uniqueModels, ...taskModels];
        } catch (error) {
          // 忽略错误，使用常见模型
        }

        try {
          // 尝试从所有GPU获取模型
          const gpuResponse = await http.get('/api/model-storage/gpus/', {
            params: {
              token: '1'  // 添加开发者后门token
            }
          });
          const allGpus = gpuResponse.results || gpuResponse || [];

          const allGpuModels = [];
          allGpus.forEach(gpu => {
            if (gpu.tested_models && Array.isArray(gpu.tested_models)) {
              allGpuModels.push(...gpu.tested_models);
            }
          });

          const filteredGpuModels = allGpuModels.filter(model =>
            model.toLowerCase().includes(searchText.toLowerCase())
          );

          uniqueModels = [...uniqueModels, ...filteredGpuModels];
        } catch (error) {
          // 忽略错误，使用已有模型
        }

        // 去重并过滤
        uniqueModels = [...new Set(uniqueModels)].filter(model =>
          model && model.toLowerCase().includes(searchText.toLowerCase())
        );

        // 限制最多显示3个候选项
        const limitedModels = uniqueModels.slice(0, 3);

        const modelOptions = limitedModels.map(model => ({
          value: model,
          label: (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <DatabaseOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              <span>{model}</span>
            </div>
          ),
          key: model
        }));

        setOptions(modelOptions);
      }
    } catch (error) {
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索函数
  const debouncedSearch = useCallback(debounce(searchModels, 300), [gpuName]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // 当GPU变化时，清空当前选择
  useEffect(() => {
    if (value && gpuName) {
      // 验证当前选择的模型是否在新GPU的模型列表中
      searchModels(value).then(() => {
        // 如果搜索结果为空，清空当前值
        if (options.length === 0) {
          onChange && onChange('');
        }
      });
    }
  }, [gpuName]);

  // 处理搜索
  const handleSearch = (searchText) => {
    debouncedSearch(searchText);
  };

  // 处理焦点事件
  const handleFocus = () => {
    // 如果有值且有GPU，显示推荐
    if (value && gpuName && options.length === 0) {
      searchModels(value);
    }
  };

  // 处理点击事件
  const handleClick = () => {
    // 如果有值且有GPU，显示推荐
    if (value && gpuName && options.length === 0) {
      searchModels(value);
    }
  };

  // 处理选择
  const handleSelect = (selectedValue, option) => {
    onChange && onChange(selectedValue);
    // 选择后清空选项列表
    setOptions([]);
  };

  // 处理输入变化
  const handleChange = (inputValue) => {
    onChange && onChange(inputValue);
    // 如果输入为空，清空选项
    if (!inputValue) {
      setOptions([]);
    }
  };

  return (
    <AutoComplete
      key={`model-selector-${gpuName || 'no-gpu'}`} // 强制重新渲染
      value={value}
      options={options}
      onSearch={handleSearch}
      onSelect={handleSelect}
      onChange={handleChange}
      onFocus={handleFocus}
      placeholder={placeholder}
      style={style}
      disabled={disabled}
      allowClear={allowClear}
      notFoundContent={loading ? '搜索中...' : '暂无匹配模型'}
      filterOption={false} // 禁用本地过滤，使用服务端搜索
    >
      <Input
        prefix={<DatabaseOutlined style={{ color: '#bfbfbf' }} />}
        placeholder={placeholder}
        onClick={handleClick}
      />
    </AutoComplete>
  );
}
