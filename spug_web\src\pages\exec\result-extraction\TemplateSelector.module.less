.templateSelector {
  .modalTitle {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
  }
  
  .selectorContent {
    .searchSection {
      margin-bottom: 16px;
    }
    
    .templateCard {
      height: 200px;
      border: 2px solid transparent;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #1890ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
      }
      
      &.selected {
        border-color: #722ed1;
        box-shadow: 0 4px 12px rgba(114, 46, 209, 0.2);
        background: rgba(114, 46, 209, 0.02);
      }
      
      .templateHeader {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        
        .templateIcon {
          font-size: 24px;
          margin-right: 12px;
          margin-top: 2px;
        }
        
        .templateInfo {
          flex: 1;
          
          .templateName {
            font-size: 14px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
          }
        }
      }
      
      .templateDescription {
        color: #666;
        font-size: 12px;
        line-height: 1.4;
        margin-bottom: 12px;
      }
      
      .templateMetrics {
        .metricsTitle {
          font-size: 12px;
          color: #666;
          margin-bottom: 6px;
        }
        
        .metricsTags {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
        }
      }
    }
    
    .templatePreview {
      margin-top: 16px;
      border-top: 1px solid #e8e8e8;
      padding-top: 16px;
      
      .previewCard {
        border: 1px solid #722ed1;
        background: rgba(114, 46, 209, 0.02);
        
        .previewContent {
          .previewHeader {
            margin-bottom: 8px;
            
            .previewName {
              font-weight: 600;
              color: #262626;
            }
          }
          
          .previewDescription {
            color: #666;
            margin-bottom: 12px;
          }
          
          .previewMetrics {
            .metricsTitle {
              font-size: 12px;
              color: #666;
              margin-bottom: 8px;
            }
          }
        }
      }
    }
  }
} 