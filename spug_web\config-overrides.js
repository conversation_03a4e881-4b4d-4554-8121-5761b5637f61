/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
const {override, addDecoratorsLegacy, addLessLoader} = require('customize-cra');
const webpack = require('webpack');

module.exports = override(
  addDecoratorsLegacy(),
  addLessLoader({
    lessOptions: {
      javascriptEnabled: true,
      modifyVars: {
        '@primary-color': '#2563fc'
      }
    }
  }),
  // 简单的 process 定义
  (config) => {
    config.plugins = (config.plugins || []).concat([
      new webpack.DefinePlugin({
        'process': JSON.stringify({
          env: process.env || {},
          browser: true
        }),
        'process.env': JSON.stringify(process.env || {})
      })
    ]);
    return config;
  }
);
