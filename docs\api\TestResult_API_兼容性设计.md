# TestResult API 兼容性设计文档

## 概述

TestResult模型已重新设计，支持两种工作模式：
1. **测试计划执行模式**：与TestPlanExecution绑定的传统模式
2. **独立日志提取模式**：不依赖测试计划的日志提取功能

## 模型变更

### 新增字段

- `source_type`: 数据来源类型
  - `execution`: 测试计划执行
  - `log_extraction`: 日志提取
  - `manual`: 手动录入

- `log_source_host`: 日志来源主机（可选）
- `log_source_path`: 日志文件路径（可选）
- `log_extraction_method`: 日志提取方式（可选）
  - `remote`: 远程提取
  - `upload`: 文件上传
  - `paste`: 粘贴输入

### 修改字段

- `execution`: 改为可选字段（null=True, blank=True）

### 新增属性

- `is_standalone`: 判断是否为独立的日志提取结果
- `to_dict()`: 完整的字典转换方法

## API 使用示例

### 1. 创建独立日志提取结果

```bash
POST /exec/test-results/
Content-Type: application/json
X-Token: 1

{
  "plan_name": "GPU性能测试",
  "task_name": "深度学习训练",
  "source_type": "log_extraction",
  "log_source_host": "*************",
  "log_source_path": "/var/log/gpu_test.log",
  "log_extraction_method": "remote",
  "metrics": [
    {
      "name": "GPU利用率",
      "value": "95%",
      "confidence": 0.9
    }
  ],
  "raw_log": "GPU utilization: 95%\nMemory usage: 8GB",
  "confirmed_metrics": 1,
  "ai_confidence": 0.9
}
```

### 2. 创建传统测试计划执行结果

```bash
POST /exec/test-results/
Content-Type: application/json
X-Token: 1

{
  "execution_id": 1,
  "plan_name": "CPU性能测试",
  "task_name": "压力测试",
  "metrics": [
    {
      "name": "CPU利用率",
      "value": "80%",
      "confidence": 0.85
    }
  ],
  "raw_log": "CPU utilization: 80%",
  "confirmed_metrics": 1,
  "ai_confidence": 0.85
}
```

### 3. 查询特定类型的测试结果

```bash
# 查询所有日志提取结果
GET /exec/test-results/?source_type=log_extraction

# 查询所有测试计划执行结果
GET /exec/test-results/?source_type=execution

# 查询特定计划名称
GET /exec/test-results/?plan_name=GPU性能测试
```

### 4. 响应数据格式

```json
{
  "data": {
    "id": 2,
    "execution_id": null,
    "plan_name": "GPU性能测试",
    "task_name": "深度学习训练",
    "source_type": "log_extraction",
    "log_source_host": "*************",
    "log_source_path": "/var/log/gpu_test.log",
    "log_extraction_method": "remote",
    "metrics": [
      {
        "name": "GPU利用率",
        "value": "95%",
        "confidence": 0.9
      }
    ],
    "raw_log": "GPU utilization: 95%\nMemory usage: 8GB",
    "total_metrics": 1,
    "confirmed_metrics": 1,
    "ai_confidence": 0.9,
    "success_rate": 100.0,
    "is_standalone": true,
    "created_at": "2025-07-16 17:38:50",
    "updated_at": "2025-07-16 17:38:50",
    "created_by_id": 1
  },
  "error": ""
}
```

## 兼容性保证

1. **向后兼容**：现有的测试计划执行模式完全保持不变
2. **数据库迁移**：自动应用，无需手动干预
3. **API接口**：保持原有接口不变，新增可选参数
4. **前端适配**：前端可以根据`is_standalone`字段判断显示方式

## 优势

1. **解耦设计**：日志提取功能不再依赖测试计划
2. **灵活性**：支持多种数据来源和提取方式
3. **可扩展性**：易于添加新的数据来源类型
4. **数据完整性**：保留日志来源信息，便于追溯
5. **性能优化**：避免创建虚拟执行记录，减少数据冗余

## 迁移说明

现有数据会自动设置为：
- `source_type`: "execution"
- `execution`: 保持原有关联
- 新增字段默认为空或默认值

无需手动数据迁移，系统会自动处理兼容性。