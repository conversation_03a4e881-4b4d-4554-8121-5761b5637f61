/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React, { useState, useEffect } from 'react';
import { 
  Drawer, 
  Form, 
  Input, 
  Switch, 
  Button, 
  Space, 
  Card, 
  Divider,
  message,
  Table,
  Tooltip,
  Popconfirm
} from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined, 
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons';
import { http } from 'libs';
import { ACEditor } from 'components';
import styles from './CardEditor.module.less';

function CardEditor({ card, visible, onClose, onSave }) {
  const [commands, setCommands] = useState([]);
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editingIndex, setEditingIndex] = useState(-1);
  const [editingFileIndex, setEditingFileIndex] = useState(-1);
  const [cardName, setCardName] = useState('');
  const [form] = Form.useForm();
  const [fileForm] = Form.useForm();

  useEffect(() => {
    console.log('CardEditor useEffect:', { card, visible });
    if (card && visible) {
      setCommands(card.commands || []);
      setFiles(card.files || []);
      setCardName(card.name || '');
      setEditingIndex(-1);
      setEditingFileIndex(-1);
      form.resetFields();
      fileForm.resetFields();
    }
  }, [card, visible]);

  const handleAddCommand = () => {
    const newCommand = {
      key: `custom_${Date.now()}`,
      label: '',
      command: '',
      description: '',
      enabled: true,
      isNew: true
    };
    setCommands([...commands, newCommand]);
    setEditingIndex(commands.length);
    form.setFieldsValue(newCommand);
  };

  const handleEditCommand = (index) => {
    setEditingIndex(index);
    form.setFieldsValue(commands[index]);
  };

  const handleSaveCommand = async () => {
    try {
      const values = await form.validateFields();
      
      const newCommands = [...commands];
      if (editingIndex >= 0) {
        newCommands[editingIndex] = {
          ...newCommands[editingIndex],
          ...values,
          isNew: false
        };
        setCommands(newCommands);
        setEditingIndex(-1);
        form.resetFields();
        message.success('命令保存成功');
      }
    } catch (error) {
      console.error('Save command error:', error);
    }
  };

  const handleDeleteCommand = (index) => {
    const newCommands = commands.filter((_, i) => i !== index);
    setCommands(newCommands);
    if (editingIndex === index) {
      setEditingIndex(-1);
      form.resetFields();
    }
    message.success('命令删除成功');
  };

  const handleToggleEnabled = (index) => {
    const newCommands = [...commands];
    newCommands[index].enabled = !newCommands[index].enabled;
    setCommands(newCommands);
  };

  const handleMoveCommand = (index, direction) => {
    const newCommands = [...commands];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;
    
    if (targetIndex >= 0 && targetIndex < newCommands.length) {
      [newCommands[index], newCommands[targetIndex]] = [newCommands[targetIndex], newCommands[index]];
      setCommands(newCommands);
      
      // 如果移动的是正在编辑的命令，更新编辑索引
      if (editingIndex === index) {
        setEditingIndex(targetIndex);
      } else if (editingIndex === targetIndex) {
        setEditingIndex(index);
      }
    }
  };

  // 文件管理相关函数
  const handleAddFile = () => {
    const newFile = {
      id: `file_${Date.now()}`,
      name: '',
      localPath: '',
      targetPath: '/tmp/',
      description: '',
      enabled: true,
      isNew: true
    };
    setFiles([...files, newFile]);
    setEditingFileIndex(files.length);
    fileForm.setFieldsValue(newFile);
  };

  const handleEditFile = (index) => {
    setEditingFileIndex(index);
    fileForm.setFieldsValue(files[index]);
  };

  const handleSaveFile = async () => {
    try {
      const values = await fileForm.validateFields();
      
      const newFiles = [...files];
      if (editingFileIndex >= 0) {
        newFiles[editingFileIndex] = {
          ...newFiles[editingFileIndex],
          ...values,
          isNew: false
        };
        setFiles(newFiles);
        setEditingFileIndex(-1);
        fileForm.resetFields();
        message.success('文件保存成功');
      }
    } catch (error) {
      console.error('Save file error:', error);
    }
  };

  const handleDeleteFile = (index) => {
    const newFiles = files.filter((_, i) => i !== index);
    setFiles(newFiles);
    if (editingFileIndex === index) {
      setEditingFileIndex(-1);
      fileForm.resetFields();
    }
    message.success('文件删除成功');
  };

  const handleToggleFileEnabled = (index) => {
    const newFiles = [...files];
    newFiles[index].enabled = !newFiles[index].enabled;
    setFiles(newFiles);
  };

  const handleSaveCard = async () => {
    if (!cardName.trim()) {
      message.error('请输入卡片名称');
      return;
    }

    setLoading(true);
    try {
      // 验证所有命令都已保存
      const hasUnsaved = commands.some(cmd => cmd.isNew && (!cmd.label || !cmd.command));
      if (hasUnsaved) {
        message.error('请先保存所有未完成的命令');
        return;
      }

      const updatedCard = {
        ...card,
        name: cardName,
        commands: commands.filter(cmd => !cmd.isNew || (cmd.label && cmd.command)),
        files: files.filter(file => !file.isNew || (file.name && file.localPath))
      };

      await http.put(`/api/exec/test-plans/${card.id}/`, updatedCard);
      message.success('测试计划保存成功');
      onSave();
    } catch (error) {
      message.error('保存失败');
      console.error('Save card error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (editingIndex >= 0) {
      setEditingIndex(-1);
      form.resetFields();
    }
    if (editingFileIndex >= 0) {
      setEditingFileIndex(-1);
      fileForm.resetFields();
    }
    onClose();
  };

  const columns = [
    {
      title: '顺序',
      width: 70,
      render: (_, record, index) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<ArrowUpOutlined />}
            disabled={index === 0}
            onClick={() => handleMoveCommand(index, 'up')}
          />
          <Button
            type="link"
            size="small"
            icon={<ArrowDownOutlined />}
            disabled={index === commands.length - 1}
            onClick={() => handleMoveCommand(index, 'down')}
          />
        </Space>
      )
    },
    {
      title: '启用',
      dataIndex: 'enabled',
      width: 60,
      render: (enabled, record, index) => (
        <Switch
          checked={enabled}
          size="small"
          onChange={() => handleToggleEnabled(index)}
        />
      )
    },
    {
      title: '字段名称',
      dataIndex: 'label',
      width: 120,
      render: (text, record, index) => (
        <div>
          {text || <span style={{ color: '#ccc' }}>未设置</span>}
          {record.isNew && <span style={{ color: '#ff4d4f', marginLeft: 4 }}>*</span>}
        </div>
      )
    },
    {
      title: '命令',
      dataIndex: 'command',
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <code style={{ fontSize: '12px' }}>
            {text || <span style={{ color: '#ccc' }}>未设置</span>}
          </code>
        </Tooltip>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 150,
      ellipsis: true,
      render: (text) => text || <span style={{ color: '#ccc' }}>无描述</span>
    },
    {
      title: '操作',
      width: 80,
      render: (_, record, index) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditCommand(index)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个命令吗？"
            onConfirm={() => handleDeleteCommand(index)}
            okText="删除"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 文件表格列定义
  const fileColumns = [
    {
      title: '启用',
      dataIndex: 'enabled',
      width: 60,
      render: (enabled, record, index) => (
        <Switch
          checked={enabled}
          size="small"
          onChange={() => handleToggleFileEnabled(index)}
        />
      )
    },
    {
      title: '文件名',
      dataIndex: 'name',
      width: 120,
      render: (text, record, index) => (
        <div>
          {text || <span style={{ color: '#ccc' }}>未设置</span>}
          {record.isNew && <span style={{ color: '#ff4d4f', marginLeft: 4 }}>*</span>}
        </div>
      )
    },
    {
      title: '本地路径',
      dataIndex: 'localPath',
      width: 150,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <code style={{ fontSize: '12px' }}>
            {text || <span style={{ color: '#ccc' }}>未设置</span>}
          </code>
        </Tooltip>
      )
    },
    {
      title: '目标路径',
      dataIndex: 'targetPath',
      width: 150,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <code style={{ fontSize: '12px' }}>
            {text || <span style={{ color: '#ccc' }}>未设置</span>}
          </code>
        </Tooltip>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 120,
      ellipsis: true,
      render: (text) => text || <span style={{ color: '#ccc' }}>无描述</span>
    },
    {
      title: '操作',
      width: 80,
      render: (_, record, index) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditFile(index)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个文件吗？"
            onConfirm={() => handleDeleteFile(index)}
            okText="删除"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Input
            value={cardName}
            onChange={(e) => setCardName(e.target.value)}
            placeholder="输入卡片名称"
            style={{ width: 300, marginRight: 16 }}
            onPressEnter={handleSaveCard}
          />
          <Space>
            <Button
              type="primary"
              loading={loading}
              icon={<SaveOutlined />}
              onClick={handleSaveCard}
            >
              保存卡片
            </Button>
            <Button
              icon={<CloseOutlined />}
              onClick={handleClose}
            >
              关闭
            </Button>
          </Space>
        </div>
      }
      placement="right"
      size="large"
      visible={visible}
      onClose={handleClose}
      maskClosable={false}
      destroyOnClose={true}
      bodyStyle={{ padding: '16px' }}
    >
      <div className={styles.cardEditor}>
        <div className={styles.editorHeader}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddCommand}
            >
              添加命令
            </Button>
            <span style={{ color: '#666' }}>
              共 {commands.length} 个命令，启用 {commands.filter(cmd => cmd.enabled).length} 个
            </span>
          </Space>
        </div>

        <div className={styles.editorContent}>
          <div className={styles.commandList}>
            <Table
              columns={columns}
              dataSource={commands}
              rowKey={(record, index) => `${record.key || index}`}
              pagination={false}
              size="small"
              scroll={{ y: 300 }}
              rowClassName={(record, index) => 
                editingIndex === index ? styles.editingRow : ''
              }
            />
          </div>

          {editingIndex >= 0 && (
            <Card 
              title="编辑命令" 
              size="small" 
              className={styles.editingCard}
              extra={
                <Space>
                  <Button size="small" onClick={() => {
                    setEditingIndex(-1);
                    form.resetFields();
                  }}>
                    取消
                  </Button>
                  <Button 
                    type="primary" 
                    size="small" 
                    icon={<SaveOutlined />}
                    onClick={handleSaveCommand}
                  >
                    保存
                  </Button>
                </Space>
              }
            >
              <Form
                form={form}
                layout="vertical"
                size="small"
              >
                <Form.Item
                  name="key"
                  label="字段Key"
                  rules={[
                    { required: true, message: '请输入字段Key' },
                    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '只能包含字母、数字和下划线，且以字母或下划线开头' }
                  ]}
                >
                  <Input placeholder="例如: gpu_temperature_check" />
                </Form.Item>

                <Form.Item
                  name="label"
                  label="显示名称"
                  rules={[{ required: true, message: '请输入显示名称' }]}
                >
                  <Input placeholder="例如: GPU温度检查" />
                </Form.Item>

                <Form.Item
                  name="command"
                  label="命令内容"
                  rules={[{ required: true, message: '请输入命令内容' }]}
                >
                  <ACEditor
                    mode="sh"
                    theme="tomorrow"
                    width="100%"
                    height="80px"
                    placeholder="例如: nvidia-smi -q -d TEMPERATURE"
                    setOptions={{
                      showLineNumbers: true,
                      showGutter: true,
                      showPrintMargin: false,
                      highlightActiveLine: true,
                      fontSize: 13,
                      fontFamily: 'Monaco, Courier New, monospace',
                      wrap: true,
                      useWorker: false, // 避免语法错误检查干扰
                      enableBasicAutocompletion: true,
                      enableLiveAutocompletion: true,
                      enableSnippets: true,
                      tabSize: 2,
                      scrollPastEnd: 0.2
                    }}
                    style={{ 
                      border: '1px solid #d9d9d9', 
                      borderRadius: '4px'
                    }}
                  />
                </Form.Item>

                <Form.Item
                  name="description"
                  label="命令描述"
                >
                  <Input.TextArea
                    rows={2}
                    placeholder="描述这个命令的作用和预期结果..."
                  />
                </Form.Item>

                <Form.Item
                  name="enabled"
                  label="启用状态"
                  valuePropName="checked"
                >
                  <Switch checkedChildren="启用" unCheckedChildren="禁用" />
                </Form.Item>
              </Form>
            </Card>
          )}
        </div>

        <Divider />

        {/* 文件管理区域 */}
        <div className={styles.editorHeader}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddFile}
              style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
            >
              📁 添加文件
            </Button>
            <span style={{ color: '#666' }}>
              共 {files.length} 个文件，启用 {files.filter(file => file.enabled).length} 个
            </span>
          </Space>
        </div>

        <div className={styles.editorContent}>
          <div className={styles.fileList}>
            <Table
              columns={fileColumns}
              dataSource={files}
              rowKey={(record, index) => `${record.id || index}`}
              pagination={false}
              size="small"
              scroll={{ y: 200 }}
              rowClassName={(record, index) => 
                editingFileIndex === index ? styles.editingRow : ''
              }
              locale={{ emptyText: '暂无关联文件' }}
            />
          </div>

          {editingFileIndex >= 0 && (
            <Card 
              title="编辑文件" 
              size="small" 
              className={styles.editingCard}
              extra={
                <Space>
                  <Button size="small" onClick={() => {
                    setEditingFileIndex(-1);
                    fileForm.resetFields();
                  }}>
                    取消
                  </Button>
                  <Button 
                    type="primary" 
                    size="small" 
                    icon={<SaveOutlined />}
                    onClick={handleSaveFile}
                  >
                    保存
                  </Button>
                </Space>
              }
            >
              <Form
                form={fileForm}
                layout="vertical"
                size="small"
              >
                <Form.Item
                  name="name"
                  label="文件名"
                  rules={[{ required: true, message: '请输入文件名' }]}
                >
                  <Input placeholder="例如: nvidia-driver-460.run" />
                </Form.Item>

                <Form.Item
                  name="localPath"
                  label="本地路径 (文件管理器中的路径)"
                  rules={[{ required: true, message: '请输入本地文件路径' }]}
                >
                  <Input placeholder="例如: 固件/nvidia-driver-460.run" />
                </Form.Item>

                <Form.Item
                  name="targetPath"
                  label="目标路径 (主机上的存放路径)"
                  rules={[{ required: true, message: '请输入目标路径' }]}
                >
                  <Input placeholder="例如: /tmp/nvidia-driver-460.run" />
                </Form.Item>

                <Form.Item
                  name="description"
                  label="文件描述"
                >
                  <Input.TextArea
                    rows={2}
                    placeholder="描述这个文件的用途和说明..."
                  />
                </Form.Item>

                <Form.Item
                  name="enabled"
                  label="启用状态"
                  valuePropName="checked"
                >
                  <Switch checkedChildren="启用" unCheckedChildren="禁用" />
                </Form.Item>
              </Form>
            </Card>
          )}
        </div>

        <Divider />
        
        <div className={styles.helpInfo}>
          <h4>使用说明：</h4>
          <ul>
            <li><strong>测试计划组成：</strong>每个测试计划包含文件和命令两部分</li>
            <li><strong>文件管理：</strong>本地路径指向文件管理器中的文件，目标路径为主机存放位置</li>
            <li><strong>执行顺序：</strong>先分发文件到目标主机，再按顺序执行命令</li>
            <li><strong>字段Key：</strong>用于程序识别，建议使用英文和下划线</li>
            <li><strong>命令内容：</strong>支持多行，可以使用管道、重定向等Linux特性</li>
            <li><strong>启用控制：</strong>可以启用/禁用单个文件或命令</li>
            <li><strong>路径示例：</strong>本地路径 <code>固件/driver.run</code> → 目标路径 <code>/tmp/driver.run</code></li>
          </ul>
        </div>
      </div>
    </Drawer>
  );
}

export default CardEditor; 