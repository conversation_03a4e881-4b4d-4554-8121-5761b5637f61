// 现代化统计页面样式
.container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.header {
  margin-bottom: 32px;
  text-align: center;
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 16px;
  color: #718096;
  font-weight: 400;
}

// 网格布局
.statsGrid {
  margin-top: 24px;
}

// 统一卡片样式
.overviewCard,
.personnelCard,
.gpuCard,
.riskCard,
.testCaseCard {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e2e8f0;
  overflow: visible;
  min-height: 320px;
  height: auto;
  display: flex;
  flex-direction: column;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }
}

// 卡片头部样式
.cardHeader {
  display: flex;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f1f5f9;
}

.iconWrapper {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.overviewCard .iconWrapper {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.personnelCard .iconWrapper {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gpuCard .iconWrapper {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.riskCard .iconWrapper {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.testCaseCard .iconWrapper {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.cardIcon {
  font-size: 24px;
  color: white;
}

.cardTitle {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
}

// 主要统计数字
.mainStat {
  padding: 0 24px 16px;
  text-align: center;
}

.mainStatValue {
  font-size: 36px;
  font-weight: 700;
  color: #2d3748;
  line-height: 1;
  margin-bottom: 6px;
}

.mainStatLabel {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
}

// 分隔线
.divider {
  margin: 0 24px 16px;
  border-color: #e2e8f0;
}

// 概览卡片特殊样式
.statsRow {
  display: flex;
  justify-content: space-around;
  padding: 0 24px 16px;
  margin-bottom: 12px;
}

.statItem {
  text-align: center;
  flex: 1;
}

.statValue {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 4px;
}

.statLabel {
  font-size: 11px;
  color: #718096;
  font-weight: 500;
}

.progressSection {
  padding: 0 24px 20px;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.progressText {
  font-size: 12px;
  font-weight: 600;
  color: #2d3748;
}

.progressLabel {
  margin-top: 8px;
  font-size: 11px;
  color: #718096;
  font-weight: 500;
}

.sectionTitle {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 16px;
}

.sectionIcon {
  margin-right: 8px;
  font-size: 16px;
}

.progressItem {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.progressLabel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: #4a5568;
}

.progressValue {
  font-weight: 600;
  color: #2d3748;
}

// GPU统计样式
.gpuStats {
  display: flex;
  justify-content: space-around;
  padding: 0 24px 16px;
}

.gpuStatItem {
  text-align: center;
  flex: 1;
}

.gpuStatValue {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 4px;
}

.gpuStatLabel {
  font-size: 11px;
  color: #718096;
  font-weight: 500;
}

// 排行榜样式
.rankingSection,
.modelsSection,
.riskSection {
  padding: 0 24px 20px;
  flex: 1;
  overflow: visible;
}

.rankingList,
.modelsList,
.riskList {
  space-y: 12px;
}

.rankingItem,
.modelItem,
.riskItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  margin-bottom: 6px;
  transition: all 0.2s ease;
  
  &:hover {
    background: #edf2f7;
    transform: translateX(2px);
  }
}

.rankingLeft,
.modelLeft,
.riskLeft {
  display: flex;
  align-items: center;
}

.rankingBadge {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 700;
  color: white;
  margin-right: 8px;
}

.rank1 {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.rank2 {
  background: linear-gradient(135deg, #c0c0c0, #e2e8f0);
}

.rank3 {
  background: linear-gradient(135deg, #cd7f32, #d69e2e);
}

.testerName,
.modelName,
.riskLabel,
.projectName {
  font-size: 12px;
  color: #2d3748;
  font-weight: 500;
}

.completedCount,
.modelCount,
.riskCount {
  font-size: 12px;
  font-weight: 600;
  color: #4a5568;
  background: #e2e8f0;
  padding: 2px 8px;
  border-radius: 10px;
}

.modelIcon {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-right: 8px;
}

// 风险指示器
.riskIndicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;
}

.highPriority {
  background: #e53e3e;
}

.delayed {
  background: #dd6b20;
}

.priorityhigh {
  background: #e53e3e;
}

.prioritymedium {
  background: #d69e2e;
}

.prioritylow {
  background: #38a169;
}

.priorityTag {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
}

.priorityTag.priorityhigh {
  background: #e53e3e;
}

.priorityTag.prioritymedium {
  background: #d69e2e;
}

.priorityTag.prioritylow {
  background: #38a169;
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .statsGrid {
    margin-top: 16px;
  }
  
  .overviewCard,
  .personnelCard,
  .gpuCard,
  .riskCard,
  .testCaseCard {
    margin-bottom: 12px;
    border-radius: 12px;
    
    :global(.ant-card-body) {
      padding: 16px;
    }
    
    :global(.ant-statistic-content) {
      font-size: 20px;
    }
  }
}

// 加载动画
.overviewCard,
.personnelCard,
.gpuCard,
.riskCard,
.testCaseCard {
  :global(.ant-spin-container) {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 数据为空时的样式
.emptyState {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.5);
  
  :global(.anticon) {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.3;
  }
  
  p {
    font-size: 16px;
    margin: 0;
  }
}

// 悬浮效果增强
.overviewCard:hover,
.personnelCard:hover,
.gpuCard:hover,
.riskCard:hover,
.testCaseCard:hover {
  :global(.ant-statistic-content) {
    transform: scale(1.05);
    transition: transform 0.2s ease;
  }
  
  :global(.ant-progress-circle) {
    transform: scale(1.02);
    transition: transform 0.2s ease;
  }
}
