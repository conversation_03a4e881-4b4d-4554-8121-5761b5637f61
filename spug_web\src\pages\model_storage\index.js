import ServerMetrics from './ServerMetrics';
import FileTree from './FileTree';
import ReleasePlanCard from './ReleasePlanCard';
import GpuManagementCard from './GpuManagementCard';
import styles from './index.module.less';

export default observer(function ModelStorage() {
  return (
    <div className={styles.modelStorage}>
      <div className={styles.header}>
        <h2>模型存储</h2>
        <p>
          模型存储管理，包括模型文件的存储、加载和版本控制。
          支持多种模型格式，如 ONNX、TensorFlow、PyTorch 等。
        </p>
      </div>
      <div className={styles.content}>
        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col xs={24} sm={24} md={10} lg={8}>
            <FileTree />
          </Col>
          <Col xs={24} sm={24} md={14} lg={16}>
            <Row gutter={[24, 24]}>
              <Col span={24}>
                <ReleasePlanCard />
              </Col>
              <Col span={24}>
                <GpuManagementCard />
              </Col>
            </Row>
          </Col>
        </Row>
      </div>
    </div>
  );
}); 