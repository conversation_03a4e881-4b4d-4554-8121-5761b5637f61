# Generated by Django 2.2.28 on 2025-07-22 13:33

from django.db import migrations, models
import libs.mixins
import libs.utils


class Migration(migrations.Migration):

    dependencies = [
        ('exec', '0006_auto_20250721_1239'),
    ]

    operations = [
        migrations.CreateModel(
            name='TestCaseTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='用例模板名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='用例模板描述')),
                ('category', models.CharField(blank=True, max_length=50, null=True, verbose_name='用例模板分类')),
                ('precondition', models.TextField(blank=True, null=True, verbose_name='预置条件')),
                ('test_steps', models.TextField(blank=True, null=True, verbose_name='测试步骤')),
                ('expected_result', models.TextField(blank=True, null=True, verbose_name='预期结果')),
                ('enabled', models.BooleanField(default=True, verbose_name='是否启用')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('created_at', models.CharField(default=libs.utils.human_datetime, max_length=20, verbose_name='创建时间')),
                ('created_by_id', models.IntegerField(default=1, verbose_name='创建人ID')),
                ('updated_at', models.CharField(max_length=20, null=True, verbose_name='更新时间')),
                ('updated_by_id', models.IntegerField(null=True, verbose_name='更新人ID')),
            ],
            options={
                'verbose_name': '测试用例模板',
                'verbose_name_plural': '测试用例模板',
                'db_table': 'exec_test_case_templates',
                'ordering': ('sort_order', '-id'),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
