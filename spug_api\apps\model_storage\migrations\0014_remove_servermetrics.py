# Generated by Django migration for removing ServerMetrics model
# This migration removes the ServerMetrics model and its database table
# as part of the optimization to avoid storing server metrics data

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0013_documentinstance_templatevariable_wordtemplate'),
    ]

    operations = [
        # Delete the ServerMetrics model and its table
        migrations.DeleteModel(
            name='ServerMetrics',
        ),
    ]
