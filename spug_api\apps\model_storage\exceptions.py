# -*- coding: utf-8 -*-
"""
Model Storage 模块专用异常类和错误处理
"""

import logging
import traceback
from typing import Optional, Dict, Any
from django.http import JsonResponse
from functools import wraps

# 配置专用logger
logger = logging.getLogger('model_storage')

class ModelStorageException(Exception):
    """Model Storage 基础异常类"""
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or 'UNKNOWN_ERROR'
        self.details = details or {}
        super().__init__(self.message)

class NetworkException(ModelStorageException):
    """网络相关异常"""
    def __init__(self, message: str, url: str = None, status_code: int = None):
        super().__init__(message, 'NETWORK_ERROR', {
            'url': url,
            'status_code': status_code
        })

class DatabaseException(ModelStorageException):
    """数据库相关异常"""
    def __init__(self, message: str, operation: str = None):
        super().__init__(message, 'DATABASE_ERROR', {
            'operation': operation
        })

class FileSystemException(ModelStorageException):
    """文件系统相关异常"""
    def __init__(self, message: str, path: str = None, operation: str = None):
        super().__init__(message, 'FILESYSTEM_ERROR', {
            'path': path,
            'operation': operation
        })

class AuthenticationException(ModelStorageException):
    """认证相关异常"""
    def __init__(self, message: str, auth_type: str = None):
        super().__init__(message, 'AUTH_ERROR', {
            'auth_type': auth_type
        })

class ValidationException(ModelStorageException):
    """数据验证异常"""
    def __init__(self, message: str, field: str = None, value: Any = None):
        super().__init__(message, 'VALIDATION_ERROR', {
            'field': field,
            'value': str(value) if value is not None else None
        })

class TimeoutException(ModelStorageException):
    """超时异常"""
    def __init__(self, message: str, timeout_seconds: int = None, operation: str = None):
        super().__init__(message, 'TIMEOUT_ERROR', {
            'timeout_seconds': timeout_seconds,
            'operation': operation
        })

def log_exception(exc: Exception, context: Dict[str, Any] = None) -> None:
    """统一的异常日志记录"""
    context = context or {}
    
    if isinstance(exc, ModelStorageException):
        logger.error(
            f"[{exc.error_code}] {exc.message}",
            extra={
                'error_code': exc.error_code,
                'details': exc.details,
                'context': context,
                'traceback': traceback.format_exc()
            }
        )
    else:
        logger.error(
            f"Unexpected error: {str(exc)}",
            extra={
                'error_type': type(exc).__name__,
                'context': context,
                'traceback': traceback.format_exc()
            }
        )

def handle_api_exception(exc: Exception, default_message: str = "操作失败") -> JsonResponse:
    """统一的API异常处理"""
    log_exception(exc)
    
    if isinstance(exc, ModelStorageException):
        return JsonResponse({
            'error': exc.message,
            'error_code': exc.error_code,
            'details': exc.details
        }, status=400)
    elif isinstance(exc, PermissionError):
        return JsonResponse({
            'error': '权限不足',
            'error_code': 'PERMISSION_DENIED'
        }, status=403)
    elif isinstance(exc, FileNotFoundError):
        return JsonResponse({
            'error': '文件或目录不存在',
            'error_code': 'NOT_FOUND'
        }, status=404)
    else:
        return JsonResponse({
            'error': default_message,
            'error_code': 'INTERNAL_ERROR'
        }, status=500)

def exception_handler(default_message: str = "操作失败"):
    """异常处理装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                return handle_api_exception(e, default_message)
        return wrapper
    return decorator

def safe_execute(func, *args, default_return=None, context: Dict[str, Any] = None, **kwargs):
    """安全执行函数，捕获并记录异常"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        log_exception(e, context)
        return default_return

class NetworkRetryHandler:
    """网络请求重试处理器"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
    
    def execute_with_retry(self, func, *args, **kwargs):
        """执行带重试的网络请求"""
        import time
        import requests
        
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except requests.exceptions.Timeout as e:
                last_exception = TimeoutException(
                    f"网络请求超时 (尝试 {attempt + 1}/{self.max_retries + 1})",
                    operation="network_request"
                )
                if attempt < self.max_retries:
                    delay = self.base_delay * (2 ** attempt)  # 指数退避
                    logger.warning(f"网络请求超时，{delay}秒后重试...")
                    time.sleep(delay)
                    continue
            except requests.exceptions.ConnectionError as e:
                last_exception = NetworkException(
                    f"网络连接失败 (尝试 {attempt + 1}/{self.max_retries + 1}): {str(e)}"
                )
                if attempt < self.max_retries:
                    delay = self.base_delay * (2 ** attempt)
                    logger.warning(f"网络连接失败，{delay}秒后重试...")
                    time.sleep(delay)
                    continue
            except requests.exceptions.RequestException as e:
                last_exception = NetworkException(f"网络请求异常: {str(e)}")
                break
            except Exception as e:
                last_exception = e
                break
        
        raise last_exception

class DatabaseRetryHandler:
    """数据库操作重试处理器"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 0.1):
        self.max_retries = max_retries
        self.base_delay = base_delay
    
    def execute_with_retry(self, func, *args, **kwargs):
        """执行带重试的数据库操作"""
        import time
        
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_msg = str(e).lower()
                if 'database is locked' in error_msg or 'deadlock' in error_msg:
                    last_exception = DatabaseException(
                        f"数据库锁定 (尝试 {attempt + 1}/{self.max_retries + 1})",
                        operation="database_operation"
                    )
                    if attempt < self.max_retries:
                        delay = self.base_delay * (2 ** attempt)  # 指数退避
                        logger.warning(f"数据库锁定，{delay}秒后重试...")
                        time.sleep(delay)
                        continue
                else:
                    last_exception = DatabaseException(f"数据库操作失败: {str(e)}")
                    break
        
        raise last_exception

# 全局实例
network_retry = NetworkRetryHandler()
database_retry = DatabaseRetryHandler()
