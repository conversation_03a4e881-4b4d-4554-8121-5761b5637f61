/* GPU表单样式 */
.gpuModal {
  :global(.ant-modal-content) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.2);
  }
  
  :global(.ant-modal-header) {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 20px 24px;
  }
  
  :global(.ant-modal-body) {
    padding: 24px;
    background: #fafbfc;
  }
  
  :global(.ant-modal-footer) {
    background: #fafbfc;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    padding: 16px 24px;
    text-align: right;
  }
}

.modalTitle {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
}

.gpuForm {
  :global(.ant-form-item-label) {
    > label {
      font-weight: 600;
      color: #374151;
      font-size: 14px;
      
      &.ant-form-item-required::before {
        color: #ef4444;
      }
    }
  }
  
  :global(.ant-form-item-explain) {
    color: #6b7280;
    font-size: 12px;
    margin-top: 4px;
  }
}

.formInput {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #667eea;
  }
  
  &:focus, &.ant-input-focused {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
}

.formTextarea {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
  resize: vertical;
  
  &:hover {
    border-color: #667eea;
  }
  
  &:focus, &.ant-input-focused {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
}

.gradientButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
  
  &:hover, &:focus {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
  
  &:active {
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .gpuModal {
    :global(.ant-modal) {
      margin: 16px;
      max-width: calc(100vw - 32px);
    }
    
    :global(.ant-modal-header) {
      padding: 16px 20px;
    }
    
    :global(.ant-modal-body) {
      padding: 20px;
    }
    
    :global(.ant-modal-footer) {
      padding: 12px 20px;
    }
  }
  
  .modalTitle {
    font-size: 16px;
  }
}
