# Generated by Django 2.2.28 on 2025-07-05 13:38

from django.db import migrations, models
import django.db.models.deletion
import libs.mixins


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0002_new_release_plan_models'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReleasePlan',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('card_model', models.CharField(default='', max_length=64, verbose_name='卡型号')),
                ('model_name', models.CharField(max_length=128, verbose_name='模型名称')),
                ('release_date', models.DateField(verbose_name='计划发布时间')),
                ('model_status', models.CharField(choices=[('complete', '已完成'), ('partial', '部分缺失'), ('missing', '缺失'), ('inProgress', '进行中'), ('released', '已发布'), ('delayed', '延期'), ('preparing', '准备中')], default='preparing', max_length=32, verbose_name='Model状态')),
                ('vendor_status', models.CharField(choices=[('complete', '已完成'), ('partial', '部分缺失'), ('missing', '缺失'), ('inProgress', '进行中'), ('released', '已发布'), ('delayed', '延期'), ('preparing', '准备中')], default='preparing', max_length=32, verbose_name='Vendor状态')),
                ('overall_status', models.CharField(choices=[('complete', '已完成'), ('partial', '部分缺失'), ('missing', '缺失'), ('inProgress', '进行中'), ('released', '已发布'), ('delayed', '延期'), ('preparing', '准备中')], default='preparing', max_length=32, verbose_name='总体状态')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': '发布计划',
                'verbose_name_plural': '发布计划',
                'db_table': 'model_storage_release_plans',
                'ordering': ('-created_at',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.AlterField(
            model_name='testtask',
            name='end_date',
            field=models.DateField(blank=True, null=True, verbose_name='结束时间'),
        ),
        migrations.AlterField(
            model_name='testtask',
            name='framework',
            field=models.CharField(blank=True, choices=[('pytorch', 'PyTorch'), ('tensorflow', 'TensorFlow'), ('onnx', 'ONNX'), ('keras', 'Keras'), ('paddle', 'PaddlePaddle'), ('mindspore', 'MindSpore'), ('other', '其他')], max_length=32, null=True, verbose_name='框架'),
        ),
        migrations.AlterField(
            model_name='testtask',
            name='model_type',
            field=models.CharField(blank=True, choices=[('inference', '推理'), ('training', '训练'), ('fine_tuning', '微调'), ('evaluation', '评估'), ('optimization', '优化')], max_length=32, null=True, verbose_name='模型类型'),
        ),
        migrations.AlterField(
            model_name='testtask',
            name='priority',
            field=models.CharField(blank=True, choices=[('low', '低'), ('medium', '中'), ('high', '高'), ('urgent', '紧急')], default='medium', max_length=32, null=True, verbose_name='优先级'),
        ),
        migrations.AlterField(
            model_name='testtask',
            name='progress',
            field=models.IntegerField(blank=True, default=0, null=True, verbose_name='进度百分比'),
        ),
        migrations.AlterField(
            model_name='testtask',
            name='provider_status',
            field=models.CharField(blank=True, choices=[('waiting', '等待中'), ('provided', '已提供'), ('partial', '部分提供'), ('rejected', '已拒绝')], default='waiting', max_length=32, null=True, verbose_name='模型提供状态'),
        ),
        migrations.AlterField(
            model_name='testtask',
            name='release_plan',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='test_tasks', to='model_storage.NewReleasePlan', verbose_name='发布计划'),
        ),
        migrations.AlterField(
            model_name='testtask',
            name='start_date',
            field=models.DateField(blank=True, null=True, verbose_name='开始时间'),
        ),
        migrations.AlterField(
            model_name='testtask',
            name='test_status',
            field=models.CharField(blank=True, choices=[('pending', '待测试'), ('running', '测试中'), ('completed', '已完成'), ('failed', '测试失败'), ('blocked', '阻塞'), ('cancelled', '已取消')], default='pending', max_length=32, null=True, verbose_name='测试状态'),
        ),
        migrations.AlterField(
            model_name='testtask',
            name='tester',
            field=models.CharField(max_length=128, verbose_name='人员名称'),
        ),
    ]
