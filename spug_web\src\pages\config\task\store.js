/**
 * 任务管理store
 */
import { observable } from "mobx";
import http from 'libs/http';

class Store {
  @observable records = [];
  @observable record = {};
  @observable loading = false;
  @observable formVisible = false;
  @observable infoVisible = false;

  @observable f_name = '';

  get dataSource() {
    let records = this.records;
    if (this.f_name) {
      records = records.filter(item => item['name'].toLowerCase().includes(this.f_name.toLowerCase()))
    }
    return records
  }

  fetchRecords = () => {
    this.loading = true;
    return http.get('/api/config/task/')
      .then(res => this.records = res)
      .finally(() => this.loading = false)
  };

  showForm = (info = {}) => {
    this.formVisible = true;
    this.record = info;
  };

  showInfo = (info) => {
    this.infoVisible = true;
    this.record = info;
  };
}

export default new Store() 