/**
 * 独立的日志分析页面
 * 提取智能结果收集器中的"获取并分析日志"功能
 */
import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Button,
  Space,
  message,
  Breadcrumb,
  Typography,
  Alert,
  Divider
} from 'antd';
import {
  DownloadOutlined,
  BarChartOutlined,
  FileTextOutlined,
  CloudServerOutlined,
  SettingOutlined
} from '@ant-design/icons';
import LogExtractionConfig from '../result-extraction/LogExtractionConfig';
import LogResultsTable from './LogResultsTable';
import styles from './index.module.less';

const { Header, Content } = Layout;
const { Title, Paragraph } = Typography;

function LogAnalysisPage() {
  const [extractionConfigVisible, setExtractionConfigVisible] = useState(false);
  const [analysisResults, setAnalysisResults] = useState([]);
  const [currentConfig, setCurrentConfig] = useState({});
  const [hasResults, setHasResults] = useState(false);



  // 处理日志提取成功
  const handleExtractionSuccess = (results, config) => {

    // 如果results是数组，直接使用；如果是对象，提取results字段
    let analysisData = results;
    let configData = config;

    if (results && typeof results === 'object' && results.results) {
      analysisData = results.results;
      configData = results.config || config;
    }

    setAnalysisResults(analysisData || []);
    setCurrentConfig(configData || {});
    setHasResults(true);
    setExtractionConfigVisible(false);

    const fileCount = Array.isArray(analysisData) ? analysisData.length : 0;
    message.success(`成功提取并分析了 ${fileCount} 个日志文件`);
  };

  // 打开配置弹窗
  const handleOpenConfig = () => {
    setExtractionConfigVisible(true);
  };

  // 清除结果
  const handleClearResults = () => {
    setAnalysisResults([]);
    setHasResults(false);
    setCurrentConfig({});
    message.success('已清除分析结果');
  };

  // 导出结果
  const handleExportResults = () => {
    if (analysisResults.length === 0) {
      message.warning('暂无数据可导出');
      return;
    }

    try {
      const dataStr = JSON.stringify(analysisResults, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `log_analysis_results_${new Date().getTime()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败: ' + error.message);
    }
  };

  return (
    <Layout className={styles.logAnalysisPage}>
      <Header className={styles.pageHeader}>
        <div className={styles.headerContent}>
          <div className={styles.headerLeft}>
            <Breadcrumb>
              <Breadcrumb.Item>批量执行</Breadcrumb.Item>
              <Breadcrumb.Item>日志分析</Breadcrumb.Item>
            </Breadcrumb>
            <Title level={3} style={{ margin: '8px 0 0 0', color: '#fff' }}>
              <FileTextOutlined style={{ marginRight: '8px' }} />
              智能日志分析
            </Title>
          </div>
          
          <div className={styles.headerRight}>
            <Space>

              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleOpenConfig}
                size="large"
              >
                获取并分析日志
              </Button>
              {hasResults && (
                <>
                  <Button
                    icon={<DownloadOutlined />}
                    onClick={handleExportResults}
                    size="large"
                  >
                    导出结果
                  </Button>
                  <Button
                    onClick={handleClearResults}
                    size="large"
                  >
                    清除结果
                  </Button>
                </>
              )}
            </Space>
          </div>
        </div>
      </Header>

      <Content className={styles.pageContent}>
        <div className={styles.contentWrapper}>
          {!hasResults ? (
            <Card className={styles.welcomeCard}>
              <div className={styles.welcomeContent}>
                <div className={styles.welcomeIcon}>
                  <BarChartOutlined />
                </div>
                <Title level={2} style={{ textAlign: 'center', marginBottom: '16px' }}>
                  智能日志分析工具
                </Title>
                <Paragraph style={{ textAlign: 'center', fontSize: '16px', color: '#666' }}>
                  支持远程主机日志文件的智能提取和性能分析，自动识别关键指标
                </Paragraph>
                
                <Divider />
                
                <div className={styles.featureList}>
                  <div className={styles.featureItem}>
                    <CloudServerOutlined className={styles.featureIcon} />
                    <div>
                      <h4>远程主机支持</h4>
                      <p>支持通过SSH连接远程主机，获取日志文件</p>
                    </div>
                  </div>
                  <div className={styles.featureItem}>
                    <FileTextOutlined className={styles.featureIcon} />
                    <div>
                      <h4>多文件分析</h4>
                      <p>同时分析多个日志文件，批量提取性能指标</p>
                    </div>
                  </div>
                  <div className={styles.featureItem}>
                    <BarChartOutlined className={styles.featureIcon} />
                    <div>
                      <h4>智能识别</h4>
                      <p>自动识别响应时间、吞吐量等关键性能指标</p>
                    </div>
                  </div>
                </div>

                <div style={{ textAlign: 'center', marginTop: '32px' }}>
                  <Button
                    type="primary"
                    size="large"
                    icon={<SettingOutlined />}
                    onClick={handleOpenConfig}
                  >
                    开始分析日志
                  </Button>
                </div>
              </div>
            </Card>
          ) : (
            <div className={styles.resultsContainer}>
              <Alert
                message="分析完成"
                description={`已成功分析 ${analysisResults.length} 个日志文件，请查看下方结果表格`}
                type="success"
                showIcon
                style={{ marginBottom: '24px' }}
              />

              <LogResultsTable
                results={analysisResults}
                config={currentConfig}
              />
            </div>
          )}
        </div>
      </Content>

      {/* 日志提取配置弹窗 */}
      <LogExtractionConfig
        visible={extractionConfigVisible}
        onCancel={() => setExtractionConfigVisible(false)}
        onSuccess={handleExtractionSuccess}
        initialLogPath=""
        executionInfo={{}}
      />
    </Layout>
  );
}

export default LogAnalysisPage;
