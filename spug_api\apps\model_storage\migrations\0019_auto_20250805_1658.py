# Generated by Django 2.2.28 on 2025-08-05 16:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0018_add_model_name_to_performance_test_data'),
    ]

    operations = [
        migrations.AddField(
            model_name='performancetestdata',
            name='bios_version',
            field=models.CharField(blank=True, default='', max_length=128, verbose_name='BIOS版本'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='cpu',
            field=models.CharField(blank=True, default='', max_length=256, verbose_name='CPU'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='cpu_mode',
            field=models.CharField(blank=True, default='', max_length=128, verbose_name='CPU模式'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='data_type',
            field=models.CharField(blank=True, default='', max_length=128, verbose_name='数据类型'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='dataset',
            field=models.CharField(blank=True, default='', max_length=256, verbose_name='数据集'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='fan_mode',
            field=models.CharField(blank=True, default='', max_length=64, verbose_name='风扇模式'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='framework',
            field=models.CharField(blank=True, default='', max_length=128, verbose_name='框架'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='framework_version',
            field=models.CharField(blank=True, default='', max_length=64, verbose_name='框架版本'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='gpu_driver_version',
            field=models.CharField(blank=True, default='', max_length=128, verbose_name='GPU驱动版本'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='gpu_firmware_version',
            field=models.CharField(blank=True, default='', max_length=128, verbose_name='GPU固件版本'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='image_version_info',
            field=models.TextField(blank=True, default='', verbose_name='镜像版本信息'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='iommu_status',
            field=models.CharField(blank=True, default='', max_length=64, verbose_name='IOMMU状态'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='machine_model',
            field=models.CharField(blank=True, default='', max_length=128, verbose_name='机型'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='memory',
            field=models.CharField(blank=True, default='', max_length=128, verbose_name='内存'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='network_card',
            field=models.CharField(blank=True, default='', max_length=256, verbose_name='网卡'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='network_type',
            field=models.CharField(blank=True, default='', max_length=128, verbose_name='网络类型'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='os_kernel',
            field=models.CharField(blank=True, default='', max_length=256, verbose_name='OS (内核)'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='remarks',
            field=models.TextField(blank=True, default='', verbose_name='备注'),
        ),
        migrations.AddField(
            model_name='performancetestdata',
            name='topology',
            field=models.CharField(blank=True, default='', max_length=256, verbose_name='拓扑'),
        ),
    ]
