/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import { Dropdown } from 'antd';
import Action from './Action';
import AuthButton from './AuthButton';
import AuthCard from './AuthCard';
import AuthDiv from './AuthDiv';
import AuthFragment from './AuthFragment';
import Breadcrumb from './Breadcrumb';
import SearchForm from './SearchForm';
import TableCard from './TableCard';
import LinkButton from './LinkButton';
import ACEditor from './ACEditor';
import SimpleCodeEditor from './SimpleCodeEditor';
import StatisticsCard from './StatisticsCard';
import NotFound from './NotFound';
import DragUpload from './DragUpload';
import FileTree from './FileTree';
import AppSelector from './AppSelector';
import Link from './Link';

// 修复Dropdown组件的bottomCenter警告
const originalDropdown = Dropdown;
Dropdown.defaultProps = {
  ...originalDropdown.defaultProps,
  placement: 'bottom'
};

export {
  Action,
  AuthButton,
  AuthCard,
  AuthDiv,
  AuthFragment,
  Breadcrumb,
  SearchForm,
  TableCard,
  LinkButton,
  ACEditor,
  SimpleCodeEditor,
  StatisticsCard,
  NotFound,
  DragUpload,
  FileTree,
  AppSelector,
  Link,
  Dropdown,
}
