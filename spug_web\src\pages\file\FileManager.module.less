.fileManager {
  padding: 20px;
  height: calc(100vh - 64px);
  overflow: hidden;

  :global(.ant-card) {
    .ant-card-head {
      padding: 0 24px;
    }

    .ant-card-body {
      padding: 16px 24px;
    }
  }

  :global(.ant-table) {
    .ant-table-tbody > tr > td {
      padding: 8px 16px;
    }

    .ant-table-thead > tr > th {
      padding: 12px 16px;
      background: #fafafa;
    }
  }

  :global(.ant-upload) {
    .ant-upload-btn {
      border: none;
      padding: 0;
    }
  }

  // 文件类型图标样式
  .fileIcon {
    margin-right: 8px;
    font-size: 16px;
  }

  // 操作按钮样式
  :global(.ant-btn-link) {
    padding: 4px 8px;
    height: auto;
    
    &:hover {
      background: rgba(0, 0, 0, 0.04);
      border-radius: 4px;
    }
  }

  // 表格行高亮
  :global(.ant-table-tbody > tr:hover > td) {
    background: #f5f5f5;
  }

  // 文件管理界面的拖拽区域
  .fileManagerDragArea {
    min-height: 200px;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: #999;
    font-size: 14px;
    transition: all 0.3s;
    cursor: pointer;

    &:hover {
      border-color: #1890ff;
      color: #1890ff;
    }

    &.active {
      border-color: #1890ff;
      background-color: #f0f8ff;
      color: #1890ff;
    }

    .icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.6;
    }

    .text {
      margin: 0;
      line-height: 1.5;
    }
  }

  // 文件分发界面的拖拽区域
  .transferDragArea {
    padding: 20px;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    text-align: center;
    color: #999;
    transition: all 0.3s;
    cursor: pointer;

    &:hover {
      border-color: #1890ff;
      color: #1890ff;
    }

    &.active {
      border-color: #1890ff;
      background-color: #f0f8ff;
      color: #1890ff;
    }

    .icon {
      font-size: 32px;
      margin-bottom: 8px;
      opacity: 0.6;
    }

    .text {
      margin: 0;
      font-size: 14px;
    }
  }

  // 拖拽样式
  :global(.ant-table-tbody > tr[draggable="true"]) {
    transition: all 0.2s;
    
    &:hover {
      background-color: #f5f5f5;
      cursor: grab;
    }
    
    &:active {
      cursor: grabbing;
      opacity: 0.7;
    }
  }

  // 拖拽指示器
  .dragIndicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #ccc;
    cursor: grab;
    margin-right: 8px;
    
    &:hover {
      background: #1890ff;
    }
  }

  // 转移对话框样式
  .transferModal {
    .selectedFiles {
      max-height: 120px;
      overflow-y: auto;
      background: #f5f5f5;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      
      .fileItem {
        display: flex;
        align-items: center;
        padding: 4px 0;
        border-bottom: 1px solid #e8e8e8;
        
        &:last-child {
          border-bottom: none;
        }
        
        .fileIcon {
          margin-right: 8px;
        }
      }
    }
    
    .targetSelector {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 8px;
      background: #fafafa;
      
      .currentPath {
        margin-bottom: 8px;
        font-size: 12px;
        color: #666;
        
        .pathValue {
          font-weight: bold;
          color: #1890ff;
        }
      }
    }
  }

  // 表格选择高亮
  :global(.ant-table-tbody > tr.ant-table-row-selected > td) {
    background-color: #e6f7ff;
  }

  // 转移按钮样式
  .transferButton {
    border-color: #1890ff;
    color: #1890ff;
    
    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
  }

  // Monaco编辑器相关样式
  .monacoEditor {
    :global(.monaco-editor) {
      .monaco-editor-background {
        background-color: #1e1e1e !important;
      }
      
      .margin {
        background-color: #1e1e1e !important;
      }
    }
  }

  // Ace编辑器相关样式
  .aceEditor {
    :global(.ace_editor) {
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      
      .ace_gutter {
        background: #2f3129 !important;
        color: #8f908a !important;
      }
      
      .ace_active-line {
        background: rgba(255, 255, 255, 0.1) !important;
      }
      
      .ace_cursor {
        color: #f8f8f2 !important;
      }
      
      .ace_selection {
        background: rgba(90, 100, 126, 0.33) !important;
      }
      
      .ace_selected-word {
        background: rgba(90, 100, 126, 0.33) !important;
        border: 1px solid rgba(90, 100, 126, 0.66) !important;
      }
    }
  }

  // 编辑器模态框样式
  :global(.ant-modal) {
    .ant-modal-body {
      padding: 0 !important;
    }
    
    // Ace编辑器在模态框中的样式调整
    .ace_editor {
      border-radius: 0 !important;
      border: none !important;
    }
  }

  // 文件名渲染样式
  .editableFileName {
    color: #1890ff;
    font-weight: bold;
    cursor: pointer;
    
    &:hover {
      text-decoration: underline;
    }
  }

  .normalFileName {
    color: inherit;
    font-weight: normal;
  }

  // 面包屑导航样式
  :global(.ant-breadcrumb) {
    a {
      color: #1890ff;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }

  // 语言标签样式
  .languageTag {
    font-size: 12px;
    margin-left: 8px;
  }
} 