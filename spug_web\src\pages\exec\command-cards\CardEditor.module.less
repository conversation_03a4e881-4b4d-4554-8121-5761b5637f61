.cardEditor {
  padding: 0;
  
  .editorHeader {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #fafafa;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
  }
  
  .editorContent {
    .commandList {
      margin-bottom: 16px;
      
      .editingRow {
        background-color: #e6f7ff !important;
      }
    }
    
    .editingCard {
      margin-top: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
  
  .helpInfo {
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    
    h4 {
      margin-bottom: 8px;
      color: #1890ff;
    }
    
    ul {
      margin: 0;
      padding-left: 16px;
      
      li {
        margin-bottom: 4px;
        color: #666;
        font-size: 12px;
      }
    }
  }
}

/* Drawer特定样式 */
:global(.ant-drawer-body) {
  .cardEditor {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .editorContent {
      flex: 1;
      overflow-y: auto;
    }
  }
} 