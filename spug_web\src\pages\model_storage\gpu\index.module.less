/* GPU管理页面样式 */
.container {
  min-height: 100vh;
  padding: 24px;
  position: relative;
  background: transparent; /* 使用透明背景，让body背景显示 */

  /* 动态背景效果 */
  &::before {
    content: '';
    position: fixed; /* 使用fixed定位覆盖整个视窗 */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundMove 20s ease-in-out infinite;
    z-index: -1; /* 确保在最底层 */
    pointer-events: none; /* 不影响鼠标事件 */
  }

  > * {
    position: relative;
    z-index: 1;
  }
}

@keyframes backgroundMove {
  0%, 100% {
    transform: translateX(0) translateY(0);
  }
  25% {
    transform: translateX(-20px) translateY(-10px);
  }
  50% {
    transform: translateX(20px) translateY(10px);
  }
  75% {
    transform: translateX(-10px) translateY(20px);
  }
}

/* 页面头部样式 */
.header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
  font-size: 32px;
  font-weight: bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  display: flex;
  align-items: center;
}

/* 统计卡片样式 */
.statsCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
  }
  
  :global(.ant-card-body) {
    padding: 24px;
  }
  
  :global(.ant-statistic-title) {
    color: #666;
    font-weight: 500;
    margin-bottom: 8px;
  }
}

/* 页面标题 */
.pageHeader {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 24px;
  padding: 24px 32px;

  .headerTitle {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;

    .headerIcon {
      font-size: 32px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .headerSubtitle {
    color: #666;
    font-size: 16px;
    margin-top: 8px;
    font-weight: 400;
  }
}

/* 搜索卡片样式 */
.searchCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 24px;

  :global(.ant-card-body) {
    padding: 24px;
  }

  :global(.ant-card-head) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

    :global(.ant-card-head-title) {
      font-weight: 600;
      color: #2c3e50;
    }
  }
}

/* 搜索输入框样式 */
.searchInput {
  border-radius: 12px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(102, 126, 234, 0.4);
  }

  &:focus, &.ant-input-focused {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
}

/* 搜索选择器样式 */
.searchSelect {
  :global(.ant-select-selector) {
    border-radius: 12px;
    border: 2px solid rgba(102, 126, 234, 0.2);
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(102, 126, 234, 0.4);
    }
  }

  &:global(.ant-select-focused) {
    :global(.ant-select-selector) {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
}

/* 按钮样式 */
.primaryButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  height: 40px;
  padding: 0 20px;

  &:hover, &:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }

  &:active {
    transform: translateY(0);
  }
}

.secondaryButton {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 12px;
  color: #667eea;
  font-weight: 600;
  height: 40px;
  padding: 0 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover, &:focus {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
  }

  &:active {
    transform: translateY(0);
  }
}

/* 渐变按钮样式 - 保持向后兼容 */
.gradientButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;

  &:hover, &:focus {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
}

/* GPU卡片样式 */
.gpuCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  height: 320px;
  display: flex;
  flex-direction: column;
  position: relative;

  /* 卡片顶部装饰条 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    z-index: 1;
  }

  /* 响应式高度调整 */
  @media (max-width: 768px) {
    height: 300px;
  }

  @media (max-width: 576px) {
    height: 280px;
  }

  &:hover {
    transform: translateY(-12px);
    box-shadow: 0 20px 80px rgba(0, 0, 0, 0.15);
    border-color: rgba(102, 126, 234, 0.4);

    &::before {
      height: 6px;
    }
  }

  :global(.ant-card-body) {
    padding: 24px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    z-index: 2;

    /* 移动端调整内边距 */
    @media (max-width: 576px) {
      padding: 20px;
    }
  }

  :global(.ant-card-actions) {
    background: rgba(102, 126, 234, 0.05);
    border-top: 1px solid rgba(102, 126, 234, 0.1);

    :global(.ant-card-actions > li) {
      margin: 8px 0;

      :global(.anticon) {
        font-size: 16px;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.2);
        }
      }
    }
  }

  :global(.ant-card-actions) {
    background: rgba(248, 250, 252, 0.8);
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    margin-top: auto; /* 将操作按钮推到底部 */

    li {
      margin: 8px 0;

      .anticon {
        font-size: 16px;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.2);
        }
      }
    }
  }
}

.gpuCardHeader {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  flex-shrink: 0; /* 防止头部被压缩 */
  min-height: 48px; /* 确保头部有足够高度 */
}

.gpuIcon {
  font-size: 24px;
  color: #667eea;
  margin-right: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gpuName {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}

.gpuInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .infoItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    min-height: 24px; /* 确保每个信息项有最小高度 */

    &:last-child {
      margin-bottom: 0;
    }
  }

  .infoLabel {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
  }
}

/* 标签样式 */
.vendorTag {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  padding: 4px 12px;
}

.countTag {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  padding: 4px 12px;
}

.modelTag {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  margin: 2px;
}

.modelList {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.modelPreview {
  cursor: pointer;
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */

  .modelText {
    color: #374151;
    font-size: 13px;
    margin-top: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%; /* 确保文本不会溢出 */
  }
}

.description {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  font-size: 13px;
  color: #6b7280;
  line-height: 1.5;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 限制显示2行 */
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  max-height: 40px; /* 限制最大高度 */
}

/* 卡片加载状态优化 */
.gpuCard {
  /* 确保加载时的骨架屏效果 */
  &.loading {
    .gpuCardHeader,
    .gpuInfo {
      opacity: 0.6;
    }
  }
}

/* 空状态优化 */
.emptyCard {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #8c8c8c;
  font-size: 14px;
}

/* 分组视图样式 */
.groupView {
  margin-bottom: 24px;
}

.groupCollapse {
  background: transparent;
  border: none;

  :global(.ant-collapse-item) {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
    margin-bottom: 16px;
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }
  }

  :global(.ant-collapse-header) {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 16px 20px;

    &:hover {
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    }
  }

  :global(.ant-collapse-content) {
    border-top: none;
    background: transparent;
  }

  :global(.ant-collapse-content-box) {
    padding: 20px;
  }
}

.groupHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.groupTitle {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
}

.groupCount {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  padding: 4px 12px;
  font-size: 12px;
}

.emptyGroup {
  text-align: center;
  padding: 40px 0;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* 分页样式 */
.paginationContainer {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.pagination {
  :global(.ant-pagination-item) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.3s ease;

    &:hover {
      border-color: #667eea;
      transform: translateY(-1px);
    }

    a {
      color: #374151;
      font-weight: 500;
    }
  }

  :global(.ant-pagination-item-active) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;

    a {
      color: white;
    }

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      border-color: #5a6fd8;
    }
  }

  :global(.ant-pagination-prev),
  :global(.ant-pagination-next),
  :global(.ant-pagination-jump-prev),
  :global(.ant-pagination-jump-next) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.3s ease;

    &:hover {
      border-color: #667eea;
      color: #667eea;
      transform: translateY(-1px);
    }
  }

  :global(.ant-pagination-options) {
    .ant-select-selector {
      border-radius: 8px;
      border: 1px solid #d1d5db;

      &:hover {
        border-color: #667eea;
      }
    }
  }

  :global(.ant-pagination-total-text) {
    color: #6b7280;
    font-weight: 500;
  }
}

/* 空状态样式 */
.emptyContainer {
  text-align: center;
  padding: 60px 0;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .header {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .searchCard {
    margin-bottom: 16px;
    
    :global(.ant-card-body) {
      padding: 16px;
    }
  }
  
  .gpuCard {
    :global(.ant-card-body) {
      padding: 16px;
    }
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  
  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
}
