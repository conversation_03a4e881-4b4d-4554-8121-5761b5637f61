# Generated by Django 2.2.28 on 2025-07-03 10:51

from django.db import migrations, models
import django.db.models.deletion
import libs.mixins


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='RemoteFolder',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='名称')),
                ('remote_path', models.Char<PERSON>ield(max_length=128, verbose_name='远程路径')),
                ('username', models.CharField(blank=True, max_length=100, null=True, verbose_name='用户名')),
                ('password', models.CharField(blank=True, max_length=200, null=True, verbose_name='密码')),
                ('domain', models.CharField(blank=True, max_length=100, null=True, verbose_name='域名')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('host_id', models.IntegerField(verbose_name='主机ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '远程文件夹',
                'verbose_name_plural': '远程文件夹',
                'db_table': 'remote_folders',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='RemoteFolderCache',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('path', models.CharField(default='', max_length=128, verbose_name='相对路径')),
                ('cache_time', models.DateTimeField(auto_now=True, verbose_name='缓存时间')),
                ('is_valid', models.BooleanField(default=True, verbose_name='缓存是否有效')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('file_count', models.IntegerField(default=0, verbose_name='文件数量')),
                ('cache_size', models.BigIntegerField(default=0, verbose_name='缓存大小（字节）')),
                ('folder_id', models.IntegerField(verbose_name='文件夹ID')),
                ('is_dir', models.BooleanField(default=False, verbose_name='是否是目录')),
                ('size', models.IntegerField(default=0, verbose_name='文件大小')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('remote_folder', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='file.RemoteFolder', verbose_name='远程文件夹')),
            ],
            options={
                'verbose_name': '远程文件夹缓存元数据',
                'verbose_name_plural': '远程文件夹缓存元数据',
                'db_table': 'remote_folder_caches',
                'ordering': ('-id',),
                'unique_together': {('remote_folder', 'path')},
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
