.logViewerContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  border-radius: 6px;
  overflow: hidden;
  
  .toolbar {
    padding: 12px;
    background: white;
    border-bottom: 1px solid #e8e8e8;
    flex-shrink: 0;
  }
  
  .logContent {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background: #1e1e1e;
    color: #d4d4d4;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
    padding: 8px 0;
    user-select: text;
    cursor: text;
    
    &.dragging {
      cursor: grabbing;
      
      .logLine {
        pointer-events: none;
      }
    }
    
    .logLine {
      display: flex;
      padding: 2px 0;
      min-height: 18px;
      border-left: 3px solid transparent;
      transition: all 0.2s ease;
      position: relative;
      
      &:hover {
        background: rgba(255, 255, 255, 0.05);
      }
      
      &.highlighted {
        background: rgba(24, 144, 255, 0.2);
        border-left-color: #1890ff;
      }
      
      &.suggested {
        background: rgba(114, 46, 209, 0.15);
        border-left-color: #722ed1;
      }
      
      .lineNumber {
        color: #858585;
        padding: 0 12px;
        text-align: right;
        flex-shrink: 0;
        width: 60px;
        cursor: pointer;
        user-select: none;
        
        &:hover {
          color: #1890ff;
        }
      }
      
      .lineContent {
        flex: 1;
        padding-right: 12px;
        word-break: break-word;
        white-space: pre-wrap;
        overflow-wrap: break-word;
        max-width: calc(100% - 80px);
      }
      
      .suggestionTip {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
  
  .emptyState {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #999;
  }
  
  .dragHint {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #1890ff;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 1000;
    pointer-events: none;
    animation: pulse 1s infinite;
  }
}

@keyframes pulse {
  0% { opacity: 0.8; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 1; transform: translate(-50%, -50%) scale(1.05); }
  100% { opacity: 0.8; transform: translate(-50%, -50%) scale(1); }
} 