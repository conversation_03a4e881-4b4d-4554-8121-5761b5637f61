import React, { useState, useEffect } from 'react';
import { 
  Drawer, 
  Card, 
  Checkbox, 
  Progress, 
  Button, 
  Space, 
  Typography, 
  Divider, 
  Slider, 
  message,
  Spin,
  Tag,
  Collapse,
  Empty
} from 'antd';
import { 
  CheckCircleOutlined, 
  ClockCircleOutlined, 
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  FileTextOutlined,
  PlayCircleOutlined,
  CheckSquareOutlined
} from '@ant-design/icons';
import http from 'libs/http';
import styles from './TestStepsSidebar.module.css';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

const TestStepsSidebar = ({ visible, onClose, caseSet, onUpdate, taskId, mode = 'case_set' }) => {
  const [loading, setLoading] = useState(false);
  const [stepStatus, setStepStatus] = useState({});
  const [manualProgress, setManualProgress] = useState(0);
  const [isManualMode, setIsManualMode] = useState(false);
  const [calculatedProgress, setCalculatedProgress] = useState(0);

  useEffect(() => {
    if (visible && caseSet) {
      initializeStepStatus();
    }
  }, [visible, caseSet]);

  const initializeStepStatus = () => {
    if (!caseSet) return;

    let initialStatus = {};
    let initialProgress = 0;

    if (mode === 'task') {
      // 任务模式：从taskId获取任务的步骤状态和进度
      initialStatus = caseSet.step_completion_status || {};
      initialProgress = caseSet.progress || 0;
    } else {
      // 用例集模式：从caseSet获取步骤状态和进度
      initialStatus = caseSet.step_completion_status || {};
      initialProgress = caseSet.progress_percentage || 0;
    }

    setStepStatus(initialStatus);
    setManualProgress(initialProgress);
    setCalculatedProgress(calculateProgress(initialStatus));
  };

  const calculateProgress = (status) => {
    if (!caseSet || !caseSet.test_cases || caseSet.test_cases.length === 0) {
      return 0;
    }

    let totalSteps = 0;
    let completedSteps = 0;

    caseSet.test_cases.forEach(testCase => {
      const caseId = testCase.id;
      if (!caseId) return;

      // 只计算测试步骤，前置条件和预期结果不参与进度计算
      if (testCase.test_steps) {
        totalSteps++;
        const caseStatus = status[caseId] || {};
        if (caseStatus.test_steps_completed) {
          completedSteps++;
        }
      }
    });

    return totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;
  };

  const handleStepToggle = (caseId, stepType, checked) => {
    const newStatus = {
      ...stepStatus,
      [caseId]: {
        ...stepStatus[caseId],
        [stepType]: checked
      }
    };
    setStepStatus(newStatus);
    
    const newProgress = calculateProgress(newStatus);
    setCalculatedProgress(newProgress);
    
    // 如果不是手动模式，自动更新进度
    if (!isManualMode) {
      setManualProgress(newProgress);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const progressToUse = isManualMode ? manualProgress : calculatedProgress;

      let apiUrl, updateData;

      if (mode === 'task' && taskId) {
        // 任务模式：更新测试任务的步骤状态
        apiUrl = `/api/model-storage/test-tasks/${taskId}/step-status/`;
        updateData = {
          step_completion_status: stepStatus,
          progress_percentage: progressToUse
        };
      } else {
        // 用例集模式：更新测试用例集的步骤状态
        apiUrl = `/api/exec/test-case-sets/${caseSet.id}/step-status/`;
        updateData = {
          step_completion_status: stepStatus,
          progress_percentage: progressToUse
        };
      }

      await http.put(apiUrl, updateData);

      message.success('测试步骤状态保存成功');

      // 通知父组件更新
      if (onUpdate) {
        if (mode === 'task') {
          onUpdate({
            ...caseSet,
            step_completion_status: stepStatus,
            progress: progressToUse
          });
        } else {
          onUpdate({
            ...caseSet,
            step_completion_status: stepStatus,
            progress_percentage: progressToUse
          });
        }
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleManualProgressSave = async () => {
    setLoading(true);
    try {
      let apiUrl;

      if (mode === 'task' && taskId) {
        // 任务模式：更新测试任务的进度
        apiUrl = `/api/model-storage/test-tasks/${taskId}/step-status/`;
      } else {
        // 用例集模式：更新测试用例集的进度
        apiUrl = `/api/exec/test-case-sets/${caseSet.id}/step-status/`;
      }

      await http.post(apiUrl, {
        progress_percentage: manualProgress
      });

      message.success('手动进度保存成功');

      // 通知父组件更新
      if (onUpdate) {
        if (mode === 'task') {
          onUpdate({
            ...caseSet,
            progress: manualProgress
          });
        } else {
          onUpdate({
            ...caseSet,
            progress_percentage: manualProgress
          });
        }
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  const renderTestCaseSteps = (testCase) => {
    const caseId = testCase.id;
    const caseStatus = stepStatus[caseId] || {};

    // 只有测试步骤可以勾选，计算进度时只考虑测试步骤
    const testStepsCompleted = caseStatus.test_steps_completed || false;
    const hasTestSteps = !!testCase.test_steps;

    return (
      <Card
        key={caseId}
        size="small"
        className={styles.testCaseCard}
        title={
          <div className={styles.caseTitle}>
            <Text strong>{testCase.name || `用例${caseId}`}</Text>
            <Tag color={hasTestSteps && testStepsCompleted ? 'success' : 'processing'}>
              {hasTestSteps ? (testStepsCompleted ? '1/1' : '0/1') : '0/0'}
            </Tag>
          </div>
        }
      >
        <div className={styles.testCaseLayout}>
          {/* 前置条件 - 左侧 */}
          {testCase.precondition && (
            <div className={styles.preconditionSection}>
              <div className={styles.sectionHeader}>
                <FileTextOutlined className={styles.sectionIcon} />
                <Text strong className={styles.sectionTitle}>前置条件</Text>
              </div>
              <div className={styles.sectionContent}>
                <Paragraph
                  className={styles.readOnlyContent}
                  ellipsis={{ rows: 3, expandable: true, symbol: '展开' }}
                >
                  {testCase.precondition}
                </Paragraph>
              </div>
            </div>
          )}

          {/* 测试步骤 - 中间 */}
          {testCase.test_steps && (
            <div className={styles.testStepsSection}>
              <div className={styles.sectionHeader}>
                <Checkbox
                  checked={testStepsCompleted}
                  onChange={(e) => handleStepToggle(caseId, 'test_steps_completed', e.target.checked)}
                  className={styles.stepCheckbox}
                >
                  <Space>
                    <PlayCircleOutlined className={styles.sectionIcon} />
                    <Text strong className={testStepsCompleted ? styles.completedStep : styles.sectionTitle}>
                      测试步骤
                    </Text>
                  </Space>
                </Checkbox>
              </div>
              <div className={styles.sectionContent}>
                <Paragraph
                  className={styles.stepContent}
                  ellipsis={{ rows: 4, expandable: true, symbol: '展开' }}
                >
                  {testCase.test_steps}
                </Paragraph>
              </div>
            </div>
          )}

          {/* 预期结果 - 右侧 */}
          {testCase.expected_result && (
            <div className={styles.expectedResultSection}>
              <div className={styles.sectionHeader}>
                <CheckSquareOutlined className={styles.sectionIcon} />
                <Text strong className={styles.sectionTitle}>预期结果</Text>
              </div>
              <div className={styles.sectionContent}>
                <Paragraph
                  className={styles.readOnlyContent}
                  ellipsis={{ rows: 3, expandable: true, symbol: '展开' }}
                >
                  {testCase.expected_result}
                </Paragraph>
              </div>
            </div>
          )}
        </div>
      </Card>
    );
  };

  if (!caseSet) return null;

  return (
    <Drawer
      title={
        <div className={styles.drawerTitle}>
          <CheckCircleOutlined style={{ marginRight: 8, color: '#52c41a' }} />
          {mode === 'task' ? '任务测试步骤进度' : '测试步骤进度'}: {caseSet.name}
        </div>
      }
      placement="right"
      width={600}
      visible={visible}
      onClose={onClose}
      maskClosable={false}
      className={styles.testStepsDrawer}
      footer={
        <div className={styles.drawerFooter}>
          <Space>
            <Button onClick={onClose} icon={<CloseOutlined />}>
              关闭
            </Button>
            <Button 
              type="primary" 
              onClick={handleSave}
              loading={loading}
              icon={<SaveOutlined />}
            >
              保存进度
            </Button>
          </Space>
        </div>
      }
    >
      <Spin spinning={loading}>
        {/* 进度概览 */}
        <Card className={styles.progressCard}>
          <Title level={5}>
            <ClockCircleOutlined style={{ marginRight: 8 }} />
            进度概览
          </Title>
          
          <div className={styles.progressSection}>
            <Text>智能计算进度:</Text>
            <Progress 
              percent={calculatedProgress} 
              size="small"
              strokeColor="#52c41a"
              className={styles.progressBar}
            />
          </div>

          <Divider />

          <div className={styles.manualProgressSection}>
            <div className={styles.manualHeader}>
              <Checkbox
                checked={isManualMode}
                onChange={(e) => setIsManualMode(e.target.checked)}
              >
                手动调整进度
              </Checkbox>
              {isManualMode && (
                <Button 
                  size="small" 
                  type="primary"
                  onClick={handleManualProgressSave}
                  loading={loading}
                >
                  保存手动进度
                </Button>
              )}
            </div>
            
            {isManualMode && (
              <div className={styles.sliderSection}>
                <Slider
                  value={manualProgress}
                  onChange={setManualProgress}
                  min={0}
                  max={100}
                  marks={{
                    0: '0%',
                    25: '25%',
                    50: '50%',
                    75: '75%',
                    100: '100%'
                  }}
                />
                <Text className={styles.progressText}>
                  当前进度: {manualProgress}%
                </Text>
              </div>
            )}
          </div>
        </Card>

        {/* 测试用例列表 */}
        <div className={styles.testCasesList}>
          {caseSet.test_cases && caseSet.test_cases.length > 0 ? (
            caseSet.test_cases.map(testCase => renderTestCaseSteps(testCase))
          ) : (
            <Empty 
              description="暂无测试用例"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </div>
      </Spin>
    </Drawer>
  );
};

export default TestStepsSidebar;
