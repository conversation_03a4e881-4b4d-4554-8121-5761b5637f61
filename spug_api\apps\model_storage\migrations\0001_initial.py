# Generated by Django 2.2.28 on 2025-07-03 13:18

from django.db import migrations, models
import libs.mixins


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='FileStatus',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_path', models.CharField(max_length=512, verbose_name='文件路径')),
                ('file_type', models.CharField(choices=[('folder', '文件夹'), ('file', '文件')], default='file', max_length=32, verbose_name='文件类型')),
                ('status', models.CharField(choices=[('synced', '已同步'), ('missing', '缺失'), ('outdated', '过期')], default='missing', max_length=32, verbose_name='状态')),
                ('size', models.BigIntegerField(blank=True, null=True, verbose_name='文件大小')),
                ('md5_hash', models.CharField(blank=True, max_length=32, null=True, verbose_name='MD5哈希')),
                ('last_modified', models.DateTimeField(blank=True, null=True, verbose_name='最后修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': '文件状态',
                'verbose_name_plural': '文件状态',
                'db_table': 'model_storage_file_status',
                'ordering': ('file_path',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='ServerMetrics',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('disk_usage', models.FloatField(verbose_name='磁盘使用率')),
                ('cpu_usage', models.FloatField(verbose_name='CPU使用率')),
                ('memory_usage', models.FloatField(default=0.0, verbose_name='内存使用率')),
                ('network_upload', models.CharField(default='0MB', max_length=32, verbose_name='网络上传')),
                ('network_download', models.CharField(default='0MB', max_length=32, verbose_name='网络下载')),
                ('network_total', models.CharField(default='0MB', max_length=32, verbose_name='网络总计')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': '服务器指标',
                'verbose_name_plural': '服务器指标',
                'db_table': 'model_storage_server_metrics',
                'ordering': ('-timestamp',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
