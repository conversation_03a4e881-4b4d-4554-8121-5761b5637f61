# Generated by Django 2.2.28 on 2025-07-09 20:18

from django.db import migrations, models
import libs.mixins
import libs.utils


class Migration(migrations.Migration):

    dependencies = [
        ('exec', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TestCaseSet',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='用例集名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='用例集描述')),
                ('category', models.CharField(blank=True, max_length=50, null=True, verbose_name='用例集分类')),
                ('test_cases', models.TextField(default='[]', verbose_name='测试用例JSON')),
                ('created_at', models.Char<PERSON>ield(default=libs.utils.human_datetime, max_length=20, verbose_name='创建时间')),
                ('created_by_id', models.IntegerField(default=1, verbose_name='创建人ID')),
                ('updated_at', models.CharField(max_length=20, null=True, verbose_name='更新时间')),
                ('updated_by_id', models.IntegerField(null=True, verbose_name='更新人ID')),
            ],
            options={
                'verbose_name': '测试用例集',
                'verbose_name_plural': '测试用例集',
                'db_table': 'exec_test_case_sets',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
