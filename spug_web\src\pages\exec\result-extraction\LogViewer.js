/**
 * 智能日志查看器 - 支持文本选择和拖拽到指标面板
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Space, Tag, Tooltip, message } from 'antd';
import { 
  SelectOutlined, 
  HighlightOutlined, 
  ClearOutlined,
  EyeOutlined,
  RobotOutlined,
  DragOutlined
} from '@ant-design/icons';
import styles from './LogViewer.module.less';

function LogViewer({ content, suggestions, selectedLines, onLineSelection, onTextDrag }) {
  const [lines, setLines] = useState([]);
  const [highlightedLines, setHighlightedLines] = useState(new Set());
  const [selectedText, setSelectedText] = useState('');
  const logViewerRef = useRef(null);

  useEffect(() => {
    if (content) {
      const logLines = content.split('\n').map((line, index) => ({
        number: index + 1,
        content: line,
        suggested: false
      }));
      setLines(logLines);
    }
  }, [content]);

  useEffect(() => {
    // 根据AI建议高亮相关行
    if (suggestions && suggestions.length > 0) {
      const suggestedLineNumbers = new Set();
      suggestions.forEach(suggestion => {
        if (suggestion.lineNumbers) {
          suggestion.lineNumbers.forEach(lineNum => {
            suggestedLineNumbers.add(lineNum);
          });
        }
      });
      
      setLines(prevLines => 
        prevLines.map(line => ({
          ...line,
          suggested: suggestedLineNumbers.has(line.number)
        }))
      );
    }
  }, [suggestions]);

  // 处理文本选择变化
  const handleSelectionChange = () => {
    const selection = window.getSelection();
    const text = selection.toString().trim();
    setSelectedText(text);
  };

  // 处理行点击选择
  const handleLineClick = (lineNumber, e) => {
    e.preventDefault();
    
    // 获取该行的文本内容
    const lineContent = lines.find(line => line.number === lineNumber)?.content || '';
    
    // 选择整行文本
    const range = document.createRange();
    const lineElement = e.currentTarget.querySelector(`.${styles.lineContent}`);
    if (lineElement) {
      range.selectNodeContents(lineElement);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
      
      setSelectedText(lineContent);
      setHighlightedLines(new Set([lineNumber]));
      onLineSelection && onLineSelection([lineNumber]);
      
      message.success('已选择整行，可以拖拽到右侧面板');
    }
  };

  // 处理双击选择单词
  const handleDoubleClick = (e) => {
    e.preventDefault();
    // 双击自动选择单词，浏览器默认行为
    setTimeout(() => {
      const selection = window.getSelection();
      const text = selection.toString().trim();
      setSelectedText(text);
    }, 10);
  };

  // 清除选择
  const clearSelection = () => {
    setHighlightedLines(new Set());
    setSelectedText('');
    window.getSelection().removeAllRanges();
    onLineSelection && onLineSelection([]);
    message.info('已清除选择');
  };

  // 选择AI建议的行
  const selectSuggestedLines = () => {
    const suggestedLines = lines
      .filter(line => line.suggested)
      .map(line => line.number);
    
    if (suggestedLines.length > 0) {
      setHighlightedLines(new Set(suggestedLines));
      onLineSelection && onLineSelection(suggestedLines);
      
      // 选择所有建议行的文本
      const suggestedText = suggestedLines
        .map(lineNum => lines.find(line => line.number === lineNum)?.content || '')
        .join('\n');
      setSelectedText(suggestedText);
      
      message.success(`已选择 ${suggestedLines.length} 行AI建议的内容`);
    }
  };

  // 获取行的CSS类名
  const getLineClassName = (line) => {
    const classes = [styles.logLine];
    
    if (highlightedLines.has(line.number)) {
      classes.push(styles.highlighted);
    }
    
    if (line.suggested) {
      classes.push(styles.suggested);
    }
    
    return classes.join(' ');
  };

  // 获取某行的AI建议
  const getSuggestionForLine = (lineNumber) => {
    return suggestions?.find(suggestion => 
      suggestion.lineNumbers && suggestion.lineNumbers.includes(lineNumber)
    );
  };

  // 添加选择变化监听
  useEffect(() => {
    const handleGlobalSelectionChange = () => {
      handleSelectionChange();
    };

    document.addEventListener('selectionchange', handleGlobalSelectionChange);
    
    return () => {
      document.removeEventListener('selectionchange', handleGlobalSelectionChange);
    };
  }, []);

  return (
    <div className={styles.logViewerContainer}>
      <div className={styles.toolbar}>
        <Space>
          <Tag icon={<DragOutlined />} color="blue">
            选中文本可拖拽
          </Tag>
          <Tag icon={<SelectOutlined />} color="green">
            点击行号选择整行
          </Tag>
          
          {selectedText && (
            <Tag color="purple" title={selectedText}>
              已选择: {selectedText.substring(0, 30)}
              {selectedText.length > 30 ? '...' : ''}
            </Tag>
          )}
          
          <Button 
            size="small"
            icon={<RobotOutlined />}
            onClick={selectSuggestedLines}
            disabled={!suggestions || suggestions.length === 0}
          >
            选择AI建议 ({suggestions ? suggestions.length : 0})
          </Button>
          
          <Button 
            size="small"
            icon={<ClearOutlined />}
            onClick={clearSelection}
            disabled={highlightedLines.size === 0 && !selectedText}
          >
            清除选择
          </Button>
        </Space>
      </div>
      
      <div 
        ref={logViewerRef}
        className={styles.logContent}
        onDoubleClick={handleDoubleClick}
      >
        {lines.map(line => {
          const suggestion = getSuggestionForLine(line.number);
          
          return (
            <div
              key={line.number}
              className={getLineClassName(line)}
              onClick={(e) => handleLineClick(line.number, e)}
            >
              <span className={styles.lineNumber}>
                {line.number.toString().padStart(4, ' ')}
              </span>
              
              <span 
                className={styles.lineContent}
                draggable="true"
                onDragStart={(e) => {
                  const text = selectedText || line.content;
                  e.dataTransfer.setData('text/plain', text);
                  e.dataTransfer.effectAllowed = 'copy';
                  
                  // 创建拖拽预览
                  const dragPreview = document.createElement('div');
                  dragPreview.style.cssText = `
                    background: #1890ff;
                    color: white;
                    padding: 6px 12px;
                    border-radius: 4px;
                    font-size: 12px;
                    position: absolute;
                    top: -1000px;
                    left: -1000px;
                    white-space: nowrap;
                    max-width: 300px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                  `;
                  dragPreview.innerHTML = `📊 ${text.substring(0, 50)}${text.length > 50 ? '...' : ''}`;
                  document.body.appendChild(dragPreview);
                  
                  e.dataTransfer.setDragImage(dragPreview, 10, 10);
                  
                  // 清理拖拽预览
                  setTimeout(() => {
                    if (document.body.contains(dragPreview)) {
                      document.body.removeChild(dragPreview);
                    }
                  }, 100);
                  
                  message.info('拖拽到右侧指标面板以添加指标');
                }}
              >
                {line.content}
              </span>
              
              {suggestion && (
                <div className={styles.suggestionTip}>
                  <Tooltip 
                    title={`AI建议: ${suggestion.label} = ${suggestion.value} ${suggestion.unit} (置信度: ${Math.round(suggestion.confidence * 100)}%)`}
                  >
                    <Tag 
                      size="small" 
                      color="purple"
                      style={{ marginLeft: '8px' }}
                    >
                      🤖 {suggestion.label}
                    </Tag>
                  </Tooltip>
                </div>
              )}
            </div>
          );
        })}
      </div>
      
      {lines.length === 0 && (
        <div className={styles.emptyState}>
          <EyeOutlined style={{ fontSize: '48px', color: '#ccc' }} />
          <div style={{ marginTop: '16px', color: '#999' }}>
            暂无日志内容
          </div>
        </div>
      )}
    </div>
  );
}

export default LogViewer; 