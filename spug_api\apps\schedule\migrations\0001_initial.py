# Generated by Django 2.2.28 on 2025-07-02 16:52

from django.db import migrations, models
import django.db.models.deletion
import libs.mixins
import libs.utils


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('account', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='History',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_id', models.IntegerField()),
                ('status', models.SmallIntegerField(choices=[(0, '执行中'), (1, '成功'), (2, '失败')])),
                ('run_time', models.CharField(max_length=20)),
                ('output', models.TextField()),
            ],
            options={
                'db_table': 'task_histories',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('type', models.CharField(max_length=50)),
                ('interpreter', models.CharField(default='sh', max_length=20)),
                ('command', models.TextField()),
                ('targets', models.TextField()),
                ('trigger', models.CharField(choices=[('date', '一次性'), ('calendarinterval', '日历间隔'), ('cron', 'UNIX cron'), ('interval', '普通间隔')], max_length=20)),
                ('trigger_args', models.CharField(max_length=255)),
                ('is_active', models.BooleanField(default=False)),
                ('desc', models.CharField(max_length=255, null=True)),
                ('rst_notify', models.CharField(max_length=255, null=True)),
                ('created_at', models.CharField(default=libs.utils.human_datetime, max_length=20)),
                ('updated_at', models.CharField(max_length=20, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='+', to='account.User')),
                ('latest', models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='schedule.History')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='account.User')),
            ],
            options={
                'db_table': 'tasks',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
