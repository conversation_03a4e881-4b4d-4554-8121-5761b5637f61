.logExtractionConfig {
  .modalTitle {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }

  .configContent {
    .dockerConfig {
      background: #fafafa;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 16px;

      .dockerSwitch {
        display: flex;
        align-items: center;
        gap: 12px;

        .dockerLabel {
          color: #595959;
          font-size: 14px;
        }
      }
    }

    .fileListContainer {
      background: #f6f8fa;
      border: 1px solid #e1e4e8;
      border-radius: 6px;
      margin-top: 16px;
      max-height: 400px;
      overflow: hidden;

      .fileListHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #fff;
        border-bottom: 1px solid #e1e4e8;
        font-weight: 500;
        color: #262626;

        .selectAllContainer {
          display: flex;
          align-items: center;
          gap: 8px;

          .ant-checkbox-wrapper {
            font-weight: 500;
            color: #1890ff;

            &:hover {
              color: #40a9ff;
            }
          }
        }
      }

      .fileListContent {
        max-height: 300px;
        overflow-y: auto;
        background: #fff;

        .fileItem {
          padding: 8px 16px;
          border-bottom: 1px solid #f0f0f0;

          &:hover {
            background: #f5f5f5;
          }

          .fileItemContent {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            width: 100%;

            .fileInfo {
              flex: 1;
              min-width: 0;

              .fileName {
                font-weight: 500;
                color: #262626;
                margin-bottom: 4px;
              }

              .fileDetails {
                display: flex;
                align-items: center;
                gap: 8px;

                .filePath {
                  color: #666;
                  font-size: 12px;
                  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
            }
          }
        }

        .loadingContainer {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
          color: #666;
        }
      }
    }

    .previewCard {
      margin-top: 16px;

      .previewContent {
        background: #f6f8fa;
        border: 1px solid #e1e4e8;
        border-radius: 4px;
        padding: 12px;
        font-size: 12px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        max-height: 200px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-break: break-all;
        margin: 0;
      }
    }

    .pathInput {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;

      .pathField {
        flex: 1;
      }

      .removeBtn {
        color: #ff4d4f;
        border-color: #ff4d4f;

        &:hover {
          background: #fff2f0;
          border-color: #ff7875;
          color: #ff7875;
        }
      }
    }

    .addPathBtn {
      width: 100%;
      border-style: dashed;
      color: #722ed1;
      border-color: #722ed1;

      &:hover {
        background: #f9f0ff;
        border-color: #9254de;
        color: #9254de;
      }
    }

    :global(.ant-divider-horizontal.ant-divider-with-text-left) {
      margin: 16px 0;
      
      .ant-divider-inner-text {
        font-weight: 500;
        color: #262626;
      }
    }

    :global(.ant-form-item-label > label) {
      font-weight: 500;
      color: #262626;
    }

    :global(.ant-select-selection-item) {
      display: flex;
      align-items: center;
    }

    :global(.ant-input-affix-wrapper) {
      border-radius: 6px;
    }

    :global(.ant-select-selector) {
      border-radius: 6px;
    }

    :global(.ant-input) {
      border-radius: 6px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .logExtractionConfig {
    .configContent {
      .pathInput {
        flex-direction: column;
        align-items: stretch;

        .pathField {
          margin-bottom: 8px;
        }
      }
    }
  }
}
