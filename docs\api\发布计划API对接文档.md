# 发布计划功能 API 对接文档

## 概述
本文档描述了发布计划功能的前后端API接口规范，包括发布计划管理、模型测试任务管理、风险预警和甘特图数据接口。

## 基础信息
- 基础URL: `/api/model-storage/`
- 认证方式: 基于现有spug系统的认证机制
- 数据格式: JSON
- 字符编码: UTF-8

## 1. 发布计划管理 API

### 1.1 获取发布计划列表
```
GET /api/model-storage/release-plans/
```

**响应示例:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "2024年6月模型发布计划",
      "description": "包含BERT、GPT等多个模型的测试计划",
      "start_date": "2024-06-01",
      "end_date": "2024-06-30",
      "status": "running",
      "status_display": "进行中",
      "risk_level": "medium",
      "risk_level_display": "中风险",
      "created_by": "管理员",
      "created_at": "2024-06-01 10:00:00",
      "updated_at": "2024-06-15 14:30:00",
      "task_count": 10,
      "completed_tasks": 3
    }
  ],
  "error": ""
}
```

### 1.2 创建发布计划
```
POST /api/model-storage/release-plans/
```

**请求参数:**
```json
{
  "name": "2024年7月模型发布计划",
  "description": "下个月的模型测试计划",
  "start_date": "2024-07-01",
  "end_date": "2024-07-31",
  "risk_level": "low",
  "created_by": "管理员"
}
```

### 1.3 更新发布计划
```
PUT /api/model-storage/release-plans/
```

**请求参数:**
```json
{
  "id": 1,
  "name": "2024年6月模型发布计划（更新）",
  "description": "更新后的描述",
  "start_date": "2024-06-01",
  "end_date": "2024-06-30",
  "status": "completed",
  "risk_level": "low"
}
```

### 1.4 删除发布计划
```
DELETE /api/model-storage/release-plans/
```

**请求参数:**
```json
{
  "id": 1
}
```

## 2. 模型测试任务管理 API

### 2.1 获取测试任务列表
```
GET /api/model-storage/test-tasks/?release_plan_id=1
```

**响应示例:**
```json
{
  "data": [
    {
      "id": 1,
      "release_plan_id": 1,
      "release_plan_name": "2024年6月模型发布计划",
      "model_name": "BERT-base-chinese",
      "model_type": "inference",
      "model_type_display": "推理",
      "framework": "pytorch",
      "framework_display": "PyTorch",
      "start_date": "2024-06-01",
      "end_date": "2024-06-05",
      "priority": "high",
      "priority_display": "高",
      "test_status": "running",
      "test_status_display": "测试中",
      "tester": "张三",
      "provider_status": "provided",
      "provider_status_display": "已提供",
      "progress": 60,
      "notes": "模型性能良好",
      "model_version": "v1.0",
      "model_size": "110MB",
      "gpu_requirements": "1x V100",
      "memory_requirements": "8GB",
      "test_dataset": "CLUE数据集",
      "accuracy_target": 0.85,
      "actual_accuracy": 0.87,
      "inference_speed": "50ms/sample",
      "actual_start_date": "2024-06-01",
      "actual_end_date": null,
      "estimated_hours": 40,
      "actual_hours": 24,
      "created_at": "2024-06-01 09:00:00",
      "updated_at": "2024-06-03 16:30:00",
      "color": "#1890ff"
    }
  ],
  "error": ""
}
```

### 2.2 创建测试任务
```
POST /api/model-storage/test-tasks/
```

**请求参数:**
```json
{
  "release_plan_id": 1,
  "model_name": "GPT-3.5-turbo",
  "model_type": "inference",
  "framework": "pytorch",
  "start_date": "2024-06-10",
  "end_date": "2024-06-15",
  "priority": "high",
  "tester": "李四",
  "provider_status": "waiting",
  "notes": "需要大内存GPU",
  "model_version": "v1.0",
  "gpu_requirements": "2x A100",
  "memory_requirements": "40GB",
  "estimated_hours": 60
}
```

### 2.3 更新测试任务
```
PUT /api/model-storage/test-tasks/
```

### 2.4 删除测试任务
```
DELETE /api/model-storage/test-tasks/
```

## 3. 风险预警管理 API

### 3.1 获取风险预警列表
```
GET /api/model-storage/risk-alerts/?release_plan_id=1
```

**响应示例:**
```json
{
  "data": [
    {
      "id": 1,
      "release_plan_id": 1,
      "title": "GPU资源不足",
      "description": "当前GPU资源可能无法满足所有模型的并行测试需求",
      "risk_type": "resource",
      "risk_type_display": "资源风险",
      "severity": "high",
      "severity_display": "高",
      "status": "active",
      "status_display": "活跃",
      "affected_tasks": [1, 3, 5],
      "mitigation_plan": "申请额外GPU资源或调整任务优先级",
      "reporter": "张三",
      "assignee": "项目经理",
      "due_date": "2024-06-20",
      "resolved_at": null,
      "created_at": "2024-06-10 14:00:00",
      "updated_at": "2024-06-10 14:00:00"
    }
  ],
  "error": ""
}
```

### 3.2 创建风险预警
```
POST /api/model-storage/risk-alerts/
```

### 3.3 更新风险预警
```
PUT /api/model-storage/risk-alerts/
```

### 3.4 删除风险预警
```
DELETE /api/model-storage/risk-alerts/
```

## 4. 甘特图数据 API

### 4.1 获取甘特图数据
```
GET /api/model-storage/gantt-data/?release_plan_id=1
```

**响应示例:**
```json
{
  "data": {
    "release_plan": {
      "id": 1,
      "name": "2024年6月模型发布计划",
      "start_date": "2024-06-01",
      "end_date": "2024-06-30",
      "status": "running"
    },
    "tasks_by_tester": {
      "张三": [
        {
          "id": 1,
          "model_name": "BERT-base-chinese",
          "model_type_display": "推理",
          "framework_display": "PyTorch",
          "start_date": "2024-06-01",
          "end_date": "2024-06-05",
          "priority_display": "高",
          "test_status": "running",
          "test_status_display": "测试中",
          "provider_status_display": "已提供",
          "progress": 60,
          "notes": "模型性能良好",
          "color": "#1890ff"
        }
      ],
      "李四": [...],
      "王五": [...]
    },
    "risk_alerts": [
      {
        "title": "GPU资源不足",
        "severity": "high",
        "severity_display": "高",
        "affected_tasks": [1, 3, 5]
      }
    ]
  },
  "error": ""
}
```

## 5. 统计数据 API

### 5.1 获取统计数据
```
GET /api/model-storage/statistics/?release_plan_id=1
```

**响应示例:**
```json
{
  "data": {
    "total_tasks": 10,
    "completed_tasks": 3,
    "running_tasks": 4,
    "pending_tasks": 3,
    "completion_rate": 0.3,
    "average_progress": 45.5,
    "high_priority_tasks": 4,
    "risk_count": {
      "high": 1,
      "medium": 2,
      "low": 0
    },
    "tester_workload": {
      "张三": 3,
      "李四": 4,
      "王五": 3
    }
  },
  "error": ""
}
```

## 6. 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 7. 错误响应格式

```json
{
  "data": null,
  "error": "错误信息描述"
}
```

## 8. 枚举值说明

### 8.1 发布计划状态
- `planning`: 计划中
- `running`: 进行中
- `completed`: 已完成
- `cancelled`: 已取消
- `paused`: 已暂停

### 8.2 风险等级
- `low`: 低风险
- `medium`: 中风险
- `high`: 高风险

### 8.3 模型类型
- `inference`: 推理
- `training`: 训练
- `fine_tuning`: 微调
- `evaluation`: 评估
- `optimization`: 优化

### 8.4 框架类型
- `pytorch`: PyTorch
- `tensorflow`: TensorFlow
- `onnx`: ONNX
- `keras`: Keras
- `paddle`: PaddlePaddle
- `mindspore`: MindSpore
- `other`: 其他

### 8.5 优先级
- `low`: 低
- `medium`: 中
- `high`: 高
- `urgent`: 紧急

### 8.6 测试状态
- `pending`: 待测试
- `running`: 测试中
- `completed`: 已完成
- `failed`: 测试失败
- `blocked`: 阻塞
- `cancelled`: 已取消

### 8.7 模型提供状态
- `waiting`: 等待中
- `provided`: 已提供
- `partial`: 部分提供
- `rejected`: 已拒绝

## 9. 前端集成说明

### 9.1 请求头设置
```javascript
headers: {
  'Content-Type': 'application/json',
  'X-Token': localStorage.getItem('token') // 使用现有spug认证token
}
```

### 9.2 错误处理
前端需要统一处理API返回的错误信息，当`error`字段不为空时，显示错误提示。

### 9.3 日期格式
- 前端发送: `YYYY-MM-DD` 格式
- 后端返回: `YYYY-MM-DD` 或 `YYYY-MM-DD HH:mm:ss` 格式

### 9.4 权限控制
所有API接口都需要相应的权限，前端需要根据用户权限显示/隐藏相关功能按钮。