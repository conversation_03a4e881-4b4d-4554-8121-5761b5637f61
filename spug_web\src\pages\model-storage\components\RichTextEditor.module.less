/* 富文本编辑器样式 */
.richTextEditor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  background: white;
  
  &:hover {
    border-color: #667eea;
  }
  
  &:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  }
}

.toolbar {
  padding: 8px 12px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  
  :global(.ant-btn) {
    border: none;
    box-shadow: none;
    
    &:hover {
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
    }
    
    &:focus {
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
    }
  }
  
  :global(.ant-select) {
    .ant-select-selector {
      border: 1px solid #d9d9d9;
      
      &:hover {
        border-color: #667eea;
      }
    }
    
    &.ant-select-focused .ant-select-selector {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }
  }
  
  :global(.ant-divider-vertical) {
    height: 20px;
    margin: 0 8px;
    border-color: #e8e8e8;
  }
}

.colorPicker {
  position: relative;
  display: inline-block;
  
  &:hover .colorPanel {
    display: block;
  }
}

.colorPanel {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 4px;
  width: 120px;
}

.colorItem {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  cursor: pointer;
  border: 1px solid #e8e8e8;
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

.editor {
  padding: 12px;
  min-height: 200px;
  outline: none;
  font-size: 14px;
  line-height: 1.6;
  color: #262626;
  overflow-y: auto;
  
  &:empty::before {
    content: attr(data-placeholder);
    color: #bfbfbf;
    font-style: italic;
  }
  
  /* 编辑器内容样式 */
  p {
    margin: 0 0 8px 0;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  h1, h2, h3, h4, h5, h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  h1 { font-size: 24px; }
  h2 { font-size: 20px; }
  h3 { font-size: 18px; }
  h4 { font-size: 16px; }
  h5 { font-size: 14px; }
  h6 { font-size: 12px; }
  
  ul, ol {
    margin: 8px 0;
    padding-left: 24px;
    
    li {
      margin: 4px 0;
    }
  }
  
  blockquote {
    margin: 16px 0;
    padding: 12px 16px;
    background: #f6f8fa;
    border-left: 4px solid #667eea;
    color: #666;
    font-style: italic;
  }
  
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
    
    th, td {
      border: 1px solid #d9d9d9;
      padding: 8px 12px;
      text-align: left;
    }
    
    th {
      background: #fafafa;
      font-weight: 600;
    }
    
    tr:nth-child(even) {
      background: #fafafa;
    }
  }
  
  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 8px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  a {
    color: #667eea;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  code {
    background: #f6f8fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
  }
  
  pre {
    background: #f6f8fa;
    padding: 12px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 16px 0;
    
    code {
      background: none;
      padding: 0;
    }
  }
  
  hr {
    border: none;
    border-top: 1px solid #e8e8e8;
    margin: 24px 0;
  }
  
  /* 选中文本样式 */
  ::selection {
    background: rgba(102, 126, 234, 0.2);
  }
  
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    padding: 6px 8px;
    
    :global(.ant-space) {
      flex-wrap: wrap;
      
      .ant-space-item {
        margin-bottom: 4px;
      }
    }
    
    :global(.ant-btn) {
      padding: 4px 6px;
      font-size: 12px;
    }
    
    :global(.ant-select) {
      min-width: 60px;
    }
  }
  
  .editor {
    padding: 8px;
    font-size: 13px;
  }
  
  .colorPanel {
    width: 100px;
    grid-template-columns: repeat(4, 1fr);
  }
  
  .colorItem {
    width: 18px;
    height: 18px;
  }
}

/* 打印样式 */
@media print {
  .toolbar {
    display: none;
  }
  
  .richTextEditor {
    border: none;
  }
  
  .editor {
    padding: 0;
    
    table {
      page-break-inside: avoid;
    }
    
    img {
      max-width: 100%;
      page-break-inside: avoid;
    }
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .richTextEditor {
    background: #1f1f1f;
    border-color: #434343;
  }
  
  .toolbar {
    background: #2d2d2d;
    border-bottom-color: #434343;
  }
  
  .editor {
    color: #e8e8e8;
    
    &:empty::before {
      color: #8c8c8c;
    }
    
    table {
      th, td {
        border-color: #434343;
      }
      
      th {
        background: #2d2d2d;
      }
      
      tr:nth-child(even) {
        background: #2d2d2d;
      }
    }
    
    blockquote {
      background: #2d2d2d;
      color: #b3b3b3;
    }
    
    code, pre {
      background: #2d2d2d;
    }
    
    hr {
      border-top-color: #434343;
    }
  }
  
  .colorPanel {
    background: #2d2d2d;
    border-color: #434343;
  }
}
