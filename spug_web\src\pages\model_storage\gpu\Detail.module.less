/* GPU详情页面样式 */
.gpuDetailDrawer {
  :global(.ant-drawer-content) {
    background: #f5f7fa;
  }
  
  :global(.ant-drawer-header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    
    .ant-drawer-title {
      color: white;
    }
    
    .ant-drawer-close {
      color: white;
      
      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
  
  :global(.ant-drawer-body) {
    padding: 24px;
    background: #f5f7fa;
  }
}

.drawerTitle {
  font-size: 18px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
}

/* 信息卡片样式 */
.infoCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  
  :global(.ant-card-head) {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 12px 12px 0 0;
    
    .ant-card-head-title {
      font-weight: 600;
      color: #1f2937;
    }
  }
  
  :global(.ant-card-body) {
    padding: 24px;
  }
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.infoLabel {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.infoValue {
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
}

.description {
  font-size: 14px;
  color: #374151;
  line-height: 1.6;
  background: #f8fafc;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  margin-top: 8px;
}

/* 模型卡片样式 */
.modelCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  
  :global(.ant-card-head) {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 12px 12px 0 0;
    
    .ant-card-head-title {
      font-weight: 600;
      color: #1f2937;
    }
  }
  
  :global(.ant-card-body) {
    padding: 24px;
  }
}

.modelSection {
  h4 {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    
    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 2px;
      margin-right: 8px;
    }
  }
}

.modelTags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 32px;
  align-items: flex-start;
}

.manualModelTag {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  padding: 4px 12px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  :global(.ant-tag-close-icon) {
    color: rgba(255, 255, 255, 0.8);
    margin-left: 6px;

    &:hover {
      color: white;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
    }
  }
}

.autoModelTag {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  padding: 4px 12px;
}

.emptyText {
  color: #9ca3af;
  font-style: italic;
  font-size: 14px;
}

/* 模型编辑相关样式 */
.modelTagWrapper {
  display: inline-block;
  margin: 4px 8px 4px 0;
}

.modelTagDisplay {
  display: flex;
  align-items: center;
  gap: 4px;

  &:hover .modelActions {
    opacity: 1;
  }
}

.modelActions {
  display: flex;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  margin-left: 4px;
}

.editingTag {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #f8fafc;
  padding: 4px 8px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.addingModelWrapper {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #f8fafc;
  padding: 4px 8px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  margin-top: 8px;
}

.modelEditInput {
  border-radius: 6px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;

  &:hover {
    border-color: #667eea;
  }

  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
  }
}

/* 统计卡片样式 */
.statsCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }
  
  :global(.ant-card-body) {
    padding: 20px;
    text-align: center;
  }
  
  :global(.ant-statistic-title) {
    color: #6b7280;
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  :global(.ant-statistic-content) {
    font-size: 24px;
    font-weight: bold;
  }
}

/* 任务卡片样式 */
.taskCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  
  :global(.ant-card-head) {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 12px 12px 0 0;
    
    .ant-card-head-title {
      font-weight: 600;
      color: #1f2937;
    }
  }
  
  :global(.ant-card-body) {
    padding: 24px;
  }
}

.taskTable {
  :global(.ant-table-thead > tr > th) {
    background: linear-gradient(135deg, #f6f8fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: #1f2937;
    
    &:first-child {
      border-radius: 8px 0 0 8px;
    }
    
    &:last-child {
      border-radius: 0 8px 8px 0;
    }
  }
  
  :global(.ant-table-tbody > tr) {
    transition: all 0.3s ease;
    
    &:hover {
      background-color: rgba(24, 144, 255, 0.05);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    > td {
      border: none;
      border-bottom: 1px solid #f0f0f0;
    }
    
    &:last-child > td {
      border-bottom: none;
    }
  }
}

/* 厂商标签样式 */
.vendorTag {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  padding: 4px 12px;
}

/* 渐变按钮样式 */
.gradientButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
  
  &:hover, &:focus {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
  
  &:active {
    transform: translateY(0);
  }
}

/* 任务模态框样式 */
.taskModal {
  :global(.ant-modal-content) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.2);
  }
  
  :global(.ant-modal-header) {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 20px 24px;
  }
  
  :global(.ant-modal-body) {
    padding: 24px;
    background: #fafbfc;
  }
  
  :global(.ant-modal-footer) {
    background: #fafbfc;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    padding: 16px 24px;
    text-align: right;
  }
}

.modalTitle {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
}

.taskForm {
  :global(.ant-form-item-label) {
    > label {
      font-weight: 600;
      color: #374151;
      font-size: 14px;
    }
  }
  
  :global(.ant-input), :global(.ant-select-selector) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #667eea;
    }
    
    &:focus, &.ant-input-focused, &.ant-select-focused {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .gpuDetailDrawer {
    :global(.ant-drawer) {
      width: 100vw !important;
    }
    
    :global(.ant-drawer-body) {
      padding: 16px;
    }
  }
  
  .infoCard, .modelCard, .taskCard {
    :global(.ant-card-body) {
      padding: 16px;
    }
  }
  
  .statsCard {
    :global(.ant-card-body) {
      padding: 16px;
    }
  }
}
