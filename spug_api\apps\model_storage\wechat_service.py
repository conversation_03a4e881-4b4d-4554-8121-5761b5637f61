# -*- coding: utf-8 -*-
"""
企业微信消息发送服务
用于模型测试任务分配的企业微信通知
"""

import requests
import json
import logging
from django.utils import timezone
from libs.utils import human_datetime
from apps.account.models import User
from apps.alarm.models import Contact

logger = logging.getLogger(__name__)


class WeChatNotificationService:
    """企业微信通知服务"""
    
    @staticmethod
    def send_task_assignment_notification(task_data, assignee_name, assigned_by, message_content=""):
        """
        发送任务分配通知
        
        Args:
            task_data: 任务数据字典
            assignee_name: 被分配人姓名
            assigned_by: 分配人
            message_content: 分配说明
        """
        try:
            # 查找被分配人的企业微信配置
            assignee_user = User.objects.filter(nickname=assignee_name, is_active=True).first()
            if not assignee_user:
                logger.warning(f"未找到用户: {assignee_name}")
                return False, "未找到指定用户"
            
            # 查找用户的企业微信联系方式
            contact = Contact.objects.filter(name=assignee_name).first()
            if not contact or not contact.qy_wx:
                logger.warning(f"用户 {assignee_name} 未配置企业微信")
                return False, f"用户 {assignee_name} 未配置企业微信"
            
            # 使用消息模板构建消息内容
            from .message_templates import get_task_assignment_message
            message_content_text = get_task_assignment_message(
                task_data, assignee_name, assigned_by, message_content
            )

            message_data = {
                'msgtype': 'markdown',
                'markdown': {
                    'content': message_content_text
                }
            }
            
            # 发送消息
            success = WeChatNotificationService._send_wechat_message(contact.qy_wx, message_data)
            
            if success:
                logger.info(f"任务分配通知发送成功: {assignee_name}")
                return True, "通知发送成功"
            else:
                logger.error(f"任务分配通知发送失败: {assignee_name}")
                return False, "通知发送失败"
                
        except Exception as e:
            logger.error(f"发送任务分配通知异常: {str(e)}")
            return False, f"发送通知异常: {str(e)}"
    
    @staticmethod
    def _build_task_assignment_message(task_data, assignee_name, assigned_by, message_content):
        """构建任务分配消息内容"""
        
        # 任务状态映射
        status_map = {
            'pending': '待开始',
            'in_progress': '进行中', 
            'completed': '已完成',
            'cancelled': '已取消',
            'blocked': '阻塞中'
        }
        
        # 优先级映射
        priority_map = {
            'p1': 'P1-高',
            'p2': 'P2-中',
            'p3': 'P3-低'
        }
        
        # 构建消息文本
        texts = [
            "## 📋 模型测试任务分配通知",
            "",
            f"**👤 分配给：** <font color=\"info\">{assignee_name}</font>",
            f"**👨‍💼 分配人：** {assigned_by}",
            f"**📅 分配时间：** {human_datetime()}",
            "",
            "### 📊 任务详情",
            f"**🤖 模型名称：** {task_data.get('model_name', 'N/A')}",
            f"**🔧 模型类型：** {task_data.get('model_type', 'N/A')}",
            f"**💻 GPU型号：** {task_data.get('gpu_model', 'N/A')}",
            f"**📈 当前进度：** {task_data.get('progress', 0)}%",
            f"**⚡ 优先级：** {priority_map.get(task_data.get('priority', 'p2'), 'P2-中')}",
            f"**📊 任务状态：** {status_map.get(task_data.get('test_status', 'pending'), '待开始')}",
            "",
            "### 📅 时间安排",
            f"**🚀 开始时间：** {task_data.get('start_date', 'N/A')}",
            f"**🏁 结束时间：** {task_data.get('end_date', 'N/A')}",
        ]
        
        # 添加分配说明
        if message_content:
            texts.extend([
                "",
                "### 💬 分配说明",
                f"{message_content}"
            ])
        
        # 添加操作提示
        texts.extend([
            "",
            "### 🔗 操作提示",
            "• 请登录 **Spug运维平台** 查看任务详情",
            "• 如有疑问请及时与分配人沟通",
            "• 请按时完成任务并更新进度",
            "",
            "> 📱 来自 **Spug模型测试管理平台**"
        ])
        
        return {
            'msgtype': 'markdown',
            'markdown': {
                'content': '\n'.join(texts)
            }
        }
    
    @staticmethod
    def send_task_progress_notification(task_data, progress_info):
        """
        发送任务进度更新通知
        
        Args:
            task_data: 任务数据
            progress_info: 进度信息
        """
        try:
            # 查找任务负责人的企业微信配置
            tester_name = task_data.get('tester')
            contact = Contact.objects.filter(name=tester_name).first()
            
            if not contact or not contact.qy_wx:
                return False, f"用户 {tester_name} 未配置企业微信"
            
            # 使用消息模板构建进度通知消息
            from .message_templates import get_task_progress_message
            message_content_text = get_task_progress_message(task_data, progress_info)

            message_data = {
                'msgtype': 'markdown',
                'markdown': {
                    'content': message_content_text
                }
            }
            
            # 发送消息
            success = WeChatNotificationService._send_wechat_message(contact.qy_wx, message_data)
            
            return success, "进度通知发送成功" if success else "进度通知发送失败"
            
        except Exception as e:
            logger.error(f"发送进度通知异常: {str(e)}")
            return False, f"发送进度通知异常: {str(e)}"
    
    @staticmethod
    def _build_progress_message(task_data, progress_info):
        """构建进度更新消息"""
        texts = [
            "## 📈 任务进度更新通知",
            "",
            f"**🤖 模型名称：** {task_data.get('model_name', 'N/A')}",
            f"**👤 负责人：** {task_data.get('tester', 'N/A')}",
            f"**📊 当前进度：** {progress_info.get('current_progress', 0)}%",
            f"**📅 更新时间：** {human_datetime()}",
            "",
            f"**📝 进度说明：** {progress_info.get('description', '无')}",
            "",
            "> 📱 来自 **Spug模型测试管理平台**"
        ]
        
        return {
            'msgtype': 'markdown',
            'markdown': {
                'content': '\n'.join(texts)
            }
        }
    
    @staticmethod
    def _send_wechat_message(webhook_url, message_data):
        """
        发送企业微信消息
        
        Args:
            webhook_url: 企业微信机器人webhook地址
            message_data: 消息数据
            
        Returns:
            bool: 发送是否成功
        """
        try:
            response = requests.post(
                webhook_url,
                json=message_data,
                timeout=15,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    return True
                else:
                    logger.error(f"企业微信API返回错误: {result}")
                    return False
            else:
                logger.error(f"企业微信API请求失败: {response.status_code}")
                return False
                
        except requests.exceptions.Timeout:
            logger.error("企业微信消息发送超时")
            return False
        except requests.exceptions.RequestException as e:
            logger.error(f"企业微信消息发送网络异常: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"企业微信消息发送异常: {str(e)}")
            return False


def send_task_assignment_wechat(task_data, assignee_name, assigned_by, message_content=""):
    """
    便捷函数：发送任务分配企业微信通知
    """
    return WeChatNotificationService.send_task_assignment_notification(
        task_data, assignee_name, assigned_by, message_content
    )


def send_task_progress_wechat(task_data, progress_info):
    """
    便捷函数：发送任务进度企业微信通知
    """
    return WeChatNotificationService.send_task_progress_notification(task_data, progress_info)
