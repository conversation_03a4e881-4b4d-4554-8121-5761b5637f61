# Generated by Django 2.2.28 on 2025-07-16 17:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('exec', '0004_remove_progress_fields'),
    ]

    operations = [
        migrations.AddField(
            model_name='testresult',
            name='log_extraction_method',
            field=models.CharField(blank=True, choices=[('remote', '远程提取'), ('upload', '文件上传'), ('paste', '粘贴输入')], help_text='日志提取方式', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='testresult',
            name='log_source_host',
            field=models.CharField(blank=True, help_text='日志来源主机', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='testresult',
            name='log_source_path',
            field=models.CharField(blank=True, help_text='日志文件路径', max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='testresult',
            name='source_type',
            field=models.CharField(choices=[('execution', '测试计划执行'), ('log_extraction', '日志提取'), ('manual', '手动录入')], default='execution', help_text='数据来源类型', max_length=20),
        ),
        migrations.AlterField(
            model_name='testresult',
            name='execution',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='test_results', to='exec.TestPlanExecution'),
        ),
    ]
