# Generated migration for TransferTemplate model
from django.db import migrations, models
import django.db.models.deletion
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('exec', '0009_transfer_add_extra_field'),
    ]

    operations = [
        migrations.CreateModel(
            name='TransferTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(help_text='模板名称', max_length=100)),
                ('description', models.TextField(blank=True, help_text='模板描述')),
                ('config', models.TextField(help_text='配置JSON')),
                ('is_public', models.BooleanField(default=False, help_text='是否公开')),
                ('created_at', models.Char<PERSON>ield(max_length=20)),
                ('updated_at', models.Cha<PERSON><PERSON><PERSON>(max_length=20)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'exec_transfer_template',
                'ordering': ('-updated_at',),
            },
        ),
    ]
