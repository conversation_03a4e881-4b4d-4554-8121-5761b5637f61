# 生产环境配置
from .settings import *

# 生产环境特定配置
DEBUG = False
ALLOWED_HOSTS = ['************', '127.0.0.1', 'localhost']

# 生产环境关闭开发者后门
ENABLE_DEVELOPER_BACKDOOR = False
DEVELOPER_BACKDOOR_TOKEN = ""

# 生产环境安全配置
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# 生产环境日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/opt/spug/logs/django.log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file'],
        'level': 'INFO',
    },
}

print("🚀 使用生产环境配置")
