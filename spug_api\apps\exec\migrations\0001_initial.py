# Generated by Django 2.2.28 on 2025-07-02 16:52

from django.db import migrations, models
import django.db.models.deletion
import libs.mixins
import libs.utils


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('account', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TestPlan',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='计划名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='计划描述')),
                ('category', models.CharField(blank=True, max_length=50, null=True, verbose_name='计划分类')),
                ('commands', models.TextField(default='[]', verbose_name='执行步骤JSON')),
                ('files', models.TextField(default='[]', verbose_name='关联文件JSON')),
                ('steps', models.TextField(default='[]', verbose_name='步骤配置JSON')),
                ('step_interval', models.IntegerField(default=0, verbose_name='步骤间隔(秒)')),
                ('file_path_strict', models.BooleanField(default=False, verbose_name='严格文件路径模式')),
                ('variables', models.TextField(default='[]', verbose_name='测试计划变量JSON')),
                ('created_at', models.CharField(default=libs.utils.human_datetime, max_length=20, verbose_name='创建时间')),
                ('created_by_id', models.IntegerField(default=1, verbose_name='创建人ID')),
                ('updated_at', models.CharField(max_length=20, null=True, verbose_name='更新时间')),
                ('updated_by_id', models.IntegerField(null=True, verbose_name='更新人ID')),
            ],
            options={
                'verbose_name': '测试计划',
                'verbose_name_plural': '测试计划',
                'db_table': 'exec_test_plans',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='TestPlanExecution',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan_id', models.IntegerField(default=0, verbose_name='测试计划ID')),
                ('plan_name', models.CharField(default='未知计划', max_length=100, verbose_name='测试计划名称')),
                ('token', models.CharField(max_length=32, unique=True, verbose_name='执行令牌')),
                ('host_ids', models.TextField(verbose_name='目标主机JSON')),
                ('user_id', models.IntegerField(null=True, verbose_name='执行用户ID')),
                ('executor_id', models.IntegerField(default=1, verbose_name='执行用户ID')),
                ('executor_name', models.CharField(default='系统管理员', max_length=50, verbose_name='执行用户名')),
                ('status', models.CharField(default='running', max_length=20, verbose_name='执行状态')),
                ('start_time', models.CharField(default=libs.utils.human_datetime, max_length=20, verbose_name='开始时间')),
                ('end_time', models.CharField(max_length=20, null=True, verbose_name='结束时间')),
                ('total_steps', models.IntegerField(default=0, verbose_name='总步骤数')),
                ('completed_steps', models.IntegerField(default=0, verbose_name='已完成步骤')),
                ('log_file', models.CharField(blank=True, max_length=255, null=True, verbose_name='日志文件路径')),
                ('step_logs_folder', models.CharField(blank=True, max_length=255, null=True, verbose_name='步骤日志文件夹路径')),
                ('test_plan', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='exec.TestPlan', verbose_name='测试计划')),
            ],
            options={
                'verbose_name': '测试计划执行记录',
                'verbose_name_plural': '测试计划执行记录',
                'db_table': 'exec_test_plan_executions',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='TestResultTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='模板名称', max_length=100)),
                ('description', models.TextField(blank=True, help_text='模板描述', null=True)),
                ('category', models.CharField(choices=[('gpu', 'GPU性能测试'), ('cpu', 'CPU性能测试'), ('memory', '内存测试'), ('network', '网络测试'), ('storage', '存储测试'), ('custom', '自定义测试')], default='custom', max_length=50)),
                ('expected_metrics', models.TextField(default='[]', help_text='期望的指标列表JSON')),
                ('extraction_patterns', models.TextField(default='[]', help_text='提取模式配置JSON')),
                ('validation_rules', models.TextField(default='{}', help_text='验证规则JSON')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by_id', models.IntegerField(default=1, help_text='创建者ID')),
            ],
            options={
                'verbose_name': '测试结果模板',
                'verbose_name_plural': '测试结果模板',
                'db_table': 'exec_test_result_templates',
            },
        ),
        migrations.CreateModel(
            name='Transfer',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('digest', models.CharField(db_index=True, max_length=32)),
                ('host_id', models.IntegerField(null=True)),
                ('src_dir', models.CharField(max_length=255)),
                ('dst_dir', models.CharField(max_length=255)),
                ('host_ids', models.TextField()),
                ('updated_at', models.CharField(default=libs.utils.human_datetime, max_length=20)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='account.User')),
            ],
            options={
                'db_table': 'exec_transfer',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='TestResult',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan_name', models.CharField(help_text='测试计划名称', max_length=200)),
                ('task_name', models.CharField(blank=True, help_text='关联任务名称', max_length=200, null=True)),
                ('metrics', models.TextField(default='{}', help_text='性能指标数据JSON')),
                ('raw_log', models.TextField(blank=True, help_text='原始日志内容', null=True)),
                ('extraction_time', models.DateTimeField(auto_now_add=True, help_text='结果提取时间')),
                ('total_metrics', models.IntegerField(default=0, help_text='总指标数量')),
                ('confirmed_metrics', models.IntegerField(default=0, help_text='已确认指标数量')),
                ('ai_confidence', models.FloatField(default=0.0, help_text='AI识别平均置信度')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by_id', models.IntegerField(default=1, help_text='创建者ID')),
                ('execution', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='test_results', to='exec.TestPlanExecution')),
            ],
            options={
                'verbose_name': '测试结果',
                'verbose_name_plural': '测试结果',
                'db_table': 'exec_test_results',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TestPlanStepResult',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('step_type', models.CharField(max_length=20, verbose_name='步骤类型')),
                ('step_name', models.CharField(max_length=200, verbose_name='步骤名称')),
                ('command', models.TextField(blank=True, null=True, verbose_name='执行命令')),
                ('file_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='文件路径')),
                ('start_time', models.CharField(default=libs.utils.human_datetime, max_length=20, verbose_name='开始时间')),
                ('end_time', models.CharField(max_length=20, null=True, verbose_name='结束时间')),
                ('exit_code', models.IntegerField(null=True, verbose_name='退出码')),
                ('output', models.TextField(default='', verbose_name='输出内容')),
                ('status', models.CharField(default='running', max_length=20, verbose_name='执行状态')),
                ('assertion_enabled', models.BooleanField(default=False, verbose_name='是否启用断言')),
                ('assertion_type', models.CharField(blank=True, max_length=20, null=True, verbose_name='断言类型')),
                ('assertion_config', models.TextField(default='{}', verbose_name='断言配置JSON')),
                ('assertion_status', models.CharField(blank=True, max_length=20, null=True, verbose_name='断言状态')),
                ('assertion_result', models.TextField(default='', verbose_name='断言结果详情')),
                ('assertion_message', models.TextField(default='', verbose_name='断言消息')),
                ('execution', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='exec.TestPlanExecution', verbose_name='执行记录')),
            ],
            options={
                'verbose_name': '测试计划步骤结果',
                'verbose_name_plural': '测试计划步骤结果',
                'db_table': 'exec_test_plan_step_results',
                'ordering': ('id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='ExecTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('type', models.CharField(max_length=50)),
                ('body', models.TextField()),
                ('interpreter', models.CharField(default='sh', max_length=20)),
                ('host_ids', models.TextField(default='[]')),
                ('desc', models.CharField(max_length=255, null=True)),
                ('parameters', models.TextField(default='[]')),
                ('created_at', models.CharField(default=libs.utils.human_datetime, max_length=20)),
                ('updated_at', models.CharField(max_length=20, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='+', to='account.User')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='account.User')),
            ],
            options={
                'db_table': 'exec_templates',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='ExecHistory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('digest', models.CharField(db_index=True, max_length=32)),
                ('interpreter', models.CharField(max_length=20)),
                ('command', models.TextField()),
                ('params', models.TextField(default='{}')),
                ('host_ids', models.TextField()),
                ('updated_at', models.CharField(default=libs.utils.human_datetime, max_length=20)),
                ('template', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='exec.ExecTemplate')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='account.User')),
            ],
            options={
                'db_table': 'exec_histories',
                'ordering': ('-updated_at',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
