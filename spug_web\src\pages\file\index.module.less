.fileManager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  margin-bottom: 16px;
}

.title {
  display: flex;
  align-items: center;
}

.breadcrumb {
  margin-left: 16px;
  display: flex;
  align-items: center;
}

.breadcrumbItem {
  color: #1890ff;
  cursor: pointer;
}

.breadcrumbItem:hover {
  text-decoration: underline;
}

.breadcrumbSeparator {
  margin: 0 8px;
  color: #d9d9d9;
}

.actions {
  display: flex;
  align-items: center;
}

.content {
  flex: 1;
  overflow-y: auto;
}

.fileList {
  display: flex;
  flex-wrap: wrap;
  padding: 8px;
}

.fileItem {
  width: 120px;
  height: 120px;
  margin: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
}

.fileItem:hover {
  background-color: #f5f5f5;
}

.fileIcon {
  font-size: 32px;
  margin-bottom: 8px;
}

.fileName {
  text-align: center;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

.cacheStatus {
  width: 100%;
  padding: 8px;
  margin-bottom: 8px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.uploadProgress {
  margin-top: 16px;
}

.uploadList {
  margin-top: 16px;
}

.searchInput {
  width: 200px;
  margin-right: 8px;
} 