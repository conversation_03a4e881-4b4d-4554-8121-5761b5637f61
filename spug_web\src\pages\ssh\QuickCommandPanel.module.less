.quickCommandPanel {
  height: 100%;
  
  :global(.ant-card) {
    height: 100%;
    display: flex;
    flex-direction: column;
    border: none;
    box-shadow: none;
  }
  
  :global(.ant-card-body) {
    flex: 1;
    overflow: hidden;
    padding: 6px;
    display: flex;
    flex-direction: column;
  }
  
  :global(.ant-card-head) {
    padding: 0 6px;
    min-height: 28px;
    border-bottom: 1px solid #e8e8e8;
    flex-shrink: 0;
  }
  
  :global(.ant-card-head-title) {
    font-size: 13px;
    font-weight: 500;
  }

  .filterSection {
    flex-shrink: 0;
    margin-bottom: 8px;
  }

  .commandContainer {
    flex: 1;
    overflow-y: auto;
    
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
      
      &:hover {
        background: #a1a1a1;
      }
    }
  }

  .commandGroup {
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .categoryTitle {
    font-size: 11px;
    color: #666;
    margin-bottom: 6px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0 2px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .categoryCount {
    font-size: 10px;
    color: #999;
    font-weight: normal;
    background: #f0f0f0;
    padding: 1px 6px;
    border-radius: 8px;
    margin-left: 4px;
  }

  .commandButton {
    height: 28px;
    font-size: 11px;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    margin-bottom: 4px;
    transition: all 0.2s ease;
    
    &:hover {
      opacity: 0.8;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    .commandText {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 45px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  .scriptBadge {
    background: rgba(255, 255, 255, 0.3);
    font-size: 9px;
    padding: 1px 4px;
    border-radius: 2px;
    margin-left: 2px;
    font-weight: bold;
    text-transform: uppercase;
  }

  .commandActions {
    position: absolute;
    top: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 0 4px 0 4px;
    display: flex;
    opacity: 0;
    transition: opacity 0.2s;
    
    .commandButton:hover + & {
      opacity: 1;
    }
    
    &:hover {
      opacity: 1;
    }
    
    :global(.ant-btn) {
      border: none;
      box-shadow: none;
      padding: 2px 4px;
      height: auto;
      line-height: 1;
      
      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 自定义命令卡片样式
.commandGroup:last-child {
  .commandButton {
    position: relative;
    
    &:hover {
      .commandActions {
        opacity: 1;
      }
    }
  }
}

// Tooltip样式优化
:global(.ant-tooltip-inner) {
  max-width: 300px;
  text-align: left;
}

// 脚本上传区域样式
:global(.ant-upload-drag) {
  background: #fafafa !important;
  border: 2px dashed #d9d9d9 !important;
  
  &:hover {
    border-color: #1890ff !important;
  }
}

:global(.ant-upload-drag.ant-upload-drag-hover) {
  border-color: #1890ff !important;
  background: #f0f8ff !important;
}

// 响应式设计
@media (max-width: 1200px) {
  .quickCommandPanel {
    .commandButton {
      .commandText {
        max-width: 40px;
      }
    }
  }
}

@media (max-width: 992px) {
  .quickCommandPanel {
    .commandButton {
      font-size: 10px;
      height: 26px;
      
      .commandText {
        max-width: 35px;
      }
    }
  }
}

// 命令卡片执行区域样式
.cardContainer {
  .commandCardItem {
    margin-bottom: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(114, 46, 209, 0.15);
      border-color: #722ed1;
    }
    
    :global(.ant-card-body) {
      padding: 12px;
    }
  }

  .cardButton {
    border-radius: 8px;
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);
      background-color: #8b5cf6 !important;
      border-color: #8b5cf6 !important;
    }
    
    &:active {
      transform: translateY(0);
    }
    
    :global(.ant-btn-loading) {
      transform: none;
    }
  }
} 