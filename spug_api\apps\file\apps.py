# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.

import logging
from django.apps import AppConfig

logger = logging.getLogger('apps.file')


class FileConfig(AppConfig):
    name = 'apps.file'
    verbose_name = '文件管理'
    
    def ready(self):
        # 启动远程文件夹缓存调度器
        try:
            # 简单导入以触发缓存服务的初始化
            import apps.file.remote_cache_service
            logger.info('远程文件夹缓存服务已加载')
        except ImportError:
            logger.warning('未找到远程文件夹缓存服务')
        except Exception as e:
            logger.error(f'加载远程文件夹缓存服务失败: {e}') 