/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React from 'react';
import { Card, Descriptions, Tag, Space, Row, Col, Progress } from 'antd';
import { DesktopOutlined, ClockCircleOutlined } from '@ant-design/icons';

function HostBasicInfo({ data = {}, loading }) {
  console.log('[DEBUG HostBasicInfo] 接收到的数据:', data);
  
  // 测试用模拟数据
  const mockData = {
    system_info: 'Red Hat Enterprise Linux 9.0 (Plow)',
    cpu_model: 'Intel(R) Xeon(R) CPU E5-2680 v4 @ 2.40GHz',
    memory_total: '32GB',
    memory_used: '8GB',
    memory_available: '24GB',
    uptime: 'up 7 days, 3 hours, 45 minutes',
    load_average: ['0.85', '0.92', '1.05']
  };
  
  // 如果没有数据，使用模拟数据进行测试
  const effectiveData = data?.system_info ? data : mockData;
  console.log('[DEBUG HostBasicInfo] 使用的数据:', effectiveData);

  // 系统状态
  const systemStatus = {
    color: 'success',
    text: '正常'
  };

  const formatUptime = (uptime) => {
    if (!uptime || uptime === '未知') return uptime || '未知';
    return uptime;
  };

  return (
    <Card
      title={
        <Space>
          <DesktopOutlined style={{ color: '#1890ff' }} />
          <span>主机基础信息</span>
          <Tag className={`status-${systemStatus.color}`}>
            {systemStatus.text}
          </Tag>
        </Space>
      }
      extra={
        data?.last_update && (
          <Space>
            <ClockCircleOutlined />
            <span style={{ fontSize: '12px', opacity: 0.8 }}>
              {new Date(data.last_update).toLocaleTimeString()}
            </span>
          </Space>
        )
      }
      loading={loading}
      style={{ height: '100%' }}
    >
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Descriptions size="small" column={1} bordered>
            <Descriptions.Item label="系统信息">{effectiveData.system_info || '未知'}</Descriptions.Item>
            <Descriptions.Item label="运行时间">{effectiveData.uptime || '未知'}</Descriptions.Item>
            <Descriptions.Item label="负载均衡">
              {effectiveData.load_average ? effectiveData.load_average.join(' / ') : '未知'}
            </Descriptions.Item>
          </Descriptions>
        </Col>
        <Col span={12}>
          <Descriptions size="small" column={1} bordered>
            <Descriptions.Item label="CPU型号">{effectiveData.cpu_model || '未知'}</Descriptions.Item>
            <Descriptions.Item label="内存总量">{effectiveData.memory_total || '未知'}</Descriptions.Item>
            <Descriptions.Item label="已用内存">
              <Space>
                {effectiveData.memory_used || '未知'}
                <Progress
                  percent={
                    effectiveData.memory_total && effectiveData.memory_used
                      ? Math.round((parseInt(effectiveData.memory_used) / parseInt(effectiveData.memory_total)) * 100)
                      : 0
                  }
                  size="small"
                  style={{ width: 100 }}
                />
              </Space>
            </Descriptions.Item>
          </Descriptions>
        </Col>
      </Row>
    </Card>
  );
}

export default HostBasicInfo; 