# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.

from django.db import models
from django.utils import timezone
import json
from libs import ModelMixin
from django.conf import settings
from apps.account.models import User


class RemoteFolder(models.Model, ModelMixin):
    """远程文件夹配置"""
    
    name = models.CharField(max_length=50, verbose_name='名称')
    remote_path = models.CharField(max_length=128, verbose_name='远程路径')
    username = models.CharField(max_length=100, blank=True, null=True, verbose_name='用户名')
    password = models.CharField(max_length=200, blank=True, null=True, verbose_name='密码')
    domain = models.CharField(max_length=100, blank=True, null=True, verbose_name='域名')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    description = models.TextField(blank=True, null=True, verbose_name='描述')
    host_id = models.IntegerField(verbose_name='主机ID')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'remote_path': self.remote_path,
            'username': self.username,
            'domain': self.domain,
            'is_active': self.is_active,
            'description': getattr(self, 'description', ''),
            'created_time': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        }
    
    def __str__(self):
        return f"{self.name} ({self.remote_path})"
    
    class Meta:
        db_table = 'remote_folders'
        ordering = ('-id',)
        verbose_name = '远程文件夹'
        verbose_name_plural = '远程文件夹'


class RemoteFolderCache(models.Model, ModelMixin):
    """远程文件夹缓存元数据（实际缓存存储在Redis中）"""
    
    remote_folder = models.ForeignKey(RemoteFolder, on_delete=models.CASCADE, verbose_name='远程文件夹')
    path = models.CharField(max_length=128, default='', verbose_name='相对路径')
    # files_data字段移除，实际数据存储在Redis中
    cache_time = models.DateTimeField(auto_now=True, verbose_name='缓存时间')
    is_valid = models.BooleanField(default=True, verbose_name='缓存是否有效')
    error_message = models.TextField(blank=True, null=True, verbose_name='错误信息')
    file_count = models.IntegerField(default=0, verbose_name='文件数量')
    cache_size = models.BigIntegerField(default=0, verbose_name='缓存大小（字节）')
    folder_id = models.IntegerField(verbose_name='文件夹ID')
    is_dir = models.BooleanField(default=False, verbose_name='是否是目录')
    size = models.IntegerField(default=0, verbose_name='文件大小')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    def to_dict(self):
        return {
            'id': self.id,
            'remote_folder_id': self.remote_folder.id,
            'path': self.path,
            'file_count': self.file_count,
            'cache_size': self.cache_size,
            'cache_time': self.cache_time.strftime('%Y-%m-%d %H:%M:%S') if self.cache_time else None,
            'is_valid': self.is_valid,
            'error_message': self.error_message
        }
    
    def __str__(self):
        return f"{self.remote_folder.name}:{self.path} (缓存于 {self.cache_time})"
    
    class Meta:
        db_table = 'remote_folder_caches'
        ordering = ('-id',)
        verbose_name = '远程文件夹缓存元数据'
        verbose_name_plural = '远程文件夹缓存元数据'
        unique_together = ('remote_folder', 'path') 