# Generated by Django 2.2.28 on 2025-07-02 16:52

from django.db import migrations, models
import django.db.models.deletion
import libs.mixins
import libs.utils


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('account', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Package',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='配套名称')),
                ('key', models.CharField(max_length=50, verbose_name='配套标识')),
                ('desc', models.CharField(max_length=255, null=True, verbose_name='配套描述')),
                ('sort_id', models.IntegerField(db_index=True, default=0)),
                ('created_at', models.CharField(default=libs.utils.human_datetime, max_length=20)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.User')),
            ],
            options={
                'db_table': 'packages',
                'ordering': ('-sort_id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('key', models.CharField(max_length=50, unique=True)),
                ('desc', models.CharField(max_length=255, null=True)),
                ('created_at', models.CharField(default=libs.utils.human_datetime, max_length=20)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.User')),
            ],
            options={
                'db_table': 'services',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='Environment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('key', models.CharField(max_length=50)),
                ('desc', models.CharField(max_length=255, null=True)),
                ('sort_id', models.IntegerField(db_index=True, default=0)),
                ('created_at', models.CharField(default=libs.utils.human_datetime, max_length=20)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.User')),
            ],
            options={
                'db_table': 'environments',
                'ordering': ('-sort_id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='ConfigHistory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(max_length=5)),
                ('o_id', models.IntegerField()),
                ('key', models.CharField(max_length=50)),
                ('env_id', models.IntegerField(null=True)),
                ('package_id', models.IntegerField(null=True)),
                ('value', models.TextField(null=True)),
                ('desc', models.CharField(max_length=255, null=True)),
                ('is_public', models.BooleanField()),
                ('old_value', models.TextField(null=True)),
                ('action', models.CharField(choices=[('1', '新增'), ('2', '更新'), ('3', '删除')], max_length=2)),
                ('updated_at', models.CharField(max_length=20)),
                ('updated_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.User')),
            ],
            options={
                'db_table': 'config_histories',
                'ordering': ('key',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='Config',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('app', 'App'), ('src', 'Service'), ('task', 'Task')], max_length=5)),
                ('o_id', models.IntegerField()),
                ('key', models.CharField(max_length=50, verbose_name='版本号')),
                ('value', models.TextField(null=True, verbose_name='文件路径')),
                ('desc', models.CharField(max_length=255, null=True, verbose_name='备注')),
                ('is_public', models.BooleanField(default=False)),
                ('updated_at', models.CharField(max_length=20)),
                ('env', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='config.Environment')),
                ('package', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='config.Package', verbose_name='关联配套')),
                ('updated_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.User')),
            ],
            options={
                'db_table': 'configs',
                'ordering': ('-key',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='TaskVersionPackage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_id', models.IntegerField(verbose_name='任务ID')),
                ('version', models.CharField(max_length=50, verbose_name='版本号')),
                ('file_path', models.TextField(verbose_name='文件路径')),
                ('desc', models.CharField(max_length=255, null=True, verbose_name='备注')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.CharField(default=libs.utils.human_datetime, max_length=20)),
                ('updated_at', models.CharField(max_length=20, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.User')),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='config.Package', verbose_name='配套')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='account.User')),
            ],
            options={
                'db_table': 'task_version_packages',
                'ordering': ('-id',),
                'unique_together': {('task_id', 'package', 'version')},
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
