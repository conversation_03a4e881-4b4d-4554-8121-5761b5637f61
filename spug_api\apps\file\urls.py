# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from django.urls import path

from .views import (
    FileView, ObjectView, FileEditView, ScriptDistributeView,
    FileManagerView, FileManagerEditView, FileDownloadView, FolderManagerView,
    FileTreeView, RemoteFolderView, RemoteFileManagerView, RemoteConnectView,
    RemoteTempFileManagerView, RemoteCacheManagementView,
    get_remote_folders, get_remote_files, download_remote_file, upload_to_remote,
    connect, cache_remote_folder, get_cache_status, transfer_to_local, universal_file_transfer
)

urlpatterns = [
    # SSH主机文件管理（原有功能） - 兼容原始路径
    path('', FileView.as_view()),  # 兼容 /file/ 直接访问
    path('ssh/', FileView.as_view()),
    path('object/', ObjectView.as_view()),  # 兼容 /file/object/ 直接访问
    path('ssh/object/', ObjectView.as_view()),
    path('edit/', FileEditView.as_view()),  # 兼容 /file/edit/ 直接访问
    path('ssh/edit/', FileEditView.as_view()),
    path('scripts/distribute/', ScriptDistributeView.as_view()),
    
    # 本地文件管理
    path('manager/', FileManagerView.as_view()),
    path('manager/edit/', FileManagerEditView.as_view()),
    path('download/<path:filename>/', FileDownloadView.as_view()),
    path('folder/', FolderManagerView.as_view()),
    path('tree/', FileTreeView.as_view()),
    
    # 远程文件夹管理
    path('remote/', RemoteFolderView.as_view()),
    path('remote/manager/', RemoteFileManagerView.as_view()),
    path('remote/connect/', RemoteConnectView.as_view()),
    path('remote/temp/', RemoteTempFileManagerView.as_view()),
    path('remote/cache/', RemoteCacheManagementView.as_view()),
    path('remote/download/', download_remote_file),
    path('remote/upload/', upload_to_remote),
    path('remote/transfer/', transfer_to_local),
    path('transfer/', universal_file_transfer),
    
    # 函数式视图（兼容性接口）
    path('connect/', connect),
    path('cache/', cache_remote_folder),
    path('status/', get_cache_status),
]
