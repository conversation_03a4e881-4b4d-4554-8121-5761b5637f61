/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React from 'react';
import { Card, Space, Row, Col, Alert, Button, Result, Empty, List, Tag } from 'antd';
import { AlertOutlined, CheckCircleOutlined, ReloadOutlined, FileTextOutlined, MonitorOutlined, ExclamationCircleOutlined, WarningOutlined } from '@ant-design/icons';

function MonitorDiagnosticInfo({ data = {}, loading }) {
  console.log('[DEBUG MonitorDiagnosticInfo] 接收到的数据:', data);
  
  // 测试用模拟数据
  const mockData = {
    error_logs: '没有GPU错误日志',
    health_status: '所有GPU运行正常',
    diagnostic_info: 'GPU健康检查通过，性能正常',
    recommendations: [
      '建议定期清理GPU散热器',
      '监控GPU温度避免过热',
      '保持驱动程序更新'
    ]
  };
  
  // 如果没有数据，使用模拟数据进行测试
  const effectiveData = data?.health_status ? data : mockData;
  console.log('[DEBUG MonitorDiagnosticInfo] 使用的数据:', effectiveData);

  const getDiagnosticStatus = () => {
    if (!data) return { color: 'default', text: '未检测', icon: <MonitorOutlined /> };
    
    const errorCount = data.error_logs?.length || 0;
    const warningCount = data.warning_logs?.length || 0;
    
    if (errorCount > 0) {
      return { color: 'error', text: `${errorCount}个错误`, icon: <ExclamationCircleOutlined /> };
    }
    if (warningCount > 0) {
      return { color: 'warning', text: `${warningCount}个警告`, icon: <WarningOutlined /> };
    }
    return { color: 'success', text: '正常', icon: <CheckCircleOutlined /> };
  };

  const errorColumns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 160,
      render: (timestamp) => new Date(timestamp).toLocaleString(),
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level) => (
        <Tag color={level === 'error' ? 'red' : level === 'warning' ? 'orange' : 'blue'}>
          {level === 'error' ? '错误' : level === 'warning' ? '警告' : '信息'}
        </Tag>
      ),
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      width: 120,
    },
    {
      title: '描述',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
    },
  ];

  const healthCheckColumns = [
    {
      title: '检查项',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'pass' ? 'green' : status === 'warning' ? 'orange' : 'red'}>
          {status === 'pass' ? '通过' : status === 'warning' ? '警告' : '失败'}
        </Tag>
      ),
    },
    {
      title: '详情',
      dataIndex: 'details',
      key: 'details',
      ellipsis: true,
    },
    {
      title: '最后检查',
      dataIndex: 'last_check',
      key: 'last_check',
      render: (time) => new Date(time).toLocaleString(),
    },
  ];

  const diagnosticStatus = getDiagnosticStatus();

  return (
    <Card
      title={
        <Space>
          <AlertOutlined style={{ color: '#10b981' }} />
          <span>监控诊断信息</span>
        </Space>
      }
      extra={
        <Space>
          <Button size="small" icon={<ReloadOutlined />}>重新检测</Button>
          <Button size="small" icon={<FileTextOutlined />}>查看日志</Button>
        </Space>
      }
      loading={loading}
      style={{ height: '100%' }}
    >
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card size="small" title="健康状态" style={{ height: '100%' }}>
            <Result
              status="success"
              title={effectiveData?.health_status || '所有GPU运行正常'}
              subTitle={effectiveData?.diagnostic_info || 'GPU健康检查通过，性能正常'}
            />
          </Card>
        </Col>
        
        <Col span={12}>
          <Card size="small" title="错误日志" style={{ height: '100%' }}>
            {effectiveData?.error_logs ? (
              <Alert
                message="错误日志"
                description={effectiveData.error_logs}
                type="info"
                showIcon
              />
            ) : (
              <Empty description="没有错误日志" />
            )}
          </Card>
        </Col>
        
        <Col span={24}>
          <Card size="small" title="优化建议">
            <List
              size="small"
              bordered
              dataSource={effectiveData?.recommendations || []}
              renderItem={item => (
                <List.Item>
                  <Space>
                    <CheckCircleOutlined style={{ color: '#10b981' }} />
                    {item}
                  </Space>
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </Card>
  );
}

export default MonitorDiagnosticInfo; 