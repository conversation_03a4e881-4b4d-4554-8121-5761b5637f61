.fileDetailCompare {
  .treeCard {
    height: 650px;
    overflow: hidden;
    
    .ant-card-body {
      padding: 12px;
      height: 100%;
      overflow: hidden;
    }
  }

  .localTree {
    border-right: 1px solid #f0f0f0;
    
    .ant-tree-node-content-wrapper {
      transition: all 0.3s;
      
      &:hover {
        background-color: #e6f7ff;
      }
    }
  }

  .remoteTree {
    .ant-tree-node-content-wrapper {
      transition: all 0.3s;
      
      &:hover {
        background-color: #f6ffed;
      }
    }
  }

  .selectedPath {
    margin-top: 16px;
  }

  .compareDetails {
    margin-top: 16px;
    
    .statusInfo {
      p {
        margin-bottom: 8px;
        font-size: 13px;
        
        strong {
          color: #262626;
          font-weight: 500;
        }
      }
    }
    
    .diffSummary {
      h4 {
        color: #262626;
        margin-bottom: 8px;
      }
      
      p {
        margin-bottom: 12px;
        color: #595959;
        font-size: 14px;
      }
    }
  }

  // 状态样式
  .ant-tree-title {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  // 同步状态样式
  .synced {
    .ant-tree-node-content-wrapper {
      background-color: #f6ffed;
      border-left: 3px solid #52c41a;
    }
  }

  .modified {
    .ant-tree-node-content-wrapper {
      background-color: #fffbe6;
      border-left: 3px solid #faad14;
    }
  }

  .missing {
    .ant-tree-node-content-wrapper {
      background-color: #fff2f0;
      border-left: 3px solid #f5222d;
    }
  }

  .added {
    .ant-tree-node-content-wrapper {
      background-color: #e6f7ff;
      border-left: 3px solid #1890ff;
    }
  }

  .conflict {
    .ant-tree-node-content-wrapper {
      background-color: #f9f0ff;
      border-left: 3px solid #722ed1;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-row {
      flex-direction: column;
    }
    
    .treeCard {
      height: 400px;
      margin-bottom: 16px;
    }
  }

  // 滚动条样式
  .ant-tree {
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
      
      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 加载状态
  .ant-spin-container {
    height: 100%;
  }

  // 搜索框样式
  .ant-input-search {
    .ant-input {
      border-radius: 4px;
    }
  }
}

/* 全屏版本样式 */
.fileDetailCompareFullscreen {
  height: 100vh;
  padding: 16px;
  background-color: #f0f2f5;
  
  .fullscreenCard {
    height: calc(100vh - 120px);
    
    .ant-card-body {
      height: calc(100vh - 200px);
      padding: 16px;
      overflow: hidden;
    }
  }
  
  .treeCard {
    height: 100%;
    
    .ant-card-body {
      height: calc(100vh - 300px);
      padding: 12px;
      overflow: auto;
    }
    
    .ant-tree {
      height: 100%;
      overflow: auto;
    }
  }
  
  .localTree {
    border-right: 1px solid #f0f0f0;
    
    .ant-tree-node-content-wrapper {
      transition: all 0.3s;
      
      &:hover {
        background-color: #e6f7ff;
      }
      
      &.ant-tree-node-selected {
        background-color: #bae7ff;
      }
    }
  }
  
  .remoteTree {
    .ant-tree-node-content-wrapper {
      transition: all 0.3s;
      
      &:hover {
        background-color: #fff7e6;
      }
      
      &.ant-tree-node-selected {
        background-color: #ffd591;
      }
    }
  }
  
  .statusInfo {
    p {
      margin-bottom: 8px;
      font-size: 14px;
      
      strong {
        color: #262626;
        margin-right: 8px;
        min-width: 80px;
        display: inline-block;
      }
    }
  }
  
  .diffSummary {
    h4 {
      color: #262626;
      margin-bottom: 12px;
      font-size: 16px;
    }
    
    p {
      color: #595959;
      margin-bottom: 16px;
      font-size: 14px;
      line-height: 1.6;
    }
  }
  
  .selectedPath {
    position: sticky;
    bottom: 0;
    z-index: 10;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(4px);
    border-radius: 6px;
    margin-top: 16px;
  }
  
  .compareDetails {
    margin-top: 16px;
    
    .ant-card-body {
      max-height: 300px;
      overflow: auto;
    }
  }
  
  // 继承原有状态样式
  .synced {
    .ant-tree-node-content-wrapper {
      background-color: #f6ffed;
      border-left: 3px solid #52c41a;
    }
  }

  .modified {
    .ant-tree-node-content-wrapper {
      background-color: #fffbe6;
      border-left: 3px solid #faad14;
    }
  }

  .missing {
    .ant-tree-node-content-wrapper {
      background-color: #fff2f0;
      border-left: 3px solid #f5222d;
    }
  }

  .added {
    .ant-tree-node-content-wrapper {
      background-color: #e6f7ff;
      border-left: 3px solid #1890ff;
    }
  }

  .conflict {
    .ant-tree-node-content-wrapper {
      background-color: #f9f0ff;
      border-left: 3px solid #722ed1;
    }
  }
  
  // 文档统计表格总计行样式
  .totalRow {
    background-color: #f0f2f5;
    font-weight: bold;
    
    td {
      border-top: 2px solid #d9d9d9;
    }
  }
  
  // 滚动条样式
  .ant-tree {
    &::-webkit-scrollbar {
      width: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
      
      &:hover {
        background: #a8a8a8;
      }
    }
  }
  
  /* 响应式设计 */
  @media (max-width: 1200px) {
    .treeCard {
      .ant-card-body {
        height: calc(100vh - 350px);
      }
    }
  }
  
  @media (max-width: 768px) {
    padding: 8px;
    
    .fullscreenCard {
      height: calc(100vh - 80px);
      
      .ant-card-body {
        height: calc(100vh - 160px);
        padding: 8px;
      }
    }
    
    .treeCard {
      .ant-card-body {
        height: calc(100vh - 400px);
        padding: 8px;
      }
    }
    
    .ant-row {
      flex-direction: column;
    }
    
    .ant-col {
      margin-bottom: 16px;
    }
  }
}

.container {
  margin: 16px;
  
  :global {
    .ant-card-body {
      padding: 16px;
    }
  }
}

.alert {
  margin-bottom: 16px;
}

.treeContainer {
  height: 600px;
  overflow: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px;
}

:global {
  .ant-tree {
    .ant-tree-node-content-wrapper {
      display: flex;
      align-items: center;
      
      .ant-tree-title {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }
}