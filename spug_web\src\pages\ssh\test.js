/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React from 'react';
import { Card, message } from 'antd';
import QuickCommandPanel from './QuickCommandPanel';

function TestPage() {
  const handleExecuteCommand = (command) => {
    message.info(`模拟执行命令: ${command}`);
    return Promise.resolve();
  };

  return (
    <div style={{ padding: '20px', height: '100vh', background: '#f0f2f5' }}>
      <Card title="快捷命令面板测试">
        <div style={{ height: '500px', width: '300px' }}>
          <QuickCommandPanel
            hostId={1}
            onExecuteCommand={handleExecuteCommand}
            disabled={false}
          />
        </div>
      </Card>
    </div>
  );
}

export default TestPage;