# Spug 项目结构文档

## 项目概述
Spug是面向中小型企业设计的轻量级无Agent的自动化运维平台，整合了主机管理、主机批量执行、主机在线终端、应用发布部署、在线任务计划、配置中心、监控、报警等功能。

## 技术栈
- 后端：Python 3.6+、Django 2.2
- 前端：Node 12.14、React 16.11、Ant Design
- 数据库：SQLite

## 项目结构

### 根目录
```
spug/
├── spug_api/          # 后端API服务
├── spug_web/          # 前端Web应用
├── docs/              # 文档目录
├── Users/             # 用户相关文件
├── README.md          # 项目说明文档
├── LICENSE            # 开源协议文件
├── Tread.md           # 项目相关说明
└── WINDOWS_COMPATIBILITY.md  # Windows兼容性说明
```

### 后端结构 (spug_api/)
```
spug_api/
├── apps/              # Django应用模块
│   ├── account/       # 账户管理
│   ├── alarm/         # 报警模块
│   ├── apis/          # API接口
│   ├── app/           # 应用管理
│   ├── config/        # 配置中心
│   ├── deploy/        # 部署模块
│   ├── exec/          # 批量执行
│   ├── file/          # 文件管理
│   ├── home/          # 首页
│   ├── host/          # 主机管理
│   ├── monitor/       # 监控模块
│   ├── notify/        # 通知模块
│   ├── repository/    # 代码仓库
│   ├── schedule/      # 任务计划
│   └── setting/       # 系统设置
├── consumer/          # 消费者服务
├── files/             # 文件存储
│   ├── 固件/
│   └── 脚本/
├── libs/              # 公共库
├── logs/              # 日志文件
├── repos/             # 代码仓库
├── spug/              # Django项目配置
├── storage/           # 存储目录
├── tools/             # 工具脚本
├── venv/              # 虚拟环境
├── manage.py          # Django管理脚本
├── requirements.txt   # Python依赖
└── db.sqlite3         # SQLite数据库
```

### 前端结构 (spug_web/)
```
spug_web/
├── src/               # 源代码
│   ├── components/    # 公共组件
│   │   ├── DragUpload/    # 拖拽上传组件
│   │   └── FileTree/      # 文件树组件
│   ├── layout/        # 布局组件
│   ├── libs/          # 公共库
│   └── pages/         # 页面组件
│       ├── alarm/         # 报警页面
│       │   ├── alarm/     # 报警记录
│       │   ├── contact/   # 联系人
│       │   └── group/     # 联系组
│       ├── config/        # 配置页面
│       │   ├── app/       # 应用配置
│       │   ├── environment/   # 环境配置
│       │   ├── service/   # 服务配置
│       │   └── setting/   # 配置设置
│       ├── dashboard/     # 仪表盘
│       ├── deploy/        # 部署页面
│       │   ├── app/       # 应用部署
│       │   ├── repository/    # 代码仓库
│       │   └── request/   # 发布申请
│       ├── exec/          # 执行页面
│       │   ├── task/      # 执行任务
│       │   ├── template/  # 执行模板
│       │   └── transfer/  # 文件传输
│       ├── file/          # 文件管理
│       ├── home/          # 首页
│       ├── host/          # 主机管理
│       ├── login/         # 登录页面
│       ├── monitor/       # 监控页面
│       ├── schedule/      # 任务计划
│       ├── ssh/           # SSH终端
│       ├── system/        # 系统管理
│       │   ├── account/   # 账户管理
│       │   ├── login/     # 登录日志
│       │   ├── role/      # 角色管理
│       │   └── setting/   # 系统设置
│       └── welcome/       # 欢迎页面
│           ├── index/     # 欢迎首页
│           └── info/      # 信息页面
├── public/            # 静态资源
│   └── resource/      # 资源文件
├── build/             # 构建输出
├── node_modules/      # Node.js依赖
├── package.json       # 项目配置
├── package-lock.json  # 依赖锁定文件
├── config-overrides.js    # 配置覆盖
└── jsconfig.json      # JavaScript配置
```

## 核心功能模块

1. **主机管理 (host/)** - 主机信息管理、在线终端、文件管理
2. **批量执行 (exec/)** - 主机命令批量执行、任务模板
3. **应用发布 (deploy/)** - 应用部署、代码仓库管理
4. **配置中心 (config/)** - 配置管理、环境配置
5. **任务计划 (schedule/)** - 定时任务管理
6. **监控报警 (monitor/alarm/)** - 系统监控、报警通知
7. **系统管理 (system/)** - 用户账户、角色权限管理
