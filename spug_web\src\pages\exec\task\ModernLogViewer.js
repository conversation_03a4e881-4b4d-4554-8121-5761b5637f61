/**
 * 现代化日志查看器 - 替代xterm终端，提供更好的用户体验
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  Card, 
  Button, 
  Input, 
  Space, 
  Tag, 
  Tooltip, 
  message, 
  Dropdown, 
  Menu, 
  Switch,
  Divider,
  BackTop,
  Progress,
  Empty
} from 'antd';
import { 
  SearchOutlined, 
  DownloadOutlined, 
  CopyOutlined,
  ClearOutlined,
  SettingOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  FilterOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons';
import styles from './ModernLogViewer.module.less';

const { Search } = Input;

function ModernLogViewer({ 
  content = '', 
  onBack, 
  title = '执行日志',
  showProgress = false,
  progress = 0,
  status = 'running',
  autoScroll = true,
  enableSearch = true,
  enableDownload = true 
}) {
  const [logs, setLogs] = useState([]);
  const [filteredLogs, setFilteredLogs] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  const [fontSize, setFontSize] = useState(13);
  const [showTimestamp, setShowTimestamp] = useState(true);
  const [showLineNumbers, setShowLineNumbers] = useState(true);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(autoScroll);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [logLevel, setLogLevel] = useState('all'); // all, info, warning, error
  const logContainerRef = useRef(null);
  const logEndRef = useRef(null);

  // 解析日志内容
  useEffect(() => {
    if (content) {
      const lines = content.split('\n').map((line, index) => {
        const timestamp = extractTimestamp(line);
        const level = extractLogLevel(line);
        const cleanedLine = cleanAnsiCodes(line);
        
        return {
          id: index,
          timestamp,
          level,
          content: cleanedLine,
          originalContent: line,
          isHighlighted: false
        };
      }).filter(log => log.content.trim() !== '');
      
      setLogs(lines);
      
      // 自动滚动到底部
      if (autoScrollEnabled) {
        setTimeout(() => {
          if (logEndRef.current) {
            logEndRef.current.scrollIntoView({ behavior: 'smooth' });
          }
        }, 100);
      }
    }
  }, [content, autoScrollEnabled]);

  // 应用过滤器
  useEffect(() => {
    let filtered = logs;
    
    // 按日志级别过滤
    if (logLevel !== 'all') {
      filtered = filtered.filter(log => log.level === logLevel);
    }
    
    setFilteredLogs(filtered);
  }, [logs, logLevel]);

  // 搜索功能
  useEffect(() => {
    if (searchTerm) {
      const results = [];
      filteredLogs.forEach((log, index) => {
        if (log.content.toLowerCase().includes(searchTerm.toLowerCase())) {
          results.push(index);
        }
      });
      setSearchResults(results);
      setCurrentSearchIndex(results.length > 0 ? 0 : -1);
    } else {
      setSearchResults([]);
      setCurrentSearchIndex(-1);
    }
  }, [searchTerm, filteredLogs]);

  // 清理ANSI转义序列
  const cleanAnsiCodes = (text) => {
    return text.replace(/\x1b\[[0-9;]*m/g, '');
  };

  // 提取时间戳
  const extractTimestamp = (line) => {
    const timestampRegex = /\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/;
    const match = line.match(timestampRegex);
    return match ? match[1] : null;
  };

  // 提取日志级别
  const extractLogLevel = (line) => {
    const cleanLine = cleanAnsiCodes(line).toLowerCase();
    if (cleanLine.includes('error') || cleanLine.includes('failed') || cleanLine.includes('❌')) {
      return 'error';
    } else if (cleanLine.includes('warning') || cleanLine.includes('warn') || cleanLine.includes('⚠️')) {
      return 'warning';
    } else if (cleanLine.includes('success') || cleanLine.includes('✅') || cleanLine.includes('完成')) {
      return 'success';
    } else {
      return 'info';
    }
  };

  // 获取日志级别颜色
  const getLogLevelColor = (level) => {
    switch (level) {
      case 'error': return '#ff4d4f';
      case 'warning': return '#faad14';
      case 'success': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  // 获取日志级别图标
  const getLogLevelIcon = (level) => {
    switch (level) {
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'success': return '✅';
      default: return 'ℹ️';
    }
  };

  // 搜索功能
  const handleSearch = (value) => {
    setSearchTerm(value);
  };

  const navigateSearch = (direction) => {
    if (searchResults.length === 0) return;
    
    let newIndex;
    if (direction === 'next') {
      newIndex = currentSearchIndex >= searchResults.length - 1 ? 0 : currentSearchIndex + 1;
    } else {
      newIndex = currentSearchIndex <= 0 ? searchResults.length - 1 : currentSearchIndex - 1;
    }
    
    setCurrentSearchIndex(newIndex);
    
    // 滚动到搜索结果
    const resultLineIndex = searchResults[newIndex];
    const element = document.getElementById(`log-line-${resultLineIndex}`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  // 复制日志
  const handleCopy = () => {
    const textToCopy = filteredLogs.map(log => 
      `${showTimestamp && log.timestamp ? `[${log.timestamp}] ` : ''}${log.content}`
    ).join('\n');
    
    navigator.clipboard.writeText(textToCopy).then(() => {
      message.success('日志已复制到剪贴板');
    });
  };

  // 下载日志
  const handleDownload = () => {
    const textToDownload = filteredLogs.map(log => 
      `${showTimestamp && log.timestamp ? `[${log.timestamp}] ` : ''}${log.content}`
    ).join('\n');
    
    const blob = new Blob([textToDownload], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `execution-log-${new Date().toISOString().slice(0, 10)}.txt`;
    a.click();
    URL.revokeObjectURL(url);
    message.success('日志已下载');
  };

  // 清空日志
  const handleClear = () => {
    setLogs([]);
    setFilteredLogs([]);
    setSearchTerm('');
    message.info('日志已清空');
  };

  // 切换全屏
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 设置菜单
  const settingsMenu = (
    <Menu>
      <Menu.Item key="fontSize">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: 200 }}>
          <span>字体大小</span>
          <Space>
            <Button 
              size="small" 
              icon={<ZoomOutOutlined />} 
              onClick={() => setFontSize(Math.max(10, fontSize - 1))}
              disabled={fontSize <= 10}
            />
            <span style={{ minWidth: 20, textAlign: 'center' }}>{fontSize}</span>
            <Button 
              size="small" 
              icon={<ZoomInOutlined />} 
              onClick={() => setFontSize(Math.min(20, fontSize + 1))}
              disabled={fontSize >= 20}
            />
          </Space>
        </div>
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="timestamp">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: 200 }}>
          <span>显示时间戳</span>
          <Switch 
            size="small" 
            checked={showTimestamp} 
            onChange={setShowTimestamp} 
          />
        </div>
      </Menu.Item>
      <Menu.Item key="lineNumbers">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: 200 }}>
          <span>显示行号</span>
          <Switch 
            size="small" 
            checked={showLineNumbers} 
            onChange={setShowLineNumbers} 
          />
        </div>
      </Menu.Item>
      <Menu.Item key="autoScroll">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: 200 }}>
          <span>自动滚动</span>
          <Switch 
            size="small" 
            checked={autoScrollEnabled} 
            onChange={setAutoScrollEnabled} 
          />
        </div>
      </Menu.Item>
    </Menu>
  );

  // 渲染高亮搜索词
  const renderHighlightedContent = (content, lineIndex) => {
    if (!searchTerm) return content;
    
    const isCurrentResult = searchResults[currentSearchIndex] === lineIndex;
    const parts = content.split(new RegExp(`(${searchTerm})`, 'gi'));
    
    return parts.map((part, index) => {
      if (part.toLowerCase() === searchTerm.toLowerCase()) {
        return (
          <span 
            key={index} 
            style={{ 
              backgroundColor: isCurrentResult ? '#ff9500' : '#ffeb3b', 
              color: isCurrentResult ? '#fff' : '#000',
              padding: '1px 2px',
              borderRadius: '2px'
            }}
          >
            {part}
          </span>
        );
      }
      return part;
    });
  };

  return (
    <div className={`${styles['modern-log-viewer']} ${isFullscreen ? styles.fullscreen : ''}`}>
      {/* 头部工具栏 */}
      <Card 
        size="small" 
        className={styles['log-toolbar']}
        bodyStyle={{ padding: '8px 16px' }}
      >
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <h4 style={{ margin: 0, fontSize: '16px' }}>{title}</h4>
              {showProgress && (
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Progress 
                    size="small" 
                    percent={progress} 
                    style={{ width: 120 }}
                    strokeColor={status === 'error' ? '#ff4d4f' : status === 'success' ? '#52c41a' : '#1890ff'}
                  />
                  <Tag color={status === 'error' ? 'red' : status === 'success' ? 'green' : 'blue'}>
                    {status === 'running' ? '执行中' : status === 'success' ? '成功' : status === 'error' ? '失败' : '完成'}
                  </Tag>
                </div>
              )}
            </div>
            
            {/* 日志级别过滤 */}
            <Space>
              <span style={{ fontSize: '12px', color: '#999' }}>日志级别:</span>
              <Button.Group size="small">
                <Button 
                  type={logLevel === 'all' ? 'primary' : 'default'}
                  onClick={() => setLogLevel('all')}
                >
                  全部 ({logs.length})
                </Button>
                <Button 
                  type={logLevel === 'info' ? 'primary' : 'default'}
                  onClick={() => setLogLevel('info')}
                >
                  信息 ({logs.filter(l => l.level === 'info').length})
                </Button>
                <Button 
                  type={logLevel === 'warning' ? 'primary' : 'default'}
                  onClick={() => setLogLevel('warning')}
                >
                  警告 ({logs.filter(l => l.level === 'warning').length})
                </Button>
                <Button 
                  type={logLevel === 'error' ? 'primary' : 'default'}
                  onClick={() => setLogLevel('error')}
                >
                  错误 ({logs.filter(l => l.level === 'error').length})
                </Button>
              </Button.Group>
            </Space>
          </div>

          <div style={{ display: 'flex', gap: 16 }}>
            {/* 搜索框 */}
            {enableSearch && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <Search
                  placeholder="搜索日志内容"
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  style={{ width: 200 }}
                  size="small"
                />
                {searchResults.length > 0 && (
                  <Space size={2}>
                    <span style={{ fontSize: '12px', color: '#999' }}>
                      {currentSearchIndex + 1}/{searchResults.length}
                    </span>
                    <Button 
                      size="small" 
                      icon={<ArrowUpOutlined />} 
                      onClick={() => navigateSearch('prev')}
                    />
                    <Button 
                      size="small" 
                      icon={<ArrowDownOutlined />} 
                      onClick={() => navigateSearch('next')}
                    />
                  </Space>
                )}
              </div>
            )}
            
            {/* 工具按钮 */}
            <Space>
              <Tooltip title="复制日志">
                <Button size="small" icon={<CopyOutlined />} onClick={handleCopy} />
              </Tooltip>
              
              {enableDownload && (
                <Tooltip title="下载日志">
                  <Button size="small" icon={<DownloadOutlined />} onClick={handleDownload} />
                </Tooltip>
              )}
              
              <Tooltip title="清空日志">
                <Button size="small" icon={<ClearOutlined />} onClick={handleClear} />
              </Tooltip>
              
              <Tooltip title="全屏切换">
                <Button 
                  size="small" 
                  icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />} 
                  onClick={toggleFullscreen} 
                />
              </Tooltip>
              
              <Dropdown overlay={settingsMenu} trigger={['click']}>
                <Button size="small" icon={<SettingOutlined />} />
              </Dropdown>
              
              {onBack && (
                <Button size="small" onClick={onBack}>
                  返回
                </Button>
              )}
            </Space>
          </div>
        </div>
      </Card>

      {/* 日志内容区域 */}
      <div 
        className={styles['log-container']} 
        ref={logContainerRef}
        style={{
          fontSize: `${fontSize}px`,
          height: isFullscreen ? 'calc(100vh - 120px)' : 'calc(100vh - 300px)',
          overflow: 'auto',
          backgroundColor: '#1e1e1e',
          color: '#d4d4d4',
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, monospace',
          lineHeight: 1.4,
          padding: '16px 0'
        }}
      >
        {filteredLogs.length === 0 ? (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center', 
            height: '100%',
            color: '#999'
          }}>
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无日志内容"
              style={{ color: '#999' }}
            />
          </div>
        ) : (
          filteredLogs.map((log, index) => (
            <div
              key={log.id}
              id={`log-line-${index}`}
              className={styles['log-line']}
              style={{
                display: 'flex',
                alignItems: 'flex-start',
                padding: '2px 16px',
                borderLeft: `3px solid ${getLogLevelColor(log.level)}`,
                backgroundColor: searchResults.includes(index) ? 
                  (currentSearchIndex !== -1 && searchResults[currentSearchIndex] === index ? 
                    'rgba(255, 149, 0, 0.1)' : 'rgba(255, 235, 59, 0.1)') : 
                  'transparent',
                ':hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.05)'
                }
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
              }}
              onMouseLeave={(e) => {
                if (!searchResults.includes(index)) {
                  e.currentTarget.style.backgroundColor = 'transparent';
                } else {
                  e.currentTarget.style.backgroundColor = currentSearchIndex !== -1 && searchResults[currentSearchIndex] === index ? 
                    'rgba(255, 149, 0, 0.1)' : 'rgba(255, 235, 59, 0.1)';
                }
              }}
            >
              {/* 行号 */}
              {showLineNumbers && (
                <span style={{
                  color: '#858585',
                  fontSize: '11px',
                  marginRight: '12px',
                  minWidth: '40px',
                  textAlign: 'right',
                  userSelect: 'none',
                  fontFamily: 'inherit'
                }}>
                  {String(index + 1).padStart(4, ' ')}
                </span>
              )}
              
              {/* 日志级别图标 */}
              <span style={{
                marginRight: '8px',
                fontSize: '12px',
                userSelect: 'none'
              }}>
                {getLogLevelIcon(log.level)}
              </span>
              
              {/* 时间戳 */}
              {showTimestamp && log.timestamp && (
                <span style={{
                  color: '#858585',
                  fontSize: '11px',
                  marginRight: '12px',
                  userSelect: 'none',
                  fontFamily: 'inherit'
                }}>
                  [{log.timestamp}]
                </span>
              )}
              
              {/* 日志内容 */}
              <span style={{
                flex: 1,
                wordBreak: 'break-word',
                whiteSpace: 'pre-wrap',
                fontFamily: 'inherit'
              }}>
                {renderHighlightedContent(log.content, index)}
              </span>
            </div>
          ))
        )}
        
        {/* 自动滚动锚点 */}
        <div ref={logEndRef} />
      </div>

      {/* 回到顶部 */}
      <BackTop 
        target={() => logContainerRef.current}
        style={{
          right: isFullscreen ? 24 : 24,
          bottom: isFullscreen ? 24 : 24
        }}
      />
    </div>
  );
}

export default ModernLogViewer; 