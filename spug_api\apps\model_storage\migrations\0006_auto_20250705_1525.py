# Generated by Django 2.2.28 on 2025-07-05 15:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0005_auto_20250705_1455'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='testtask',
            name='framework',
        ),
        migrations.AddField(
            model_name='testtask',
            name='document_output',
            field=models.BooleanField(default=False, help_text='是否已输出测试资料文档', verbose_name='资料输出'),
        ),
        migrations.AddField(
            model_name='testtask',
            name='gpu_model',
            field=models.CharField(blank=True, choices=[('P800', 'P800'), ('P800-PCIe', 'P800-PCIe'), ('RG800', 'RG800'), ('R5500_G6', 'R5500 G6'), ('R5500_G7', 'R5500 G7'), ('R5330_G7', 'R5330 G7'), ('C550', 'C550'), ('C500', 'C500'), ('other', '其他')], default='P800', max_length=32, null=True, verbose_name='GPU型号'),
        ),
    ]
