.gpuDashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4f8 0%, #e8f2ff 50%, #f8fafc 100%);
  color: #1a202c;
  overflow-x: hidden;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 0;
    z-index: 1000;
    
    .headerLeft,
    .headerRight {
      flex: 1;
    }
    
    .headerCenter {
      flex: 2;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .updateTime {
      color: #64748b;
      font-size: 12px;
      margin-left: 16px;
    }
  }
  
  .content {
    padding: 24px;
    min-height: calc(100vh - 80px);
    
    // 全局卡片样式
    :global(.ant-card) {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(0, 0, 0, 0.06);
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        border-color: rgba(59, 130, 246, 0.3);
      }
      
      .ant-card-head {
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 16px 16px 0 0;
        
        .ant-card-head-title {
          color: #1e293b;
          font-weight: 600;
          font-size: 16px;
        }
        
        .ant-card-extra {
          color: #64748b;
        }
      }
      
      .ant-card-body {
        background: transparent;
        color: #334155;
      }
    }
    
    // 统计卡片样式
    :global(.ant-statistic) {
      .ant-statistic-title {
        color: #64748b;
        font-size: 14px;
        margin-bottom: 8px;
        font-weight: 500;
      }
      
      .ant-statistic-content {
        color: #1e293b;
        font-weight: 600;
      }
    }
    
    // 进度条样式
    :global(.ant-progress) {
      .ant-progress-text {
        color: #1e293b !important;
        font-weight: 500;
      }
      
      .ant-progress-bg {
        background: rgba(59, 130, 246, 0.15) !important;
      }
      
      .ant-progress-inner {
        background: #e2e8f0 !important;
      }
    }
    
    // 标签样式
    :global(.ant-tag) {
      background: #f1f5f9;
      border: 1px solid #cbd5e1;
      color: #475569;
      border-radius: 8px;
      font-weight: 500;
      
      &.status-normal {
        background: #dcfce7;
        border-color: #86efac;
        color: #166534;
      }
      
      &.status-warning {
        background: #fef3c7;
        border-color: #fbbf24;
        color: #92400e;
      }
      
      &.status-error {
        background: #fee2e2;
        border-color: #fca5a5;
        color: #dc2626;
      }
    }
    
    // 描述列表样式
    :global(.ant-descriptions) {
      .ant-descriptions-item-label {
        color: #64748b;
        font-weight: 600;
      }
      
      .ant-descriptions-item-content {
        color: #1e293b;
        font-weight: 500;
      }
      
      &.ant-descriptions-bordered {
        .ant-descriptions-item-label,
        .ant-descriptions-item-content {
          background: #f8fafc;
          border-color: #e2e8f0;
        }
        
        .ant-descriptions-item-label {
          background: #f1f5f9;
        }
      }
    }
    
    // 表格样式
    :global(.ant-table) {
      background: transparent;
      
      .ant-table-thead > tr > th {
        background: #f8fafc;
        border-bottom: 1px solid #e2e8f0;
        color: #1e293b;
        font-weight: 600;
      }
      
      .ant-table-tbody > tr > td {
        background: rgba(255, 255, 255, 0.8);
        border-bottom: 1px solid #f1f5f9;
        color: #334155;
      }
      
      .ant-table-tbody > tr:hover > td {
        background: rgba(59, 130, 246, 0.05);
      }
    }
    
    // 空状态样式
    :global(.ant-empty) {
      .ant-empty-description {
        color: #64748b;
      }
    }
    
    // 按钮样式优化
    :global(.ant-btn) {
      border-radius: 8px;
      font-weight: 500;
      
      &.ant-btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        
        &:hover {
          background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
      }
      
      &.ant-btn-default {
        background: #ffffff;
        border-color: #d1d5db;
        color: #374151;
        
        &:hover {
          border-color: #3b82f6;
          color: #3b82f6;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1600px) {
  .gpuDashboard .content {
    padding: 16px;
  }
}

@media (max-width: 1200px) {
  .gpuDashboard {
    .header {
      padding: 12px 16px;
      flex-direction: column;
      gap: 12px;
      
      .headerLeft,
      .headerCenter,
      .headerRight {
        flex: none;
        width: 100%;
        justify-content: center;
      }
    }
    
    .content {
      padding: 12px;
    }
  }
}

// 全屏模式优化
:global(.fullscreen) {
  .gpuDashboard {
    .header {
      background: rgba(255, 255, 255, 0.95);
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
    
    .content {
      padding: 32px;
    }
  }
}

// 动画效果
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

// 状态指示器动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  
  &.active {
    animation: pulse 2s infinite;
  }
  
  &.normal {
    background: #10b981;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
  }
  
  &.warning {
    background: #f59e0b;
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
  }
  
  &.error {
    background: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
  }
  
  &.unknown {
    background: #6b7280;
  }
} 