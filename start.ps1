# start.ps1

# 设置参数
param(
    [switch]$CleanInstall = $false,    # 是否清理安装
    [switch]$SkipScoop = $false,       # 是否跳过Scoop安装
    [switch]$SkipPython = $false,      # 是否跳过Python安装
    [switch]$SkipNode = $false,        # 是否跳过Node安装
    [switch]$Force = $false            # 强制模式，不询问确认
)

# 设置执行策略为允许运行脚本
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser -Force

# 定义所需版本
$REQUIRED_PYTHON_VERSION = "3.11.9"
$REQUIRED_NODE_VERSION = "23.11.0"
$MAX_RETRY_COUNT = 3

# 输出带颜色的信息函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

# 检查命令是否存在
function Check-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# 环境状态检查
function Check-EnvironmentStatus {
    $needClean = $false
    $reason = @()
    
    # 检查Python依赖
    if (Test-Path "spug_api/venv") {
        try {
            # 检查是否能正常激活虚拟环境
            & "spug_api/venv/Scripts/python" -c "print('test')" 2>$null
            if ($LASTEXITCODE -ne 0) {
                $needClean = $true
                $reason += "Python虚拟环境可能损坏"
            }
        } catch {
            $needClean = $true
            $reason += "Python虚拟环境访问异常"
        }
    }
    
    # 检查Node依赖
    if (Test-Path "spug_web/node_modules") {
        if (-not (Test-Path "spug_web/node_modules/.package-lock.json")) {
            $needClean = $true
            $reason += "Node依赖可能不完整"
        }
    }
    
    return @{
        NeedClean = $needClean
        Reason = $reason
    }
}

# 环境清理函数
function Clean-Environment {
    param(
        [switch]$Force = $false
    )
    
    if ($CleanInstall -or $Force) {
        Write-ColorOutput Yellow "开始清理环境..."
        
        # 询问用户确认
        if (-not $Force) {
            $confirm = Read-Host "此操作将删除所有依赖和构建文件，确定要继续吗？(Y/N)"
            if ($confirm -ne "Y") {
                Write-ColorOutput Green "已取消清理操作"
                return $false
            }
        }
        
        # 清理前端
        if (Test-Path "spug_web/node_modules") {
            Remove-Item -Recurse -Force "spug_web/node_modules"
        }
        if (Test-Path "spug_web/dist") {
            Remove-Item -Recurse -Force "spug_web/dist"
        }
        
        # 清理后端
        if (Test-Path "spug_api/venv") {
            Remove-Item -Recurse -Force "spug_api/venv"
        }
        
        Write-ColorOutput Green "环境清理完成"
        return $true
    }
    return $true
}

# 版本检查函数
function Check-PythonVersion {
    if (Check-Command "python") {
        $currentVersion = (python --version 2>&1).ToString() -replace "Python "
        if ([version]$currentVersion -lt [version]$REQUIRED_PYTHON_VERSION) {
            Write-ColorOutput Yellow "当前Python版本($currentVersion)低于要求版本($REQUIRED_PYTHON_VERSION)，需要更新"
            return $false
        }
        return $true
    }
    return $false
}

function Check-NodeVersion {
    if (Check-Command "node") {
        $currentVersion = (node --version).ToString() -replace "v"
        if ([version]$currentVersion -lt [version]$REQUIRED_NODE_VERSION) {
            Write-ColorOutput Yellow "当前Node.js版本($currentVersion)低于要求版本($REQUIRED_NODE_VERSION)，需要更新"
            return $false
        }
        return $true
    }
    return $false
}

# 安装Scoop
function Install-Scoop {
    if (-not $SkipScoop) {
        if (-not (Check-Command "scoop")) {
            Write-ColorOutput Green "正在安装 Scoop..."
            try {
                Invoke-Expression (New-Object System.Net.WebClient).DownloadString('https://get.scoop.sh')
                $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
                Write-ColorOutput Green "Scoop 安装成功"
            }
            catch {
                Write-ColorOutput Red "Scoop 安装失败: $_"
                return $false
            }
        }
        else {
            Write-ColorOutput Green "Scoop 已安装"
            scoop update
        }
    }
    return $true
}

# 安装Python
function Install-Python {
    if (-not $SkipPython) {
        $retryCount = 0
        while ($retryCount -lt $MAX_RETRY_COUNT) {
            if (-not (Check-PythonVersion)) {
                Write-ColorOutput Green "正在安装 Python $REQUIRED_PYTHON_VERSION..."
                try {
                    scoop install python@$REQUIRED_PYTHON_VERSION
                    if (Check-PythonVersion) {
                        Write-ColorOutput Green "Python 安装成功"
                        return $true
                    }
                }
                catch {
                    Write-ColorOutput Red "Python 安装失败: $_"
                    $retryCount++
                    if ($retryCount -lt $MAX_RETRY_COUNT) {
                        Write-ColorOutput Yellow "正在重试... (尝试 $($retryCount + 1)/$MAX_RETRY_COUNT)"
                        Start-Sleep -Seconds 3
                    }
                }
            }
            else {
                Write-ColorOutput Green "Python 版本检查通过"
                return $true
            }
        }
        return $false
    }
    return $true
}

# 安装Node.js
function Install-Node {
    if (-not $SkipNode) {
        $retryCount = 0
        while ($retryCount -lt $MAX_RETRY_COUNT) {
            if (-not (Check-NodeVersion)) {
                Write-ColorOutput Green "正在安装 Node.js $REQUIRED_NODE_VERSION..."
                try {
                    scoop install nodejs@$REQUIRED_NODE_VERSION
                    if (Check-NodeVersion) {
                        Write-ColorOutput Green "Node.js 安装成功"
                        return $true
                    }
                }
                catch {
                    Write-ColorOutput Red "Node.js 安装失败: $_"
                    $retryCount++
                    if ($retryCount -lt $MAX_RETRY_COUNT) {
                        Write-ColorOutput Yellow "正在重试... (尝试 $($retryCount + 1)/$MAX_RETRY_COUNT)"
                        Start-Sleep -Seconds 3
                    }
                }
            }
            else {
                Write-ColorOutput Green "Node.js 版本检查通过"
                return $true
            }
        }
        return $false
    }
    return $true
}

# 安装依赖
function Install-Dependencies {
    # 安装后端依赖
    Write-ColorOutput Green "正在安装后端依赖..."
    Set-Location spug_api
    python -m venv venv
    . .\venv\Scripts\activate
    python -m pip install --upgrade pip
    pip install -r requirements.txt
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput Red "后端依赖安装失败"
        return $false
    }
    deactivate
    Set-Location ..

    # 安装前端依赖
    Write-ColorOutput Green "正在安装前端依赖..."
    Set-Location spug_web
    npm install --legacy-peer-deps
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput Red "前端依赖安装失败"
        return $false
    }
    Set-Location ..
    
    return $true
}

# 启动服务
function Start-Services {
    # 启动后端
    Write-ColorOutput Green "正在启动后端服务..."
    Start-Process -NoNewWindow -FilePath "powershell" -ArgumentList "-Command", "Set-Location spug_api; .\venv\Scripts\activate; python manage.py runserver"

    # 启动前端
    Write-ColorOutput Green "正在启动前端服务..."
    Set-Location spug_web
    npm start
}

# 主函数
function Main {
    # 检查环境状态
    $status = Check-EnvironmentStatus
    if ($status.NeedClean) {
        Write-ColorOutput Yellow "检测到以下问题："
        foreach ($r in $status.Reason) {
            Write-ColorOutput Yellow "- $r"
        }
        
        if (-not (Clean-Environment -Force:$Force)) {
            Write-ColorOutput Red "环境清理被取消，退出安装"
            return
        }
    }

    # 安装必要工具
    if (-not (Install-Scoop)) { return }
    if (-not (Install-Python)) { return }
    if (-not (Install-Node)) { return }

    # 安装依赖
    if (-not (Install-Dependencies)) {
        Write-ColorOutput Red "依赖安装失败，请检查错误信息"
        return
    }

    # 启动服务
    Start-Services
}

# 执行主函数
Main