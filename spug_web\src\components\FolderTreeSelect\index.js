/**
 * 通用文件夹树选择组件
 * 支持本地文件夹和远程文件夹的树形选择
 */
import React, { useState, useEffect } from 'react';
import { TreeSelect, message } from 'antd';
import { FolderOutlined, HomeOutlined, CloudServerOutlined } from '@ant-design/icons';
import { http } from 'libs';

const FolderTreeSelect = ({ 
  value, 
  onChange, 
  placeholder = "选择目标文件夹", 
  allowClear = true,
  showSearch = true,
  style = { width: '100%' },
  mode = 'local', // 'local' | 'remote' | 'both'
  disabled = false,
  dropdownStyle = { maxHeight: 400, overflow: 'auto' }
}) => {
  const [treeData, setTreeData] = useState([]);
  const [loading, setLoading] = useState(false);

  // 获取本地文件夹树
  const getLocalFolderTree = async () => {
    try {
      console.log('🌳 获取本地文件夹树');
      const response = await http.get('/api/file/tree/');
      
      // 获取树数据，处理API响应格式
      const treeData = response.tree || response;
      
      if (!Array.isArray(treeData)) {
        console.error('❌ 树数据不是数组格式:', treeData);
        return [{
          title: (
            <span>
              <HomeOutlined style={{ color: '#52c41a', marginRight: 8 }} />
              根目录
            </span>
          ),
          value: '',
          key: 'root',
          children: []
        }];
      }
      
      // 过滤出文件夹节点并构建树形数据
      const buildTree = (nodes, parentPath = '') => {
        return nodes
          .filter(node => node.type === 'folder')
          .map(node => {
            const fullPath = parentPath ? `${parentPath}/${node.title}` : node.title;
            return {
              title: (
                <span>
                  <FolderOutlined style={{ color: '#faad14', marginRight: 8 }} />
                  {node.title}
                </span>
              ),
              value: fullPath,
              key: fullPath,
              children: node.children && node.children.length > 0 
                ? buildTree(node.children, fullPath) 
                : undefined
            };
          });
      };

      const localTree = buildTree(treeData);
      
      // 添加根目录选项
      return [{
        title: (
          <span>
            <HomeOutlined style={{ color: '#52c41a', marginRight: 8 }} />
            根目录
          </span>
        ),
        value: '',
        key: 'root',
        children: localTree
      }];
    } catch (error) {
      console.error('获取本地文件夹失败:', error);
      message.error('获取本地文件夹失败');
      return [];
    }
  };

  // 获取远程文件夹
  const getRemoteFolders = async () => {
    try {
      console.log('📡 获取远程文件夹列表');
      const response = await http.get('/api/file/remote/');
      
      return response.map(folder => ({
        title: (
          <span>
            <CloudServerOutlined style={{ color: '#1890ff', marginRight: 8 }} />
            {folder.name}
          </span>
        ),
        value: `remote:${folder.id}`,
        key: `remote:${folder.id}`,
        isLeaf: true,
        folder_info: folder
      }));
    } catch (error) {
      console.error('获取远程文件夹失败:', error);
      message.error('获取远程文件夹失败');
      return [];
    }
  };

  // 加载树形数据
  const loadTreeData = async () => {
    setLoading(true);
    try {
      let data = [];
      
      if (mode === 'local' || mode === 'both') {
        const localData = await getLocalFolderTree();
        data = [...data, ...localData];
      }
      
      if (mode === 'remote' || mode === 'both') {
        const remoteData = await getRemoteFolders();
        if (remoteData.length > 0) {
          // 为远程文件夹添加分组
          data.push({
            title: (
              <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
                <CloudServerOutlined style={{ marginRight: 8 }} />
                远程文件夹
              </span>
            ),
            value: 'remote_group',
            key: 'remote_group',
            disabled: true,
            children: remoteData
          });
        }
      }
      
      setTreeData(data);
    } catch (error) {
      console.error('加载文件夹树失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTreeData();
  }, [mode]);

  const handleChange = (selectedValue, label, extra) => {
    
    if (onChange) {
      // 如果是远程文件夹，传递额外的文件夹信息
      if (selectedValue && selectedValue.startsWith('remote:')) {
        const folderId = selectedValue.replace('remote:', '');
        // 只传递序列化安全的数据，完全避免DOM元素引用
        const folderInfo = extra?.triggerNode?.props?.folder_info || extra?.node?.folder_info;
        const safeExtraData = {
          type: 'remote',
          folderId: parseInt(folderId),
          folderInfo: folderInfo ? {
            id: folderInfo.id,
            name: folderInfo.name,
            remote_path: folderInfo.remote_path
          } : null
        };
        onChange(selectedValue, safeExtraData);
      } else {
        const safeExtraData = {
          type: 'local',
          path: selectedValue || ''
        };
        onChange(selectedValue, safeExtraData);
      }
    }
  };

  const filterTreeNode = (inputValue, treeNode) => {
    if (!inputValue) return true;
    
    // 获取节点的文本内容
    const nodeText = treeNode.title?.props?.children?.[1] || treeNode.title || '';
    return nodeText.toLowerCase().includes(inputValue.toLowerCase());
  };

  return (
    <TreeSelect
      value={value}
      onChange={handleChange}
      treeData={treeData}
      placeholder={placeholder}
      allowClear={allowClear}
      showSearch={showSearch}
      style={style}
      disabled={disabled}
      loading={loading}
      dropdownStyle={dropdownStyle}
      treeDefaultExpandAll={false}
      treeDefaultExpandedKeys={['root']}
      showCheckedStrategy={TreeSelect.SHOW_PARENT}
      filterTreeNode={filterTreeNode}
      treeNodeFilterProp="title"
    />
  );
};

export default FolderTreeSelect; 