# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.

import logging
import time
import json
import hashlib
import concurrent.futures
import threading
from typing import List, Dict, Optional, Tuple, Set
from datetime import datetime, timedelta
from django.core.cache import caches
# from django.core.cache.backends.redis import RedisCache
from django_redis import get_redis_connection
from django.utils import timezone
from django.conf import settings
from apps.file.models import RemoteFolder
from apps.file.remote_manager import RemoteFileManager

logger = logging.getLogger(__name__)

class RedisRemoteFolderCacheService:
    """基于Redis的远程文件夹缓存服务"""
    
    def __init__(self):
        self.lock = threading.Lock()
        self.cache_expire_seconds = getattr(settings, 'REMOTE_FOLDER_CACHE_EXPIRE_MINUTES', 30) * 60
        self.max_depth = 3  # 最大递归深度
        self.max_workers = 3  # 最大线程数，减少以避免数据库锁定
        
        # 获取专用Redis缓存连接
        try:
            self.cache = caches['remote_file_cache']
            self.redis_client = get_redis_connection('remote_file_cache')
            logger.info("Redis远程文件缓存服务初始化成功")
        except Exception as e:
            logger.error(f"Redis缓存初始化失败: {e}")
            raise
        
        # Redis key前缀
        self.cache_key_prefix = "spug:remote_files"
        self.status_key_prefix = "spug:remote_files:status"
        self.metadata_key_prefix = "spug:remote_files:meta"
        
    def _make_cache_key(self, folder_id: int, path: str = "") -> str:
        """生成缓存key"""
        # 使用folder_id和path生成唯一key
        path_hash = hashlib.md5(path.encode('utf-8')).hexdigest()
        return f"{self.cache_key_prefix}:{folder_id}:{path_hash}"
    
    def _make_status_key(self, folder_id: int) -> str:
        """生成状态key"""
        return f"{self.status_key_prefix}:{folder_id}"
    
    def _make_metadata_key(self, folder_id: int, path: str = "") -> str:
        """生成元数据key"""
        path_hash = hashlib.md5(path.encode('utf-8')).hexdigest()
        return f"{self.metadata_key_prefix}:{folder_id}:{path_hash}"
    
    def get_cached_files(self, folder_id: int, path: str = "") -> Tuple[List[Dict], bool]:
        """获取缓存的文件列表"""
        try:
            cache_key = self._make_cache_key(folder_id, path)
            
            # 尝试从Redis获取缓存数据
            # 先尝试使用Django Cache API
            cached_data = None
            try:
                cached_data = self.cache.get(cache_key)
            except Exception as cache_error:
                logger.debug(f"Django cache获取失败，尝试直接使用Redis: {cache_error}")
                # 如果Django cache失败，直接使用redis客户端
                try:
                    raw_data = self.redis_client.get(cache_key)
                    if raw_data:
                        cached_data = json.loads(raw_data)
                except Exception as redis_error:
                    logger.debug(f"直接Redis获取也失败: {redis_error}")
            
            if cached_data:
                logger.debug(f"命中缓存: folder_id={folder_id}, path={path}")
                return cached_data.get('files', []), True
            
            # 缓存miss，实时获取数据
            logger.debug(f"缓存未命中，实时获取: folder_id={folder_id}, path={path}")
            files = self._fetch_and_cache_files(folder_id, path)
            return files, False
            
        except Exception as e:
            logger.error(f"获取缓存文件失败: {e}")
            return [], False
    
    def _fetch_and_cache_files(self, folder_id: int, path: str = "") -> List[Dict]:
        """获取并缓存文件列表"""
        try:
            # 获取远程文件夹配置
            folder = RemoteFolder.objects.get(id=folder_id, is_active=True)
            
            # 创建远程管理器
            remote_manager = RemoteFileManager(
                folder.remote_path,
                folder.username,
                folder.password,
                folder.domain
            )
            
            if not remote_manager.connect():
                logger.error(f"无法连接远程路径: {folder.remote_path}")
                return []
            
            try:
                # 获取文件列表
                files = remote_manager.list_files(path)
                
                # 保存到Redis缓存
                self._save_to_cache(folder_id, path, files)
                
                logger.info(f"成功缓存文件列表: folder_id={folder_id}, path={path}, count={len(files)}")
                return files
                
            finally:
                remote_manager.disconnect()
                
        except Exception as e:
            logger.error(f"获取文件列表失败: {e}")
            return []
    
    def _save_to_cache(self, folder_id: int, path: str, files: List[Dict]):
        """保存数据到Redis缓存"""
        try:
            cache_key = self._make_cache_key(folder_id, path)
            metadata_key = self._make_metadata_key(folder_id, path)
            
            # 准备缓存数据
            cache_data = {
                'files': files,
                'cached_at': timezone.now().isoformat(),
                'folder_id': folder_id,
                'path': path,
                'file_count': len(files)
            }
            
            # 准备元数据
            metadata = {
                'folder_id': folder_id,
                'path': path,
                'cached_at': timezone.now().isoformat(),
                'file_count': len(files),
                'cache_key': cache_key
            }
            
            # 使用Redis管道提高性能
            pipe = self.redis_client.pipeline()
            
            # 保存主要缓存数据
            pipe.setex(cache_key, self.cache_expire_seconds, json.dumps(cache_data, ensure_ascii=False))
            
            # 保存元数据（用于管理和统计）
            pipe.setex(metadata_key, self.cache_expire_seconds, json.dumps(metadata, ensure_ascii=False))
            
            # 更新文件夹状态
            status_key = self._make_status_key(folder_id)
            status_data = {
                'last_cache_time': timezone.now().isoformat(),
                'total_paths': self._get_path_count(folder_id) + 1,
                'last_path': path
            }
            pipe.setex(status_key, self.cache_expire_seconds, json.dumps(status_data, ensure_ascii=False))
            
            # 执行管道
            pipe.execute()
            
        except Exception as e:
            logger.error(f"保存缓存失败: {e}")
    
    def _get_path_count(self, folder_id: int) -> int:
        """获取指定文件夹的缓存路径数量"""
        try:
            pattern = f"{self.metadata_key_prefix}:{folder_id}:*"
            keys = self.redis_client.keys(pattern)
            return len(keys)
        except Exception as e:
            logger.error(f"获取路径数量失败: {e}")
            return 0
    
    def refresh_cache(self, folder_id: int, path: str = "") -> bool:
        """强制刷新缓存"""
        try:
            # 先删除旧缓存
            self.clear_cache(folder_id, path)
            
            # 重新获取并缓存
            files = self._fetch_and_cache_files(folder_id, path)
            return len(files) >= 0  # 即使空目录也算成功
            
        except Exception as e:
            logger.error(f"刷新缓存失败: {e}")
            return False
    
    def clear_cache(self, folder_id: int = None, path: str = None):
        """清理缓存"""
        try:
            if folder_id is None:
                # 清理所有缓存
                patterns = [
                    f"{self.cache_key_prefix}:*",
                    f"{self.metadata_key_prefix}:*",
                    f"{self.status_key_prefix}:*"
                ]
                deleted_count = 0
                for pattern in patterns:
                    keys = self.redis_client.keys(pattern)
                    if keys:
                        deleted_count += self.redis_client.delete(*keys)
                
                logger.info(f"清理所有缓存完成，删除 {deleted_count} 个key")
                return deleted_count
            
            elif path is None:
                # 清理指定文件夹的所有缓存
                patterns = [
                    f"{self.cache_key_prefix}:{folder_id}:*",
                    f"{self.metadata_key_prefix}:{folder_id}:*",
                    f"{self.status_key_prefix}:{folder_id}"
                ]
                deleted_count = 0
                for pattern in patterns:
                    keys = self.redis_client.keys(pattern)
                    if keys:
                        deleted_count += self.redis_client.delete(*keys)
                
                logger.info(f"清理文件夹 {folder_id} 缓存完成，删除 {deleted_count} 个key")
                return deleted_count
            
            else:
                # 清理指定路径的缓存
                cache_key = self._make_cache_key(folder_id, path)
                metadata_key = self._make_metadata_key(folder_id, path)
                
                deleted_count = 0
                for key in [cache_key, metadata_key]:
                    if self.redis_client.delete(key):
                        deleted_count += 1
                
                logger.info(f"清理指定路径缓存完成: folder_id={folder_id}, path={path}")
                return deleted_count
                
        except Exception as e:
            logger.error(f"清理缓存失败: {e}")
            return 0
    
    def cache_entire_file_tree(self, folder_id: int) -> Dict:
        """递归缓存整个文件树"""
        logger.info(f"开始缓存整个文件树: folder_id={folder_id}")
        
        stats = {
            'total_paths': 0,
            'success_paths': 0,
            'failed_paths': 0,
            'start_time': timezone.now().isoformat(),
            'folder_id': folder_id
        }
        
        def cache_recursive(current_path: str = "", depth: int = 0):
            if depth > self.max_depth:
                logger.warning(f"达到最大递归深度: {current_path}")
                return
            
            try:
                stats['total_paths'] += 1
                
                # 获取并缓存当前路径
                files = self._fetch_and_cache_files(folder_id, current_path)
                
                if files is not None:
                    stats['success_paths'] += 1
                    
                    # 递归处理子文件夹
                    for file_item in files:
                        if file_item.get('type') == 'folder':
                            subfolder_path = f"{current_path}/{file_item['name']}" if current_path else file_item['name']
                            cache_recursive(subfolder_path, depth + 1)
                else:
                    stats['failed_paths'] += 1
                    
            except Exception as e:
                logger.error(f"缓存路径失败: {current_path}, 错误: {e}")
                stats['failed_paths'] += 1
        
        # 从根路径开始递归
        cache_recursive()
        
        stats['end_time'] = timezone.now().isoformat()
        stats['duration'] = str(timezone.now() - datetime.fromisoformat(stats['start_time']))
        
        logger.info(f"文件树缓存完成: {stats}")
        return stats
    
    def refresh_folder_cache(self, folder_id: int, path: str = "", recursive: bool = False) -> Dict:
        """刷新文件夹缓存（统一接口）"""
        if recursive:
            return self.cache_entire_file_tree(folder_id)
        else:
            success = self.refresh_cache(folder_id, path)
            return {
                'success': success,
                'folder_id': folder_id,
                'path': path,
                'recursive': False
            }
    
    def get_cache_status(self) -> Dict:
        """获取缓存系统状态"""
        try:
            # 统计各种类型的key数量
            cache_keys = self.redis_client.keys(f"{self.cache_key_prefix}:*")
            metadata_keys = self.redis_client.keys(f"{self.metadata_key_prefix}:*")
            status_keys = self.redis_client.keys(f"{self.status_key_prefix}:*")
            
            # 获取Redis内存信息
            redis_info = self.redis_client.info('memory')
            
            # 获取活跃的远程文件夹
            active_folders = RemoteFolder.objects.filter(is_active=True).count()
            
            # 统计每个文件夹的缓存情况
            folder_stats = {}
            for status_key in status_keys:
                try:
                    status_data = json.loads(self.redis_client.get(status_key))
                    folder_id = status_key.split(':')[-1]
                    folder_stats[folder_id] = status_data
                except Exception as e:
                    logger.debug(f"解析状态key失败: {status_key}, {e}")
            
            return {
                'redis_connected': True,
                'cache_keys_count': len(cache_keys),
                'metadata_keys_count': len(metadata_keys),
                'active_folders': active_folders,
                'folder_with_cache': len(status_keys),
                'redis_memory_used': redis_info.get('used_memory_human', 'N/A'),
                'redis_memory_peak': redis_info.get('used_memory_peak_human', 'N/A'),
                'folder_stats': folder_stats,
                'cache_expire_seconds': self.cache_expire_seconds,
                'last_update': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取缓存状态失败: {e}")
            return {
                'redis_connected': False,
                'error': str(e),
                'last_update': timezone.now().isoformat()
            }
    
    def refresh_all_caches(self) -> Dict:
        """刷新所有活跃文件夹的缓存"""
        logger.info("开始刷新所有远程文件夹缓存...")
        
        stats = {
            'total_folders': 0,
            'success_folders': 0,
            'failed_folders': 0,
            'start_time': timezone.now().isoformat(),
            'errors': []
        }
        
        try:
            # 获取所有活跃的远程文件夹
            active_folders = RemoteFolder.objects.filter(is_active=True)
            stats['total_folders'] = active_folders.count()
            
            # 使用线程池并发刷新
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_folder = {
                    executor.submit(self.refresh_cache, folder.id): folder 
                    for folder in active_folders
                }
                
                # 等待任务完成
                for future in concurrent.futures.as_completed(future_to_folder):
                    folder = future_to_folder[future]
                    try:
                        success = future.result(timeout=30)  # 30秒超时
                        if success:
                            stats['success_folders'] += 1
                        else:
                            stats['failed_folders'] += 1
                            stats['errors'].append(f"文件夹 {folder.name} (ID: {folder.id}) 刷新失败")
                    except Exception as e:
                        stats['failed_folders'] += 1
                        error_msg = f"文件夹 {folder.name} (ID: {folder.id}) 刷新异常: {str(e)}"
                        stats['errors'].append(error_msg)
                        logger.error(error_msg)
        
        except Exception as e:
            error_msg = f"刷新所有缓存异常: {str(e)}"
            stats['errors'].append(error_msg)
            logger.error(error_msg)
        
        stats['end_time'] = timezone.now().isoformat()
        stats['duration'] = str(timezone.now() - datetime.fromisoformat(stats['start_time']))
        
        logger.info(f"所有缓存刷新完成: {stats}")
        return stats
    
    def clear_expired_caches(self) -> int:
        """清理过期缓存（Redis会自动过期，这里主要用于统计）"""
        try:
            # Redis会自动清理过期key，这里主要返回当前存在的key数量
            cache_keys = self.redis_client.keys(f"{self.cache_key_prefix}:*")
            logger.info(f"当前有效缓存key数量: {len(cache_keys)}")
            return len(cache_keys)
        except Exception as e:
            logger.error(f"检查过期缓存失败: {e}")
            return 0

# 创建全局缓存服务实例
_redis_cache_service = None

def get_redis_cache_service():
    """获取Redis缓存服务实例（单例模式）"""
    global _redis_cache_service
    if _redis_cache_service is None:
        _redis_cache_service = RedisRemoteFolderCacheService()
    return _redis_cache_service

# 向后兼容的代理类
class CacheServiceProxy:
    """向后兼容的缓存服务代理"""
    def __getattr__(self, name):
        return getattr(get_redis_cache_service(), name)

# 导出缓存服务实例
cache_service = CacheServiceProxy() 