# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.executors.pool import ThreadPoolExecutor
from django.conf import settings
from apps.file.redis_cache_service import cache_service
from apps.file.models import RemoteFolder
import logging
import time

logger = logging.getLogger(__name__)


class RemoteCacheScheduler:
    """远程文件夹缓存定时调度器"""
    timezone = settings.TIME_ZONE
    
    def __init__(self):
        self.scheduler = BackgroundScheduler(
            timezone=self.timezone, 
            executors={'default': ThreadPoolExecutor(5)}
        )
    
    def _refresh_all_caches(self):
        """刷新所有缓存的定时任务"""
        try:
            logger.info("开始定时刷新远程文件夹缓存...")
            stats = cache_service.refresh_all_caches()
            logger.info(f"缓存刷新完成: {stats}")
        except Exception as e:
            logger.error(f"定时刷新缓存失败: {e}")
    
    def _clear_expired_caches(self):
        """清理过期缓存的定时任务"""
        try:
            count = cache_service.clear_expired_caches()
            if count > 0:
                logger.info(f"定时清理过期缓存: {count} 条")
        except Exception as e:
            logger.error(f"定时清理过期缓存失败: {e}")
    
    def _preload_file_trees(self):
        """预加载所有远程文件夹的文件树"""
        try:
            logger.info("开始预加载所有远程文件夹文件树...")
            
            active_folders = RemoteFolder.objects.filter(is_active=True)
            total_folders = active_folders.count()
            
            if total_folders == 0:
                logger.info("没有活动的远程文件夹，跳过预加载")
                return
                
            logger.info(f"发现 {total_folders} 个活动的远程文件夹，开始预加载文件树")
            
            success_folders = 0
            failed_folders = 0
            
            for folder in active_folders:
                try:
                    logger.info(f"预加载文件夹: {folder.name}")
                    stats = cache_service.cache_entire_file_tree(folder.id)
                    
                    if 'error' not in stats:
                        success_folders += 1
                        logger.info(f"文件夹预加载成功: {folder.name}, 路径数: {stats.get('total_paths')}")
                    else:
                        failed_folders += 1
                        logger.error(f"文件夹预加载失败: {folder.name}, 错误: {stats.get('error')}")
                        
                except Exception as e:
                    failed_folders += 1
                    logger.error(f"文件夹预加载异常: {folder.name} - {e}")
            
            logger.info(f"文件树预加载完成: 总数={total_folders}, 成功={success_folders}, 失败={failed_folders}")
            
        except Exception as e:
            logger.error(f"预加载文件树失败: {e}")
    
    def _init(self):
        """初始化定时任务"""
        # 每5分钟刷新一次所有远程文件夹缓存
        self.scheduler.add_job(
            self._refresh_all_caches,
            'interval',
            minutes=5,
            id='refresh_remote_caches'
        )
        
        # 每小时清理一次过期缓存
        self.scheduler.add_job(
            self._clear_expired_caches,
            'interval',
            hours=1,
            id='clear_expired_caches'
        )
    
    def run(self):
        """启动调度器"""
        self._init()
        self.scheduler.start()
        logger.info("远程文件夹缓存调度器已启动")
        
        # 启动后延迟几秒预加载文件树，避免与其他启动任务冲突
        logger.info("计划在10秒后开始预加载文件树...")
        time.sleep(10)
        self._preload_file_trees()


# 创建全局实例
remote_cache_scheduler = RemoteCacheScheduler() 