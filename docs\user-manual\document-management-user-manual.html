<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档管理系统 - 用户手册</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .version-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-top: 20px;
        }

        /* 导航栏 */
        .nav {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: #2c3e50;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #667eea;
        }

        /* 主要内容区域 */
        .main-content {
            padding: 60px 0;
        }

        .section {
            margin-bottom: 80px;
        }

        .section-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 50px;
        }

        /* 功能卡片 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            border: 1px solid #e9ecef;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #667eea;
        }

        .feature-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .feature-description {
            color: #7f8c8d;
            line-height: 1.6;
        }

        /* 步骤指南 */
        .steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .step {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            position: relative;
            border: 2px solid #e9ecef;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .step:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .step-number {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .step-title {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .step-description {
            color: #7f8c8d;
        }

        /* 截图样式 */
        .screenshot {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            text-align: center;
            border: 2px dashed #dee2e6;
        }

        .screenshot img {
            max-width: 100%;
            border-radius: 8px;
        }

        .screenshot-placeholder {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .screenshot-caption {
            color: #6c757d;
            font-style: italic;
        }

        /* 代码块样式 */
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
            overflow-x: auto;
        }

        /* 提示框 */
        .tip, .warning, .info {
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid;
        }

        .tip {
            background: #f0f9ff;
            border-color: #0ea5e9;
            color: #0c4a6e;
        }

        .warning {
            background: #fef3c7;
            border-color: #f59e0b;
            color: #92400e;
        }

        .info {
            background: #ecfdf5;
            border-color: #10b981;
            color: #065f46;
        }

        /* 底部 */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .steps {
                grid-template-columns: 1fr;
            }
        }
        
        /* FAQ样式 */
        .faq-item {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .faq-question {
            color: #667eea;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .faq-answer {
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1><i class="fas fa-file-alt"></i> 文档管理系统</h1>
                <p>智能化的Word文档模板管理与自动生成平台</p>
                <div class="version-badge">v1.0 用户手册</div>
            </div>
        </div>
    </header>

    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container">
            <div class="nav-content">
                <div class="logo">
                    <strong>用户手册</strong>
                </div>
                <ul class="nav-links">
                    <li><a href="#overview">系统概述</a></li>
                    <li><a href="#features">功能介绍</a></li>
                    <li><a href="#getting-started">快速开始</a></li>
                    <li><a href="#tutorials">使用教程</a></li>
                    <li><a href="#faq">常见问题</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 系统概述 -->
            <section id="overview" class="section">
                <h2 class="section-title">系统概述</h2>
                <p class="section-subtitle">文档管理系统是一个专业的Word文档模板管理平台，提供模板创建、变量配置、文档生成和实例管理功能</p>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-file-word"></i>
                        </div>
                        <h3 class="feature-title">模板管理</h3>
                        <p class="feature-description">支持Word模板的上传、编辑、版本控制和状态管理，提供完整的模板生命周期管理</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3 class="feature-title">变量配置</h3>
                        <p class="feature-description">灵活的模板变量定义，支持文本、数字、日期、选择等多种类型，提供默认值和验证规则</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-magic"></i>
                        </div>
                        <h3 class="feature-title">自动生成</h3>
                        <p class="feature-description">基于模板和变量值自动生成Word文档，支持富文本编辑和实时预览</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-folder-open"></i>
                        </div>
                        <h3 class="feature-title">实例管理</h3>
                        <p class="feature-description">管理生成的文档实例，支持草稿保存、状态跟踪和文件下载</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="feature-title">统计分析</h3>
                        <p class="feature-description">提供模板使用统计、文档生成报告和使用趋势分析</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">协作管理</h3>
                        <p class="feature-description">支持多用户协作，权限控制和操作日志记录</p>
                    </div>
                </div>
            </section>

            <!-- 功能介绍 -->
            <section id="features" class="section">
                <h2 class="section-title">功能介绍</h2>
                <p class="section-subtitle">详细了解系统的各项功能特性</p>

                <!-- 模板管理 -->
                <div class="feature-detail">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-file-word"></i> 模板管理
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        提供完整的Word模板生命周期管理，支持模板上传、编辑、版本控制和状态管理。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            模板管理界面截图
                        </div>
                        <p class="screenshot-caption">模板列表显示所有可用模板，支持状态筛选和批量操作</p>
                    </div>

                    <div class="info">
                        <strong>主要功能：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>Word模板文件上传和导入</li>
                            <li>模板基本信息编辑（名称、描述、类型）</li>
                            <li>模板状态管理（启用、禁用、草稿）</li>
                            <li>模板变量定义和配置</li>
                            <li>使用统计和历史记录</li>
                            <li>模板复制和版本管理</li>
                        </ul>
                    </div>
                </div>

                <!-- 变量配置 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-cogs"></i> 变量配置
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        灵活的模板变量系统，支持多种数据类型和验证规则，提供默认值和分组管理。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            变量配置界面截图
                        </div>
                        <p class="screenshot-caption">变量编辑器支持拖拽排序和分组管理</p>
                    </div>

                    <div class="info">
                        <strong>支持的变量类型：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li><strong>文本类型：</strong>单行文本、多行文本、富文本</li>
                            <li><strong>数值类型：</strong>整数、小数、百分比</li>
                            <li><strong>日期类型：</strong>日期、时间、日期时间</li>
                            <li><strong>选择类型：</strong>单选、多选、下拉列表</li>
                            <li><strong>布尔类型：</strong>是/否、开关</li>
                            <li><strong>文件类型：</strong>图片、附件</li>
                        </ul>
                    </div>
                </div>

                <!-- 文档生成 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-magic"></i> 文档生成
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        基于模板和变量值自动生成Word文档，支持实时预览和富文本编辑。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            文档生成器界面截图
                        </div>
                        <p class="screenshot-caption">文档生成器提供变量填写和富文本编辑功能</p>
                    </div>

                    <div class="info">
                        <strong>生成流程：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>选择模板并填写变量值</li>
                            <li>使用富文本编辑器补充内容</li>
                            <li>实时预览生成效果</li>
                            <li>一键生成Word文档</li>
                            <li>自动保存为文档实例</li>
                            <li>支持下载和分享</li>
                        </ul>
                    </div>
                </div>

                <!-- 实例管理 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-folder-open"></i> 实例管理
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        管理所有生成的文档实例，提供状态跟踪、版本控制和文件管理功能。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            文档实例管理界面截图
                        </div>
                        <p class="screenshot-caption">实例列表显示所有文档的状态和基本信息</p>
                    </div>

                    <div class="info">
                        <strong>实例状态：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li><strong>草稿：</strong>正在编辑中的文档</li>
                            <li><strong>已生成：</strong>已完成生成的文档</li>
                            <li><strong>已导出：</strong>已下载或分享的文档</li>
                            <li><strong>已归档：</strong>不再使用的历史文档</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 快速开始 -->
            <section id="getting-started" class="section">
                <h2 class="section-title">快速开始</h2>
                <p class="section-subtitle">按照以下步骤快速上手使用系统</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">上传模板</h3>
                        <p class="step-description">进入模板管理页面，上传Word模板文件，设置基本信息</p>
                    </div>

                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">配置变量</h3>
                        <p class="step-description">为模板定义变量，设置类型、默认值和验证规则</p>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">生成文档</h3>
                        <p class="step-description">使用文档生成器填写变量值，生成Word文档</p>
                    </div>

                    <div class="step">
                        <div class="step-number">4</div>
                        <h3 class="step-title">管理实例</h3>
                        <p class="step-description">在实例管理中查看、编辑和下载生成的文档</p>
                    </div>
                </div>
            </section>

            <!-- 使用教程 -->
            <section id="tutorials" class="section">
                <h2 class="section-title">使用教程</h2>
                <p class="section-subtitle">详细的操作指南和最佳实践</p>

                <!-- 创建模板教程 -->
                <div class="tutorial">
                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #2c3e50;">
                        📄 如何创建Word模板
                    </h3>

                    <div class="steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <h4 class="step-title">准备模板文件</h4>
                            <p class="step-description">在Word中创建模板，使用{{变量名}}标记需要替换的内容</p>
                        </div>

                        <div class="step">
                            <div class="step-number">2</div>
                            <h4 class="step-title">上传模板</h4>
                            <p class="step-description">在模板管理页面点击"上传模板"，选择Word文件</p>
                        </div>

                        <div class="step">
                            <div class="step-number">3</div>
                            <h4 class="step-title">设置基本信息</h4>
                            <p class="step-description">填写模板名称、描述和类型信息</p>
                        </div>

                        <div class="step">
                            <div class="step-number">4</div>
                            <h4 class="step-title">配置变量</h4>
                            <p class="step-description">为模板中的变量设置类型、默认值和验证规则</p>
                        </div>
                    </div>

                    <div class="tip">
                        <strong>💡 模板变量语法：</strong> 在Word文档中使用双大括号包围变量名，如{{项目名称}}、{{开始日期}}等。系统会自动识别这些变量并生成配置界面。
                    </div>
                </div>

                <!-- 变量配置教程 -->
                <div class="tutorial" style="margin-top: 50px;">
                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #2c3e50;">
                        ⚙️ 变量配置详解
                    </h3>

                    <h4 style="color: #667eea; margin-bottom: 15px;">变量类型选择</h4>
                    <ul style="padding-left: 20px; color: #7f8c8d; margin-bottom: 20px;">
                        <li><strong>文本：</strong>适用于姓名、标题等短文本</li>
                        <li><strong>多行文本：</strong>适用于描述、备注等长文本</li>
                        <li><strong>数字：</strong>适用于数量、金额等数值</li>
                        <li><strong>日期：</strong>适用于时间相关的变量</li>
                        <li><strong>选择：</strong>适用于有固定选项的变量</li>
                    </ul>

                    <h4 style="color: #667eea; margin-bottom: 15px; margin-top: 30px;">默认值设置</h4>
                    <ul style="padding-left: 20px; color: #7f8c8d; margin-bottom: 20px;">
                        <li>为常用变量设置默认值，提高填写效率</li>
                        <li>日期类型可以设置为"今天"、"明天"等相对日期</li>
                        <li>选择类型需要预先定义所有可选项</li>
                    </ul>

                    <div class="warning">
                        <strong>⚠️ 注意：</strong> 变量名称必须与Word模板中的标记完全一致，区分大小写。建议使用有意义的中文名称，便于理解和维护。
                    </div>
                </div>

                <!-- 文档生成教程 -->
                <div class="tutorial" style="margin-top: 50px;">
                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #2c3e50;">
                        🎯 文档生成流程
                    </h3>

                    <p style="color: #7f8c8d; margin-bottom: 20px;">
                        文档生成器提供直观的界面，帮助用户快速生成专业的Word文档。
                    </p>

                    <div class="steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <h4 class="step-title">选择模板</h4>
                            <p class="step-description">从模板列表中选择合适的模板</p>
                        </div>

                        <div class="step">
                            <div class="step-number">2</div>
                            <h4 class="step-title">填写变量</h4>
                            <p class="step-description">根据提示填写所有必需的变量值</p>
                        </div>

                        <div class="step">
                            <div class="step-number">3</div>
                            <h4 class="step-title">编辑内容</h4>
                            <p class="step-description">使用富文本编辑器补充或修改内容</p>
                        </div>

                        <div class="step">
                            <div class="step-number">4</div>
                            <h4 class="step-title">生成下载</h4>
                            <p class="step-description">点击生成按钮，系统自动生成并提供下载</p>
                        </div>
                    </div>

                    <div class="info">
                        <strong>📈 生成特性：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>支持实时预览，所见即所得</li>
                            <li>自动保存草稿，防止数据丢失</li>
                            <li>支持批量生成多个文档</li>
                            <li>生成历史记录，便于追溯</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 常见问题 -->
            <section id="faq" class="section">
                <h2 class="section-title">常见问题</h2>
                <p class="section-subtitle">解答使用过程中的常见问题</p>

                <div style="max-width: 800px; margin: 0 auto;">
                    <div class="faq-item">
                        <h4 class="faq-question">Q: 支持哪些Word文档格式？</h4>
                        <p class="faq-answer">A: 系统支持.docx和.doc格式的Word文档。推荐使用.docx格式，兼容性更好，功能更完整。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 模板变量的命名有什么要求？</h4>
                        <p class="faq-answer">A: 变量名应使用中文或英文，避免特殊字符。在Word模板中使用{{变量名}}格式标记，变量名区分大小写。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何在模板中插入图片变量？</h4>
                        <p class="faq-answer">A: 在Word模板中使用{{图片变量名}}标记位置，然后在变量配置中将该变量设置为"图片"类型。生成时用户可以上传图片文件。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 生成的文档可以再次编辑吗？</h4>
                        <p class="faq-answer">A: 可以。在文档实例管理中点击"编辑"按钮，可以修改变量值和富文本内容，然后重新生成文档。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何批量生成多个文档？</h4>
                        <p class="faq-answer">A: 目前支持基于同一模板生成多个文档实例。可以通过复制现有实例，然后修改变量值的方式实现批量生成。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 模板变量的默认值如何设置？</h4>
                        <p class="faq-answer">A: 在模板编辑页面的变量配置中，每个变量都可以设置默认值。日期类型支持相对日期如"今天"、"下周一"等。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 生成的文档保存在哪里？</h4>
                        <p class="faq-answer">A: 生成的文档保存在服务器上，可以通过文档实例管理页面查看和下载。系统会自动记录生成时间和文件大小。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何删除不需要的模板？</h4>
                        <p class="faq-answer">A: 在模板管理页面，点击对应模板的"删除"按钮。注意：删除模板会影响基于该模板创建的所有文档实例，请谨慎操作。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 富文本编辑器支持哪些功能？</h4>
                        <p class="faq-answer">A: 富文本编辑器支持文字格式化、插入表格、添加链接、插入图片等常用功能。编辑的内容会与模板变量一起合并到最终文档中。</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 底部 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 文档管理系统. 版权所有.</p>
            <p style="margin-top: 10px; opacity: 0.8;">
                如有问题请联系系统管理员 |
                <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a>
            </p>
        </div>
    </footer>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航栏高亮
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('.nav');
            if (window.scrollY > 100) {
                nav.style.background = 'rgba(255, 255, 255, 0.95)';
                nav.style.backdropFilter = 'blur(10px)';
            } else {
                nav.style.background = 'white';
                nav.style.backdropFilter = 'none';
            }
        });
    </script>
</body>
</html>
