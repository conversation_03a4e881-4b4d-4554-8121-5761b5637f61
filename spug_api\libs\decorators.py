# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from functools import wraps
from .utils import json_response


def auth(perm_list):
    def decorate(view_func):
        # 处理perm_list为字符串或functools.partial对象的情况
        if hasattr(perm_list, 'split'):
            codes = perm_list.split('|')
        else:
            # 如果perm_list不是字符串（可能是functools.partial对象），使用空列表
            codes = []

        @wraps(view_func)
        def wrapper(*args, **kwargs):
            user = None
            request = None

            # 获取request对象
            for item in args[:2]:
                if hasattr(item, 'user'):
                    user = item.user
                    request = item
                    break
                elif hasattr(item, 'method'):  # 这可能是request对象
                    request = item
                    if hasattr(item, 'user'):
                        user = item.user
                    break

            # 如果request没有user属性，创建DeveloperUser（用于model-storage内部系统）
            if request and not hasattr(request, 'user'):
                from libs.middleware import DeveloperUser
                request.user = DeveloperUser()
                user = request.user

            # 权限检查
            if user and user.has_perms(codes):
                return view_func(*args, **kwargs)
            return json_response(error='权限拒绝')

        return wrapper

    return decorate


def has_permission(user, perm_code):
    """检查用户是否有指定权限"""
    if not user:
        return False

    # 如果user没有has_perms方法，创建DeveloperUser（用于model-storage内部系统）
    if not hasattr(user, 'has_perms'):
        from libs.middleware import DeveloperUser
        user = DeveloperUser()

    codes = perm_code.split('|') if hasattr(perm_code, 'split') else [perm_code]
    return user.has_perms(codes)
