/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React, { useState } from 'react';
import { observer } from 'mobx-react';
import { Modal, Form, Input, Checkbox, Switch, Row, Col, message, Button, Tag } from 'antd';
import { FolderOpenOutlined } from '@ant-design/icons';
import http from 'libs/http';
import store from './store';
import envStore from '../environment/store';
import packageStore from '../package/store';
import FileSelector from './FileSelector';
import styles from './index.module.css';
import lds from 'lodash';

export default observer(function () {
  const isModify = store.record.id !== undefined;
  const isTask = store.type === 'task';
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [envs, setEnvs] = useState(isModify ? [store.env.id] : []);
  const [packages, setPackages] = useState(isModify ? [store.package.id] : []);
  const [fileVisible, setFileVisible] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState(
    isModify && store.record.value ? (
      Array.isArray(store.record.value) ? store.record.value : 
      store.record.value.split(',').filter(f => f.trim())
    ) : []
  );

  function handleSubmit() {
    setLoading(true);
    const formData = form.getFieldsValue();
    
    // 对于任务类型，将文件数组转换为逗号分隔的字符串
    if (isTask && selectedFiles.length > 0) {
      formData['value'] = selectedFiles.join(',');
    }
    
    formData['is_public'] = store.type === 'src' ? false : (formData['is_public'] || false);
    let request;
    if (isModify) {
      formData['id'] = store.record.id;
      request = http.patch('/api/config/', formData)
    } else {
      formData['type'] = store.type;
      formData['o_id'] = store.id;
      if (isTask) {
        formData['packages'] = packages;
      } else {
        formData['envs'] = envs;
      }
      request = http.post('/api/config/', formData)
    }
    request.then(res => {
      message.success('操作成功');
      store.formVisible = false;
      store.fetchRecords()
    }, () => setLoading(false))
  }

  function handleEnvCheck(id) {
    if (!isModify) {
      const tmp = lds.clone(envs);
      const index = tmp.indexOf(id);
      if (index !== -1) {
        tmp.splice(index, 1)
      } else {
        tmp.push(id)
      }
      setEnvs(tmp)
    }
  }

  function handlePackageCheck(id) {
    if (!isModify) {
      const tmp = lds.clone(packages);
      const index = tmp.indexOf(id);
      if (index !== -1) {
        tmp.splice(index, 1)
      } else {
        tmp.push(id)
      }
      setPackages(tmp)
    }
  }

  function handleFileSelect(filePaths) {
    setSelectedFiles(filePaths);
    form.setFieldsValue({ value: filePaths.join(',') });
    setFileVisible(false);
  }

  function removeFile(fileToRemove) {
    const newFiles = selectedFiles.filter(file => file !== fileToRemove);
    setSelectedFiles(newFiles);
    form.setFieldsValue({ value: newFiles.join(',') });
  }

  const getFieldLabel = () => {
    if (isTask) return '版本号';
    return 'Key';
  };

  const getValueLabel = () => {
    if (isTask) return '配套文件';
    return 'Value';
  };

  const getValueComponent = () => {
    if (isTask) {
      return (
        <div>
          <div style={{ marginBottom: 8 }}>
            {selectedFiles.map(file => (
              <Tag 
                key={file} 
                closable 
                onClose={() => removeFile(file)}
                style={{ marginBottom: 4 }}
              >
                {file}
              </Tag>
            ))}
          </div>
          <Button 
            type="dashed" 
            icon={<FolderOpenOutlined />} 
            onClick={() => setFileVisible(true)}
            style={{ width: '100%' }}
          >
            {selectedFiles.length > 0 ? '添加更多文件' : '选择配套文件'}
          </Button>
        </div>
      );
    }
    return <Input.TextArea placeholder="请输入"/>;
  };

  const getSelectionLabel = () => {
    if (isTask) return '选择配套';
    return '选择环境';
  };

  const getSelectionRecords = () => {
    if (isTask) return packageStore.records;
    return envStore.records;
  };

  const getSelectionValues = () => {
    if (isTask) return packages;
    return envs;
  };

  const handleSelectionCheck = (id) => {
    if (isTask) {
      handlePackageCheck(id);
    } else {
      handleEnvCheck(id);
    }
  };

  return (
    <Modal
      visible
      width={800}
      maskClosable={false}
      title={store.record.id ? (isTask ? '更新版本配套' : '更新配置') : (isTask ? '新增版本配套' : '新增配置')}
      onCancel={() => store.formVisible = false}
      confirmLoading={loading}
      onOk={handleSubmit}>
      <Form form={form} initialValues={{...store.record, value: isTask ? selectedFiles.join(',') : store.record.value}} labelCol={{span: 6}} wrapperCol={{span: 14}}>
        <Form.Item required name="key" label={getFieldLabel()}>
          <Input disabled={isModify} placeholder={isTask ? "请输入版本号，如：v1.2.3" : "请输入"}/>
        </Form.Item>
        <Form.Item name="value" label={getValueLabel()}>
          {getValueComponent()}
        </Form.Item>
        <Form.Item name="desc" label="备注">
          <Input.TextArea placeholder="请输入备注信息"/>
        </Form.Item>
        {store.type === 'app' && (
          <Form.Item
            label="类型"
            name="is_public"
            valuePropName="checked"
            initialValue={store.record.is_public === undefined || store.record.is_public}
            tooltip={<a target="_blank" rel="noopener noreferrer"
                        href="https://ops.spug.cc/docs/conf-app">什么是公共/私有配置？</a>}>
            <Switch checkedChildren="公共" unCheckedChildren="私有"/>
          </Form.Item>
        )}
        {isModify ? null : (
          <Form.Item label={getSelectionLabel()} style={{lineHeight: '40px'}}>
            {getSelectionRecords().map((item, index) => (
              <Row
                key={item.id}
                onClick={() => handleSelectionCheck(item.id)}
                style={{cursor: 'pointer', borderTop: index ? '1px solid #e8e8e8' : ''}}>
                <Col span={2}><Checkbox checked={getSelectionValues().includes(item.id)}/></Col>
                <Col span={10} className={styles.ellipsis}>{item.key}</Col>
                <Col span={10} className={styles.ellipsis}>{item.name}</Col>
              </Row>
            ))}
          </Form.Item>
        )}
      </Form>
      {fileVisible && (
        <FileSelector
          visible={fileVisible}
          onSelect={handleFileSelect}
          onCancel={() => setFileVisible(false)}
        />
      )}
    </Modal>
  )
})