/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Card, 
  Button, 
  Input, 
  Form, 
  Table, 
  Switch, 
  Space, 
  message, 
  Tabs, 
  Row, 
  Col,
  Typography,
  Popconfirm,
  Tag,
  Alert,
  Breadcrumb,
  Modal,
  Checkbox,
  Radio,
  Select,
  Spin,
  Badge,
} from 'antd';
import { 
  SaveOutlined, 
  ArrowLeftOutlined, 
  PlusOutlined, 
  DeleteOutlined,
  FileOutlined,
  CodeOutlined,
  InfoCircleOutlined,
  HolderOutlined,
  CopyOutlined,
  SettingOutlined,
  ImportOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import { http, history } from 'libs';
import FileSelector from 'pages/file/FileSelector';
import { ACEditor } from 'components';
import styles from './index.module.less';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// 拖拽手柄组件
const DragHandle = SortableHandle(() => (
  <HolderOutlined className={styles.dragHandle} />
));

// 可排序的表格行
const SortableItem = SortableElement(props => <tr {...props} />);

// 可排序的表格容器
const SortableBody = SortableContainer(props => <tbody {...props} />);

function TestPlanEditor({ match }) {
  const [testPlan, setTestPlan] = useState(null);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [steps, setSteps] = useState([]);
  const [activeTab, setActiveTab] = useState('steps');
  const [fileSelectorVisible, setFileSelectorVisible] = useState(false);
  const [assertionModalVisible, setAssertionModalVisible] = useState(false);
  const [currentAssertionStepId, setCurrentAssertionStepId] = useState(null);
  const [assertionForm] = Form.useForm();
  
  // 模板选择相关状态
  const [templateSelectorVisible, setTemplateSelectorVisible] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [templateLoading, setTemplateLoading] = useState(false);

  // 用例池选择相关状态
  const [caseTemplateSelectorVisible, setCaseTemplateSelectorVisible] = useState(false);
  const [caseTemplates, setCaseTemplates] = useState([]);
  const [caseTemplateLoading, setCaseTemplateLoading] = useState(false);
  
  // 组件挂载状态跟踪
  const isMountedRef = useRef(true);
  
  // 新增缺少的状态
  const [selectedStepForFiles, setSelectedStepForFiles] = useState(null);
  const [selectedStepForAssertion, setSelectedStepForAssertion] = useState(null);
  const [showAssertions, setShowAssertions] = useState(false);
  
  // 变量系统状态
  const [variables, setVariables] = useState([]);

  
  const testPlanId = match.params.id;
  const isEdit = testPlanId !== 'new';

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // 简单的更新步骤字段
  const updateStepField = useCallback((stepId, field, value) => {
    setSteps(prevSteps => {
      return prevSteps.map(step => {
        if (step.id === stepId) {
          const updatedStep = { ...step, [field]: value };
          
          // 如果workPath发生了变化，自动更新相关文件的targetPath
          if (field === 'workPath' && value !== step.workPath) {
            // 更新文件的targetPath
            if (updatedStep.files && updatedStep.files.length > 0) {
              updatedStep.files = updatedStep.files.map(file => {
                // 生成新的targetPath
                const newTargetPath = `${value}/${file.name}`;
                return {
                  ...file,
                  targetPath: newTargetPath
                };
              });
              
              message.info({
                content: `步骤"${updatedStep.label}"的执行路径已更改，已自动更新 ${updatedStep.files.length} 个文件的目标路径`,
                duration: 3
              });
            }
          }
          
          return updatedStep;
        }
        return step;
      });
    });
  }, []);
  
  // 拖拽排序处理函数
  const onSortEnd = ({ oldIndex, newIndex }) => {
    if (oldIndex !== newIndex) {
      const newSteps = [...steps];
      const [removed] = newSteps.splice(oldIndex, 1);
      newSteps.splice(newIndex, 0, removed);
      
      // 更新order字段
      const updatedSteps = newSteps.map((step, index) => ({
        ...step,
        order: index + 1
      }));
      
      setSteps(updatedSteps);
      message.success('步骤顺序已调整');
    }
  };

  // 自定义表格组件，支持拖拽
  const DraggableContainer = props => (
    <SortableBody
      useDragHandle
      disableAutoscroll
      helperClass={styles.rowDragging}
      onSortEnd={onSortEnd}
      {...props}
    />
  );

  const DraggableBodyRow = ({ className, style, ...restProps }) => {
    // 找到当前行的索引
    const index = steps.findIndex(x => x.id === restProps['data-row-key']);
    return <SortableItem index={index} {...restProps} />;
  };
  
  useEffect(() => {
    if (isEdit) {
      fetchTestPlan();
    } else {
      // 新建测试计划 - 提供默认的执行步骤
      const defaultSteps = [
        {
          id: 'step1',
          label: '驱动安装',
          description: '检查当前系统状态和环境',
          command: 'uname -a && lscpu | head -20',
          workPath: '/tmp',
          files: [],
          enabled: true,
          order: 1
        },
        {
          id: 'step2', 
          label: '固件安装',
          description: '驱动安装',
          command: 'ls -la ',
          workPath: '/tmp',
          files: [],
          enabled: true,
          order: 2
        },
        {
          id: 'step3',
          label: '固件安装', 
          description: '固件安装',
          command: 'nvidia-smi -q -d MEMORY,UTILIZATION,TEMPERATURE',
          workPath: '/tmp',
          files: [],
          enabled: true,
          order: 3
        },
        {
          id: 'step4',
          label: 'pcle带宽测试',
          description: '运行pcle带宽测试',
          command: 'python3 /tmp/gpu_benchmark.py',
          workPath: '/tmp',
          files: [],
          enabled: true,
          order: 4
        }
      ];
      
      const newPlan = {
        name: '',
        description: '',
        category: '',
        step_interval: 0,
        steps: defaultSteps,
        variables: []
      };
      setTestPlan(newPlan);
      setSteps(defaultSteps);
      setVariables([]);
      form.setFieldsValue({
        name: newPlan.name,
        description: newPlan.description,
        category: newPlan.category,
        step_interval: newPlan.step_interval
      });
    }
  }, [testPlanId]);

  const fetchTestPlan = async () => {
    if (!isMountedRef.current) return;
    setLoading(true);
    try {
      const response = await http.get(`/api/exec/test-plans/${testPlanId}/`);
      // 兼容不同的响应格式
      const plan = response.data || response;

      
      if (!isMountedRef.current) return;
      setTestPlan(plan);
      
      // 如果是旧格式（分离的files和commands），转换为新格式（steps）
      if (plan.steps) {

        // 确保步骤按order字段排序
        const sortedSteps = [...plan.steps].sort((a, b) => (a.order || 0) - (b.order || 0));
        setSteps(sortedSteps);
      } else {
        // 兼容旧格式，将commands和files合并为steps
        const convertedSteps = (plan.commands || []).map((cmd, index) => {
          const workPath = cmd.workPath || '/tmp';
          
          return {
            id: `step_${cmd.key || index}`,
            label: cmd.label || `步骤${index + 1}`,
            description: cmd.description || '',
            command: cmd.command || '',
            workPath: workPath, // 添加执行路径字段，默认为/tmp
            files: cmd.files ? cmd.files.map(file => {
              // 检查并更新旧的targetPath
              let targetPath = file.targetPath;
              // 如果targetPath使用的是/tmp而workPath不是/tmp，则更新targetPath
              if (targetPath && targetPath.startsWith('/tmp/') && workPath !== '/tmp') {
                const fileName = targetPath.replace('/tmp/', '');
                targetPath = `${workPath}/${fileName}`;
              }
              
              return {
                ...file,
                // 兼容旧的localPath字段
                path: file.path || file.localPath || file.name,
                targetPath: targetPath || `${workPath}/${file.name}`
              };
            }) : [],
            enabled: cmd.enabled !== false,
            order: index + 1
          };
        });
        
        // 如果命令中没有files信息，但有整体的files数据，将它们添加到第一个步骤中（旧旧格式兼容）
        if (plan.files && plan.files.length > 0 && convertedSteps.length > 0 && 
            convertedSteps.every(step => !step.files || step.files.length === 0)) {
          convertedSteps[0].files = (plan.files || []).map(file => ({
            ...file,
            // 兼容旧的localPath字段
            path: file.path || file.localPath || file.name
          }));
        }
        

        if (!isMountedRef.current) return;
        setSteps(convertedSteps);
      }
      
      // 加载变量
      if (!isMountedRef.current) return;
      setVariables(plan.variables || []);
      
      if (!isMountedRef.current) return;
      form.setFieldsValue({
        name: plan.name,
        description: plan.description,
        category: plan.category,
        step_interval: plan.step_interval || 0,
        file_path_strict: plan.file_path_strict || false
      });
    } catch (error) {
      if (isMountedRef.current) {
        message.error('加载测试计划失败');
      }

    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      
      const testPlanData = {
        ...values,
        steps,
        variables, // 添加变量系统
        // 为了向下兼容，同时提供旧格式，每个command包含自己的files
        commands: steps.map(step => ({
          key: step.id,
          label: step.label,
          command: step.command,
          description: step.description,
          enabled: step.enabled,
          files: step.files || [],
          workPath: step.workPath || '' // 添加执行路径
        })),
        // 保留整体的files字段用于兼容（已被弃用）
        files: steps.flatMap(step => step.files || [])
      };


      if (!isMountedRef.current) return;
      setLoading(true);
      
      if (isEdit) {
        await http.put(`/api/exec/test-plans/${testPlanId}/`, testPlanData);
        if (isMountedRef.current) {
          message.success('测试计划保存成功');
        }
      } else {
        const response = await http.post('/api/exec/test-plans/', testPlanData);
        if (isMountedRef.current) {
          message.success('测试计划创建成功');
          // 新建成功后，切换到编辑模式
          if (response.data && response.data.id) {
            history.replace(`/exec/command-cards/test-plans/edit/${response.data.id}`);
          }
        }
      }
      
      // 不再自动返回列表页，保留在当前页面
    } catch (error) {
      if (isMountedRef.current) {
        message.error(isEdit ? '保存失败' : '创建失败');
      }

    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  };

  const handleBack = () => {
    history.goBack();
  };

  // 步骤管理
  const handleAddStep = useCallback(() => {
    const newStep = {
      id: `step_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      label: '',
      description: '',
      command: '',
      workPath: '/tmp', // 默认执行路径
      files: [],
      enabled: true,
      order: steps.length + 1
    };
    setSteps(prevSteps => [...prevSteps, newStep]);
  }, [steps.length]);

  const handleDeleteStep = useCallback((stepId) => {
    setSteps(prevSteps => {
      const newSteps = prevSteps.filter(step => step.id !== stepId);
      // 重新排序
      return newSteps.map((step, idx) => ({
        ...step,
        order: idx + 1
      }));
    });
    message.success('步骤删除成功');
  }, []);

  // 文件管理（在步骤内）- 已移除，现在使用表格内的按钮直接设置selectedStepForFiles

  const handleSelectFiles = (selectedFiles) => {
    
    if (selectedStepForFiles) {
      const newSteps = [...steps];
      const stepIndex = newSteps.findIndex(step => step.id === selectedStepForFiles);
      
      if (stepIndex >= 0) {
        if (!newSteps[stepIndex].files) {
          newSteps[stepIndex].files = [];
        }
        
        // 添加选中的文件
        const stepWorkPath = newSteps[stepIndex].workPath || '/tmp';
        const newFiles = selectedFiles.map(file => ({
          id: `file_${Date.now()}_${Math.random()}`,
          name: file.name,
          path: file.path,
          targetPath: `${stepWorkPath}/${file.name}`,
          enabled: true
        }));
        
        newSteps[stepIndex].files.push(...newFiles);
        setSteps(newSteps);
        
        const stepName = newSteps[stepIndex].label || `步骤${stepIndex + 1}`;
        message.success(`已添加 ${selectedFiles.length} 个文件到"${stepName}"`);
        

      } else {
        message.error('添加文件失败：找不到对应的步骤');
      }
    } else {
      message.error('添加文件失败：未选择步骤');
    }
    
    setFileSelectorVisible(false);
    setSelectedStepForFiles(null);
  };

  // 断言配置
  const handleConfigAssertion = useCallback((stepId) => {
    const step = steps.find(s => s.id === stepId);
    if (step) {
      setCurrentAssertionStepId(stepId);
      
      // 设置断言表单的初始值
      const assertion = step.assertion || { enabled: false, type: 'command' };
      assertionForm.setFieldsValue({
        enabled: assertion.enabled,
        type: assertion.type,
        commandAssertion: assertion.commandAssertion || { command: '', description: '' },
        outputAssertion: assertion.outputAssertion || { pattern: '', type: 'contains', description: '' },
        runOnFailure: assertion.runOnFailure || false
      });
      
      setAssertionModalVisible(true);
    }
  }, [steps, assertionForm]);

  const handleSaveAssertion = async () => {
    try {
      const values = await assertionForm.validateFields();
      
      setSteps(prevSteps => {
        return prevSteps.map(step => {
          if (step.id === currentAssertionStepId) {
            return {
              ...step,
              assertion: values
            };
          }
          return step;
        });
      });
      
      setAssertionModalVisible(false);
      setCurrentAssertionStepId(null);
      message.success('断言配置保存成功');
    } catch (error) {
      // 断言表单验证失败
    }
  };

  // 检查命令是否包含可能需要用户输入的内容
  const checkInteractiveCommand = (command) => {
    const interactivePatterns = [
      /read\s+(-[a-z]+\s+)*[a-zA-Z_][a-zA-Z0-9_]*/g,  // read命令
      /read\s+-r\s+-p/g,  // read -r -p
      /input\s*\(/g,  // Python input()
      /scanf/g,  // C语言scanf
      /gets/g,   // C语言gets
      /getchar/g, // C语言getchar
      /choice\s+\/[CM]/g, // Windows choice命令
      /pause/g,  // Windows pause命令
      /select\s+.*in/g, // shell select语句
    ];
    
    const warnings = [];
    
    interactivePatterns.forEach((pattern, index) => {
      if (pattern.test(command)) {
        const warningMessages = [
          '检测到 read 命令，可能需要用户输入',
          '检测到交互式 read 命令',
          '检测到 Python input() 函数',
          '检测到 C语言 scanf 函数',
          '检测到 C语言 gets 函数',
          '检测到 C语言 getchar 函数',
          '检测到 Windows choice 命令',
          '检测到 Windows pause 命令',
          '检测到 shell select 语句',
        ];
        warnings.push(warningMessages[index]);
      }
    });
    
    return warnings;
  };

  // 最简单的表格列定义，使用非受控组件
  const getColumns = () => {
    const baseColumns = [
      {
        title: '排序',
        width: 30,
        fixed: 'left',
        render: () => <DragHandle />
      },
      {
        title: '序号',
        width: 30,
        fixed: 'left',
        render: (text, record, index) => (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            fontSize: '12px',
            fontWeight: 'bold',
            color: record.enabled ? '#1890ff' : '#999'
          }}>
            {index + 1}
          </div>
        )
      },
      {
        title: '启用',
        width: 60,
        dataIndex: 'enabled',
        render: (enabled, record) => (
          <Switch
            size="small"
            checked={enabled}
            onChange={(value) => {
              const newSteps = steps.map(step => 
                step.id === record.id ? { ...step, enabled: value } : step
              );
              setSteps(newSteps);
            }}
          />
        )
      },
      {
        title: '步骤名称',
        dataIndex: 'label',
        width: 100,
        render: (text, record) => (
          <Input
            size="small"
            defaultValue={text || ''}
            placeholder="步骤名称"
            onBlur={(e) => updateStepField(record.id, 'label', e.target.value)}
            style={{ fontSize: '16px' }}
          />
        )
      },
      {
        title: '关联文件',
        dataIndex: 'files',
        width: 170,
        render: (files, record) => (
          <div>
            {files && files.length > 0 ? (
              <div style={{ maxHeight: '120px', overflowY: 'auto' }}>
                {files.map((file, fileIndex) => (
                  <div key={file.id} style={{ marginBottom: '4px', padding: '2px', border: '1px solid #f0f0f0' }}>
                    <Switch
                      size="small"
                      checked={file.enabled}
                      onChange={(value) => {
                        const newSteps = steps.map(step => {
                          if (step.id === record.id) {
                            const newFiles = [...(step.files || [])];
                            newFiles[fileIndex] = { ...newFiles[fileIndex], enabled: value };
                            return { ...step, files: newFiles };
                          }
                          return step;
                        });
                        setSteps(newSteps);
                      }}
                    />
                    <div style={{ fontSize: '14px' }}>
                      📄 {file.name}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      📂 {file.path}
                    </div>
                    <Input
                      size="small"
                      value={file.targetPath || ''}
                      placeholder="目标路径"
                      onChange={(e) => {
                        const newSteps = steps.map(step => {
                          if (step.id === record.id) {
                            const newFiles = [...(step.files || [])];
                            newFiles[fileIndex] = { ...newFiles[fileIndex], targetPath: e.target.value };
                            return { ...step, files: newFiles };
                          }
                          return step;
                        });
                        setSteps(newSteps);
                      }}
                      style={{ fontSize: '12px', padding: '2px 4px', marginTop: '2px' }}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div style={{ color: '#999', fontSize: '12px' }}>无文件</div>
            )}
            <Button
              size="small"
              type="link"
              icon={<FileOutlined />}
              onClick={() => {
                setSelectedStepForFiles(record.id);
                setFileSelectorVisible(true);
              }}
              style={{ padding: '2px 4px', fontSize: '11px' }}
            >
              选择文件
            </Button>
          </div>
        )
      },
      {
        title: '执行路径',
        dataIndex: 'workPath',
        width: 130,
        render: (text, record) => (
          <Input
            size="small"
            defaultValue={text || ''}
            placeholder="/tmp"
            onBlur={(e) => updateStepField(record.id, 'workPath', e.target.value)}
            style={{ fontSize: '16px' }}
          />
        )
      },
      {
        title: '执行命令',
        dataIndex: 'command',
        width: 300,
        render: (text, record) => {
          // 前端预览变量替换效果
          const replaceVariables = (command) => {
            if (!command || !variables || variables.length === 0) return command;
            let result = command;
            variables.forEach(variable => {
              if (variable.name && variable.value) {
                const pattern = new RegExp(`\\$\\{${variable.name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\}`, 'g');
                result = result.replace(pattern, variable.value);
              }
            });
            return result;
          };

          const originalCommand = text || '';
          const processedCommand = replaceVariables(originalCommand);
          const hasVariables = originalCommand !== processedCommand;

          return (
            <div style={{ position: 'relative' }}>
              <ACEditor
                mode="sh"
                theme="tomorrow"
                width="100%"
                height="60px"
                defaultValue={originalCommand}
                onBlur={(event, editor) => {
                  const value = editor.getValue();
                  updateStepField(record.id, 'command', value);
                }}
                placeholder={variables.length > 0 ? `例如: nvidia-smi -i \$\{${variables[0]?.name || 'device_id'}\}` : "例如: nvidia-smi -q"}
                setOptions={{
                  showLineNumbers: false,
                  showGutter: false,
                  showPrintMargin: false,
                  highlightActiveLine: false,
                  fontSize: 16,
                  fontFamily: 'Victor Mono, Consolas, monospace',
                  wrap: true,
                  useWorker: false,
                  enableBasicAutocompletion: true,
                  enableLiveAutocompletion: true,
                  enableSnippets: true,
                  tabSize: 2
                }}
                style={{ 
                  border: '1px solid #d9d9d9', 
                  borderRadius: '4px'
                }}
              />
              
              {hasVariables && (
                <div style={{ 
                  marginTop: '4px', 
                  padding: '6px 8px', 
                  background: '#f6ffed', 
                  border: '1px solid #b7eb8f', 
                  borderRadius: '4px',
                  fontSize: '11px'
                }}>
                  <div style={{ 
                    color: '#389e0d', 
                    fontWeight: 'bold',
                    marginBottom: '4px',
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    🔄 执行时将替换为:
                  </div>
                  <div style={{ 
                    color: '#52c41a', 
                    fontFamily: 'Monaco, Courier New, monospace',
                    fontSize: '11px',
                    wordBreak: 'break-all',
                    background: '#fff',
                    padding: '4px 6px',
                    borderRadius: '3px',
                    border: '1px solid #d9f7be'
                  }}>
                    {processedCommand}
                  </div>
                </div>
              )}

              {/* {variables.length > 0 && !hasVariables && (
                <div style={{ 
                  marginTop: '4px', 
                  padding: '6px 8px', 
                  background: '#fff7e6', 
                  border: '1px solid #ffd666', 
                  borderRadius: '4px',
                  fontSize: '11px'
                }}>
                  <div style={{ 
                    color: '#d46b08', 
                    fontWeight: 'bold',
                    marginBottom: '4px'
                  }}>
                    💡 可用变量 (点击插入):
                  </div>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
                    {variables.map(variable => (
                      <span
                        key={variable.id}
                        style={{
                          display: 'inline-block',
                          padding: '2px 6px',
                          backgroundColor: '#fff',
                          border: '1px solid #ffd666',
                          borderRadius: '3px',
                          fontSize: '10px',
                          cursor: 'pointer',
                          fontFamily: 'Monaco, Courier New, monospace',
                          color: '#d46b08'
                        }}
                        onClick={() => {
                          // 这里可以添加插入变量到编辑器的逻辑
                          const variableText = `\${${variable.name}}`;
                          navigator.clipboard.writeText(variableText);
                          message.success(`已复制变量 ${variableText} 到剪贴板`);
                        }}
                        title={`${variable.description || '点击复制'} - 值: ${variable.value || '未设置'}`}
                      >
                        ${'${' + variable.name + '}'}
                      </span>
                    ))}
                  </div>
                </div>
              )} */}
            </div>
          );
        }
      },

      {
        title: '操作',
        width: 120,
        fixed: 'right',
        render: (text, record) => (
          <Space size="small">
            <Button
              size="small"
              type="link"
              icon={<CopyOutlined />}
              onClick={() => duplicateStep(record.id)}
              style={{ padding: '2px 4px' }}
            >
              复制
            </Button>
            <Popconfirm
              title="确定删除这个步骤吗？"
              onConfirm={() => removeStep(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                size="small"
                type="link"
                danger
                icon={<DeleteOutlined />}
                style={{ padding: '2px 4px' }}
              >
                删除
              </Button>
            </Popconfirm>
          </Space>
        )
      },
    ];

    // 如果有断言功能，添加断言列
    if (showAssertions) {
      baseColumns.splice(-1, 0, {
        title: '断言',
        dataIndex: 'assertions',
        width: 200,
        render: (assertions, record) => (
          <div>
            {assertions && assertions.length > 0 ? (
              <div>
                {assertions.map((assertion, index) => (
                  <Tag key={index} color={assertion.enabled ? 'green' : 'default'} style={{ fontSize: '11px', marginBottom: '2px' }}>
                    {assertion.type}: {assertion.description}
                  </Tag>
                ))}
              </div>
            ) : (
              <div style={{ color: '#999', fontSize: '12px' }}>无断言</div>
            )}
            <Button
              size="small"
              type="link"
              onClick={() => {
                setSelectedStepForAssertion(record.id);
                setAssertionModalVisible(true);
              }}
              style={{ padding: '2px 4px', fontSize: '11px' }}
            >
              设置断言
            </Button>
          </div>
        )
      });
    }

    return baseColumns;
  };

  // 复制步骤
  const duplicateStep = useCallback((stepId) => {
    const stepToDuplicate = steps.find(step => step.id === stepId);
    if (stepToDuplicate) {
      const newStep = {
        ...stepToDuplicate,
        id: `step_${Date.now()}`,
        label: `${stepToDuplicate.label} (副本)`,
        order: steps.length + 1
      };
      setSteps(prevSteps => [...prevSteps, newStep]);
      message.success('步骤已复制');
    }
  }, [steps]);

  // 删除步骤
  const removeStep = useCallback((stepId) => {
    setSteps(prevSteps => {
      const newSteps = prevSteps.filter(step => step.id !== stepId);
      // 重新排序
      return newSteps.map((step, index) => ({
        ...step,
        order: index + 1
      }));
    });
    message.success('步骤已删除');
  }, []);

  // 变量管理函数
  const handleAddVariable = useCallback(() => {
    const newVariable = {
      id: `var_${Date.now()}`,
      name: '',
      value: '',
      description: ''
    };
    setVariables(prev => [...prev, newVariable]);
  }, []);

  const handleDeleteVariable = useCallback((varId) => {
    setVariables(prev => prev.filter(v => v.id !== varId));
    message.success('变量已删除');
  }, []);

  const updateVariableField = useCallback((varId, field, value) => {
    setVariables(prev => prev.map(v => 
      v.id === varId ? { ...v, [field]: value } : v
    ));
  }, []);

  // 模板管理函数
  const loadTemplates = async () => {
    if (!isMountedRef.current) return;
    setTemplateLoading(true);
    try {
      const response = await http.get('/api/exec/template/');
      if (!isMountedRef.current) return;
      setTemplates(response.templates || []);
    } catch (error) {
      if (isMountedRef.current) {
        message.error('加载命令模板失败');
      }

    } finally {
      if (isMountedRef.current) {
        setTemplateLoading(false);
      }
    }
  };

  const handleImportTemplate = (template) => {
    // 将模板转换为测试计划步骤
    const newStep = {
      id: `step_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      label: template.name,
      description: template.desc || `从命令模板"${template.name}"导入`,
      command: convertTemplateToCommand(template),
      workPath: '/tmp',
      files: [],
      enabled: true,
      order: steps.length + 1,
      // 保存原始模板信息以便处理参数化
      templateInfo: {
        id: template.id,
        name: template.name,
        parameters: template.parameters || [],
        interpreter: template.interpreter
      }
    };

    // 如果模板有参数化配置，转换为测试计划变量
    if (template.parameters && template.parameters.length > 0) {
      const newVariables = template.parameters.map(param => ({
        id: `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: param.variable, // 使用模板的变量名
        value: param.default || '', // 使用默认值
        description: `${param.name} - ${param.desc || '从模板导入'}`,
        templateParam: true // 标记为模板参数
      }));

      // 添加新变量到变量列表
      setVariables(prev => [...prev, ...newVariables]);
      
      message.success(`已导入模板"${template.name}"并转换了 ${newVariables.length} 个参数为变量`);
    } else {
      message.success(`已导入模板"${template.name}"`);
    }

    setSteps(prevSteps => [...prevSteps, newStep]);
    setTemplateSelectorVisible(false);
  };

  // 转换模板命令格式
  const convertTemplateToCommand = (template) => {
    let command = template.body;

    // 将模板的环境变量格式 _SPUG_xxx 转换为测试计划的变量格式 ${xxx}
    if (template.parameters && template.parameters.length > 0) {
      template.parameters.forEach(param => {
        const envVarPattern = new RegExp(`_SPUG_${param.variable}`, 'g');
        command = command.replace(envVarPattern, `\${${param.variable}}`);
      });
    }

    return command;
  };

  // 用例池管理函数
  const loadCaseTemplates = async () => {
    if (!isMountedRef.current) return;
    setCaseTemplateLoading(true);
    try {
      const response = await http.get('/api/exec/test-case-templates/');
      if (!isMountedRef.current) return;
      setCaseTemplates(response || []);
    } catch (error) {
      if (isMountedRef.current) {
        message.error('加载用例模板失败');
      }

    } finally {
      if (isMountedRef.current) {
        setCaseTemplateLoading(false);
      }
    }
  };

  const handleImportCaseTemplate = (caseTemplate) => {
    // 将用例模板转换为测试计划步骤
    const newStep = {
      id: `step_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      label: caseTemplate.name,
      description: caseTemplate.description || `从用例池"${caseTemplate.name}"导入`,
      command: `# 测试用例: ${caseTemplate.name}
# 预置条件: ${caseTemplate.precondition || '无'}
# 测试步骤:
${caseTemplate.test_steps || ''}
# 预期结果: ${caseTemplate.expected_result || ''}`,
      workPath: '/tmp',
      files: [],
      enabled: caseTemplate.enabled !== false,
      order: steps.length + 1,
      // 保存原始用例模板信息
      caseTemplateInfo: {
        id: caseTemplate.id,
        name: caseTemplate.name,
        category: caseTemplate.category,
        precondition: caseTemplate.precondition,
        test_steps: caseTemplate.test_steps,
        expected_result: caseTemplate.expected_result
      }
    };

    message.success(`已导入用例模板"${caseTemplate.name}"`);
    setSteps(prevSteps => [...prevSteps, newStep]);
    setCaseTemplateSelectorVisible(false);
  };

  if (loading && isEdit && !testPlan) {
    return <div style={{ textAlign: 'center', padding: '50px' }}>加载中...</div>;
  }

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: 'calc(100vh - 64px)' }}>
      {/* 面包屑导航 */}
      <Breadcrumb style={{ marginBottom: '16px' }}>
        <Breadcrumb.Item>脚本配置中心</Breadcrumb.Item>
        <Breadcrumb.Item>
          <a onClick={handleBack} style={{ cursor: 'pointer' }}>测试计划</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{isEdit ? '编辑' : '新建'}</Breadcrumb.Item>
      </Breadcrumb>
      
      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Space style={{ marginBottom: '16px' }}>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              返回
            </Button>
            <Title level={4} style={{ margin: 0 }}>
              {isEdit ? '编辑测试计划' : '新建测试计划'}
            </Title>
          </Space>
          
          <div style={{ float: 'right' }}>
            <Space>
              <Button onClick={handleBack}>返回列表</Button>
              <Button 
                type="primary" 
                icon={<SaveOutlined />} 
                onClick={handleSave}
                loading={loading}
              >
                保存
              </Button>
            </Space>
          </div>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<span><InfoCircleOutlined />基本信息</span>} key="basic">
            <Row gutter={24}>
              <Col span={12}>
                <Form form={form} layout="vertical">
                  <Form.Item
                    name="name"
                    label="测试计划名称"
                    rules={[{ required: true, message: '请输入测试计划名称' }]}
                  >
                    <Input placeholder="例如: GPU驱动升级测试" />
                  </Form.Item>
                  
                  <Form.Item
                    name="category"
                    label="分类"
                  >
                    <Input placeholder="例如: 硬件测试" />
                  </Form.Item>
                  
                  <Form.Item
                    name="step_interval"
                    label="步骤间隔"
                    extra="每个步骤之间的等待时间（秒），0表示不等待"
                    rules={[
                      { pattern: /^[0-9]*$/, message: '请输入有效的数字' }
                    ]}
                  >
                    <Input 
                      placeholder="0" 
                      suffix="秒"
                      style={{ width: '150px' }}
                    />
                  </Form.Item>
                  
                  <Form.Item
                    name="file_path_strict"
                    label="目标路径策略"
                    extra="严格模式：目标目录不存在时跳过传输；兼容模式：目标目录不存在时自动创建"
                    valuePropName="checked"
                  >
                    <Switch 
                      checkedChildren="严格模式" 
                      unCheckedChildren="兼容模式"
                      style={{ width: '120px' }}
                    />
                  </Form.Item>
                  
                  <Form.Item
                    name="description"
                    label="描述"
                  >
                    <Input.TextArea 
                      rows={4} 
                      placeholder="描述这个测试计划的用途和流程..."
                    />
                  </Form.Item>
                </Form>
              </Col>
              <Col span={12}>
                <Alert
                  message="测试计划说明"
                  description={
                    <div>
                                             <p><strong>执行流程：</strong></p>
                       <ol>
                         <li>📁 文件分发：将关联文件从文件管理器传输到目标主机</li>
                         <li>🚀 命令执行：按顺序执行配置的命令</li>
                         <li>📊 结果收集：实时收集执行状态和输出</li>
                       </ol>
                       <p><strong>文件选择：</strong></p>
                       <ul>
                         <li>✨ 点击"📁 选择文件"从文件管理器中选择</li>
                         <li>🎯 支持多文件选择，自动设置默认目标路径</li>
                         <li>🔧 可自定义目标路径，如 /tmp/、/opt/ 等</li>
                       </ul>
                                             <p><strong>统计信息：</strong></p>
                       <ul>
                         <li>🔄 执行步骤: {steps.length} (启用: {steps.filter(s => s.enabled).length})</li>
                         <li>📄 关联文件: {steps.reduce((total, step) => total + (step.files?.length || 0), 0)} 个</li>
                         <li>⚙️ 配置变量: {variables.length} 个</li>
                       </ul>
                    </div>
                  }
                  type="info"
                  showIcon
                />
              </Col>
            </Row>
          </TabPane>

          <TabPane tab={<span><SettingOutlined />变量配置 ({variables.length})</span>} key="variables">
            <div style={{ marginBottom: '16px' }}>
              <Space>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />} 
                  onClick={handleAddVariable}
                >
                  ⚙️ 添加变量
                </Button>
                <Text type="secondary">
                  共 {variables.length} 个变量，支持 ${'${name}'} 格式引用
                </Text>
              </Space>
              
              {/* 变量使用统计 */}
              {variables.length > 0 && (
                <div style={{ 
                  marginTop: 16, 
                  padding: '12px 16px', 
                  background: '#f0f9ff', 
                  border: '1px solid #bae6fd', 
                  borderRadius: '6px' 
                }}>
                  <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#0369a1' }}>
                    🎯 变量使用预览
                  </div>
                  {variables.map((variable, index) => (
                    <div key={variable.id} style={{ 
                      marginBottom: index < variables.length - 1 ? '4px' : 0,
                      fontSize: '12px'
                    }}>
                      <span style={{ 
                        color: '#0369a1', 
                        fontFamily: 'Monaco, Courier New, monospace',
                        fontWeight: 'bold'
                      }}>
                        ${'${' + variable.name + '}'}
                      </span>
                      <span style={{ margin: '0 8px', color: '#6b7280' }}>→</span>
                      <span style={{ 
                        color: '#059669', 
                        fontFamily: 'Monaco, Courier New, monospace',
                        background: '#dcfce7',
                        padding: '2px 4px',
                        borderRadius: '3px'
                      }}>
                        {variable.value || '<未设置>'}
                      </span>
                      {variable.description && (
                        <span style={{ marginLeft: '8px', color: '#6b7280', fontSize: '11px' }}>
                          ({variable.description})
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              )}
              
              <Alert
                message="变量说明"
                description={
                  <div>
                    <p><strong>变量系统</strong>：沿用JMeter的变量设计，使用${'${variable_name}'}格式在命令中引用变量。</p>
                    <p><strong>使用示例</strong>：定义变量 device_id=0，在命令中使用 nvidia-smi -i ${'${device_id}'} 会自动替换为 nvidia-smi -i 0</p>
                    <p><strong>模板导入</strong>：从命令模板导入的参数会自动转换为测试计划变量，模板的 _SPUG_变量名 格式会转换为 ${'${变量名}'} 格式</p>
                    <p><strong>适用场景</strong>：设备ID、路径、版本号等重复配置，便于统一管理和批量修改。</p>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginTop: 16 }}
              />
            </div>
            
            <Table
              dataSource={variables}
              rowKey="id"
              pagination={false}
              locale={{ emptyText: '暂无变量，点击"添加变量"开始配置' }}
              size="small"
            >
              <Table.Column
                title="变量名"
                dataIndex="name"
                width={150}
                key="variableName"
                render={(text, record) => (
                  <Input
                    size="small"
                    value={text || ''}
                    placeholder="例如: device_id"
                    onChange={(e) => updateVariableField(record.id, 'name', e.target.value)}
                    style={{ fontSize: '12px' }}
                  />
                )}
              />
              <Table.Column
                title="变量值"
                dataIndex="value"
                width={150}
                key="variableValue"
                render={(text, record) => (
                  <Input
                    size="small"
                    value={text || ''}
                    placeholder="例如: 0"
                    onChange={(e) => updateVariableField(record.id, 'value', e.target.value)}
                    style={{ fontSize: '12px' }}
                  />
                )}
              />
              <Table.Column
                title="使用格式"
                width={120}
                key="variableFormat"
                render={(text, record) => (
                  <div style={{
                    padding: '4px 8px',
                    backgroundColor: '#f0f9ff',
                    border: '1px solid #bae6fd',
                    borderRadius: '4px',
                    fontSize: '11px',
                    fontFamily: 'Monaco, Courier New, monospace',
                    color: '#0369a1',
                    textAlign: 'center'
                  }}>
                    ${'${' + (record.name || 'name') + '}'}
                  </div>
                )}
              />
              <Table.Column
                title="引用次数"
                width={80}
                key="variableUsage"
                render={(text, record) => {
                  const usageCount = steps.reduce((count, step) => {
                    if (!step.command || !record.name) return count;
                    const pattern = new RegExp(`\\$\\{${record.name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\}`, 'g');
                    const matches = step.command.match(pattern);
                    return count + (matches ? matches.length : 0);
                  }, 0);
                  
                  return (
                    <div style={{ textAlign: 'center' }}>
                      {usageCount > 0 ? (
                        <Badge 
                          count={usageCount} 
                          style={{ backgroundColor: '#52c41a' }}
                          title={`在 ${usageCount} 个地方使用`}
                        />
                      ) : (
                        <span style={{ color: '#999', fontSize: '12px' }}>未使用</span>
                      )}
                    </div>
                  );
                }}
              />
              <Table.Column
                title="备注说明"
                dataIndex="description"
                key="variableDescription"
                render={(text, record) => (
                  <Input
                    size="small"
                    value={text || ''}
                    placeholder="例如: GPU设备ID，默认为0号设备"
                    onChange={(e) => updateVariableField(record.id, 'description', e.target.value)}
                    style={{ fontSize: '12px' }}
                  />
                )}
              />
              <Table.Column
                title="操作"
                width={100}
                key="variableActions"
                render={(text, record) => (
                  <Popconfirm
                    title="确定删除这个变量吗？"
                    onConfirm={() => handleDeleteVariable(record.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      size="small"
                      type="link"
                      danger
                      icon={<DeleteOutlined />}
                      style={{ padding: '2px 4px' }}
                    >
                      删除
                    </Button>
                  </Popconfirm>
                )}
              />
            </Table>
          </TabPane>

          <TabPane tab={<span><CodeOutlined />执行步骤 ({steps.length})</span>} key="steps">
            <div style={{ marginBottom: '16px' }}>
              <Space>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />} 
                  onClick={handleAddStep}
                >
                  🔄 添加步骤
                </Button>
                <Button
                  icon={<ImportOutlined />}
                  onClick={() => {
                    setTemplateSelectorVisible(true);
                    loadTemplates();
                  }}
                >
                  📋 引入命令模板
                </Button>
                <Button
                  icon={<ImportOutlined />}
                  onClick={() => {
                    setCaseTemplateSelectorVisible(true);
                    loadCaseTemplates();
                  }}
                >
                  📝 引入用例模板
                </Button>
                <Text type="secondary">
                  共 {steps.length} 个步骤，启用 {steps.filter(s => s.enabled).length} 个
                </Text>
              </Space>
              <Alert
                message="步骤说明"
                description={
                  <div>
                    <p>每个步骤可以包含多个文件和一个命令。执行时会先分发该步骤的文件，再执行命令。</p>
                    <p><strong>🔄 拖拽排序：</strong>点击并拖拽 <HolderOutlined style={{ color: '#1890ff' }} /> 图标可以调整步骤执行顺序</p>
                    <p><strong>📋 引入命令模板：</strong>可以从现有的命令模板库中选择并导入，自动转换参数化配置</p>
                    <p><strong>⏰ 超时配置：</strong>所有步骤使用系统统一的超时配置（60分钟），避免配置冲突</p>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginTop: 16 }}
              />
            </div>
            
            <Table
              className={styles.compactTable}
              columns={getColumns()}
              dataSource={steps}
              rowKey={record => record.id}
              pagination={false}
              scroll={{ x: 1600, y: 600 }}
              locale={{ emptyText: '暂无执行步骤，点击"添加步骤"开始配置' }}
              size="small"
              components={{
                body: {
                  wrapper: DraggableContainer,
                  row: DraggableBodyRow,
                },
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 文件选择器 */}
      <FileSelector
        visible={fileSelectorVisible}
        onCancel={() => {

          setFileSelectorVisible(false);
          setSelectedStepForFiles(null);
        }}
        onSelect={handleSelectFiles}
        multiple={true}
      />

      {/* 断言配置Modal */}
      <Modal
        title="配置断言检查"
        visible={assertionModalVisible}
        onOk={handleSaveAssertion}
        onCancel={() => {
          setAssertionModalVisible(false);
          setCurrentAssertionStepId(null);
        }}
        width={800}
        okText="保存"
        cancelText="取消"
      >
        <Form form={assertionForm} layout="vertical">
          <Form.Item
            name="enabled"
            valuePropName="checked"
          >
            <Checkbox>启用断言检查</Checkbox>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.enabled !== currentValues.enabled}
          >
            {({ getFieldValue }) => {
              const enabled = getFieldValue('enabled');
              if (!enabled) return null;

              return (
                <>
                  <Form.Item
                    name="type"
                    label="断言类型"
                    rules={[{ required: true, message: '请选择断言类型' }]}
                  >
                    <Radio.Group>
                      <Radio value="command">命令断言</Radio>
                      <Radio value="output">输出匹配断言</Radio>
                    </Radio.Group>
                  </Form.Item>

                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
                  >
                    {({ getFieldValue: getFieldValue2 }) => {
                      const assertionType = getFieldValue2('type');

                      if (assertionType === 'command') {
                        return (
                          <Card title="命令断言配置" size="small" style={{ marginBottom: 16 }}>
                            <Form.Item
                              name={['commandAssertion', 'command']}
                              label="断言命令"
                              rules={[{ required: true, message: '请输入断言命令' }]}
                              extra="执行该命令，根据退出码判断断言结果（退出码为0表示通过）"
                            >
                              <Input.TextArea
                                rows={3}
                                placeholder="例如: test -f /opt/nvidia-driver.ko && echo '驱动文件存在'"
                              />
                            </Form.Item>
                            <Form.Item
                              name={['commandAssertion', 'description']}
                              label="断言描述"
                              rules={[{ required: true, message: '请输入断言描述' }]}
                            >
                              <Input placeholder="例如: 检查GPU驱动文件是否存在" />
                            </Form.Item>
                          </Card>
                        );
                      }

                      if (assertionType === 'output') {
                        return (
                          <Card title="输出匹配断言配置" size="small" style={{ marginBottom: 16 }}>
                            <Form.Item
                              name={['outputAssertion', 'type']}
                              label="匹配类型"
                              rules={[{ required: true, message: '请选择匹配类型' }]}
                            >
                              <Select>
                                <Option value="contains">包含文本</Option>
                                <Option value="equals">完全匹配</Option>
                                <Option value="regex">正则表达式</Option>
                              </Select>
                            </Form.Item>
                            <Form.Item
                              name={['outputAssertion', 'pattern']}
                              label="匹配规则"
                              rules={[{ required: true, message: '请输入匹配规则' }]}
                              extra="检查命令输出是否符合指定的匹配规则"
                            >
                              <Input.TextArea
                                rows={2}
                                placeholder="例如: NVIDIA Driver Version 或 /NVIDIA.*Driver Version: \\d+/"
                              />
                            </Form.Item>
                            <Form.Item
                              name={['outputAssertion', 'description']}
                              label="断言描述"
                              rules={[{ required: true, message: '请输入断言描述' }]}
                            >
                              <Input placeholder="例如: 检查输出中包含驱动版本信息" />
                            </Form.Item>
                          </Card>
                        );
                      }

                      return null;
                    }}
                  </Form.Item>

                  <Form.Item
                    name="runOnFailure"
                    valuePropName="checked"
                  >
                    <Checkbox>即使命令执行失败也运行断言</Checkbox>
                  </Form.Item>
                </>
              );
            }}
          </Form.Item>
        </Form>
      </Modal>

      {/* 命令模板选择器 */}
      <Modal
        title="选择命令模板"
        visible={templateSelectorVisible}
        onCancel={() => setTemplateSelectorVisible(false)}
        footer={null}
        width={1000}
        style={{ top: 20 }}
        bodyStyle={{ maxHeight: '70vh', overflowY: 'auto' }}
      >
        <div style={{ marginBottom: 16 }}>
          <Alert
            message="模板导入说明"
            description={
              <div>
                <p><strong>🔄 格式转换：</strong>模板的环境变量格式 <code>_SPUG_变量名</code> 将自动转换为测试计划的变量格式 <code>${'${变量名}'}</code></p>
                <p><strong>📋 参数处理：</strong>模板的参数化配置会自动转换为测试计划变量，可在"变量配置"标签页中管理</p>
                <p><strong>💡 使用建议：</strong>导入后可以根据需要调整命令内容和变量配置</p>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        </div>
        
        <Spin spinning={templateLoading}>
          {templates.length === 0 && !templateLoading ? (
            <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
              <CodeOutlined style={{ fontSize: '48px', marginBottom: '16px', color: '#d9d9d9' }} />
              <div>暂无可用的命令模板</div>
              <div style={{ fontSize: '12px', marginTop: '8px' }}>
                请先在 <a href="/exec/template" target="_blank">模板管理</a> 中创建命令模板
              </div>
            </div>
          ) : (
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', gap: '16px' }}>
              {templates.map(template => (
                <Card
                  key={template.id}
                  size="small"
                  hoverable
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleImportTemplate(template)}
                  title={
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <CodeOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                        <span style={{ fontWeight: 'bold' }}>{template.name}</span>
                      </div>
                      <Tag color={template.interpreter === 'python' ? 'green' : 'blue'}>
                        {template.interpreter}
                      </Tag>
                    </div>
                  }
                  extra={
                    <Space size="small">
                      {template.parameters && template.parameters.length > 0 && (
                        <Tag color="orange" style={{ fontSize: '11px' }}>
                          {template.parameters.length} 个参数
                        </Tag>
                      )}
                      <Tag color="purple" style={{ fontSize: '11px' }}>
                        {template.type || '通用'}
                      </Tag>
                    </Space>
                  }
                >
                  <div style={{ marginBottom: '12px' }}>
                    {template.desc ? (
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {template.desc}
                      </Text>
                    ) : (
                      <Text type="secondary" style={{ fontSize: '12px', fontStyle: 'italic' }}>
                        无描述信息
                      </Text>
                    )}
                  </div>
                  
                  <div style={{ 
                    background: '#f5f5f5', 
                    padding: '8px', 
                    borderRadius: '4px',
                    fontFamily: 'Monaco, Courier New, monospace',
                    fontSize: '11px',
                    maxHeight: '100px',
                    overflowY: 'auto',
                    border: '1px solid #e8e8e8'
                  }}>
                    {template.body || '无命令内容'}
                  </div>
                  
                  {template.parameters && template.parameters.length > 0 && (
                    <div style={{ marginTop: '8px' }}>
                      <Text strong style={{ fontSize: '11px', color: '#666' }}>参数：</Text>
                      <div style={{ marginTop: '4px' }}>
                        {template.parameters.map((param, index) => (
                          <Tag 
                            key={index} 
                            size="small" 
                            color="geekblue"
                            style={{ fontSize: '10px', marginBottom: '2px' }}
                          >
                            {param.name} ({param.variable})
                          </Tag>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <div style={{ 
                    marginTop: '8px', 
                    textAlign: 'center',
                    borderTop: '1px solid #f0f0f0',
                    paddingTop: '8px'
                  }}>
                    <Button type="link" size="small" style={{ padding: 0 }}>
                      点击导入此模板
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </Spin>
      </Modal>

      {/* 用例模板选择器 */}
      <Modal
        title="选择用例模板"
        visible={caseTemplateSelectorVisible}
        onCancel={() => setCaseTemplateSelectorVisible(false)}
        footer={null}
        width={1000}
        style={{ top: 20 }}
        bodyStyle={{ maxHeight: '70vh', overflowY: 'auto' }}
      >
        <div style={{ marginBottom: 16 }}>
          <Alert
            message="用例模板导入说明"
            description={
              <div>
                <p><strong>📝 格式转换：</strong>用例模板将转换为包含预置条件、测试步骤和预期结果的命令注释</p>
                <p><strong>🔄 步骤处理：</strong>导入后可以根据实际需要修改命令内容和执行逻辑</p>
                <p><strong>💡 使用建议：</strong>用例模板主要用于标准化测试流程，导入后请根据具体环境调整</p>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        </div>

        <Spin spinning={caseTemplateLoading}>
          {caseTemplates.length === 0 && !caseTemplateLoading ? (
            <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📝</div>
              <div style={{ fontSize: '16px', marginBottom: '8px' }}>暂无用例模板</div>
              <div style={{ fontSize: '14px' }}>请先在用例池中创建用例模板</div>
            </div>
          ) : (
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))', gap: '16px' }}>
              {caseTemplates.map(caseTemplate => (
                <Card
                  key={caseTemplate.id}
                  size="small"
                  hoverable
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleImportCaseTemplate(caseTemplate)}
                  title={
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <FileTextOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
                        <span style={{ fontWeight: 'bold' }}>{caseTemplate.name}</span>
                      </div>
                      <div>
                        {caseTemplate.category && (
                          <Tag color="blue" style={{ fontSize: '11px' }}>
                            {caseTemplate.category}
                          </Tag>
                        )}
                        <Tag color={caseTemplate.enabled ? 'green' : 'red'} style={{ fontSize: '11px' }}>
                          {caseTemplate.enabled ? '启用' : '禁用'}
                        </Tag>
                      </div>
                    </div>
                  }
                >
                  <div style={{ marginBottom: '12px' }}>
                    {caseTemplate.description ? (
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {caseTemplate.description}
                      </Text>
                    ) : (
                      <Text type="secondary" style={{ fontSize: '12px', fontStyle: 'italic' }}>
                        无描述信息
                      </Text>
                    )}
                  </div>

                  <div style={{ marginBottom: '8px' }}>
                    <Text strong style={{ fontSize: '12px', color: '#666' }}>预置条件：</Text>
                    <div style={{
                      fontSize: '12px',
                      color: '#333',
                      background: '#f5f5f5',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      marginTop: '4px',
                      maxHeight: '40px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}>
                      {caseTemplate.precondition || '无'}
                    </div>
                  </div>

                  <div style={{ marginBottom: '8px' }}>
                    <Text strong style={{ fontSize: '12px', color: '#666' }}>测试步骤：</Text>
                    <div style={{
                      fontSize: '12px',
                      color: '#333',
                      background: '#f5f5f5',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      marginTop: '4px',
                      maxHeight: '60px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'pre-wrap'
                    }}>
                      {caseTemplate.test_steps || '无'}
                    </div>
                  </div>

                  <div style={{ marginBottom: '12px' }}>
                    <Text strong style={{ fontSize: '12px', color: '#666' }}>预期结果：</Text>
                    <div style={{
                      fontSize: '12px',
                      color: '#333',
                      background: '#f5f5f5',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      marginTop: '4px',
                      maxHeight: '40px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}>
                      {caseTemplate.expected_result || '无'}
                    </div>
                  </div>

                  <div style={{ textAlign: 'center', paddingTop: '8px', borderTop: '1px solid #f0f0f0' }}>
                    <Button type="primary" size="small" style={{ fontSize: '12px' }}>
                      点击导入此用例模板
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </Spin>
      </Modal>

    </div>
  );
}

export default TestPlanEditor;