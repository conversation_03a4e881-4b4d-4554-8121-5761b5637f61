<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPU设备管理 - 用户手册</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .version-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-top: 20px;
        }

        /* 导航栏 */
        .nav {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: #2c3e50;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #667eea;
        }

        /* 主要内容区域 */
        .main-content {
            padding: 60px 0;
        }

        .section {
            margin-bottom: 80px;
        }

        .section-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 50px;
        }

        /* 功能卡片 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            border: 1px solid #e9ecef;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #667eea;
        }

        .feature-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .feature-description {
            color: #7f8c8d;
            line-height: 1.6;
        }

        /* 步骤指南 */
        .steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .step {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            position: relative;
            border: 2px solid #e9ecef;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .step:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .step-number {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .step-title {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .step-description {
            color: #7f8c8d;
        }

        /* 截图样式 */
        .screenshot {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            text-align: center;
            border: 2px dashed #dee2e6;
        }

        .screenshot img {
            max-width: 100%;
            border-radius: 8px;
        }

        .screenshot-placeholder {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .screenshot-caption {
            color: #6c757d;
            font-style: italic;
        }

        /* 提示框 */
        .tip, .warning, .info {
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid;
        }

        .tip {
            background: #f0f9ff;
            border-color: #0ea5e9;
            color: #0c4a6e;
        }

        .warning {
            background: #fef3c7;
            border-color: #f59e0b;
            color: #92400e;
        }

        .info {
            background: #ecfdf5;
            border-color: #10b981;
            color: #065f46;
        }

        /* 底部 */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .steps {
                grid-template-columns: 1fr;
            }
        }
        
        /* FAQ样式 */
        .faq-item {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .faq-question {
            color: #667eea;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .faq-answer {
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1><i class="fas fa-microchip"></i> GPU设备管理</h1>
                <p>专业的GPU设备监控与测试任务管理平台</p>
                <div class="version-badge">v1.0 用户手册</div>
            </div>
        </div>
    </header>

    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container">
            <div class="nav-content">
                <div class="logo">
                    <strong>用户手册</strong>
                </div>
                <ul class="nav-links">
                    <li><a href="#overview">系统概述</a></li>
                    <li><a href="#features">功能介绍</a></li>
                    <li><a href="#getting-started">快速开始</a></li>
                    <li><a href="#tutorials">使用教程</a></li>
                    <li><a href="#faq">常见问题</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 系统概述 -->
            <section id="overview" class="section">
                <h2 class="section-title">系统概述</h2>
                <p class="section-subtitle">GPU设备管理系统是一个专业的GPU资源管理平台，提供设备注册、状态监控、测试任务分配和性能分析功能</p>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <h3 class="feature-title">设备管理</h3>
                        <p class="feature-description">支持多种GPU型号的注册和管理，包括NVIDIA、AMD、昆仑芯等主流GPU设备</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title">性能监控</h3>
                        <p class="feature-description">实时监控GPU使用率、温度、内存占用等关键指标，提供历史数据分析</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <h3 class="feature-title">任务分配</h3>
                        <p class="feature-description">智能分配测试任务到合适的GPU设备，支持负载均衡和优先级管理</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <h3 class="feature-title">分组管理</h3>
                        <p class="feature-description">按厂商、型号、用途等维度对GPU设备进行分组管理，提高管理效率</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="feature-title">统计报表</h3>
                        <p class="feature-description">提供详细的使用统计和性能报表，支持导出和定制化分析</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="feature-title">告警通知</h3>
                        <p class="feature-description">设备异常、性能下降等情况的实时告警，确保系统稳定运行</p>
                    </div>
                </div>
            </section>

            <!-- 功能介绍 -->
            <section id="features" class="section">
                <h2 class="section-title">功能介绍</h2>
                <p class="section-subtitle">详细了解GPU设备管理系统的各项功能特性</p>

                <!-- GPU设备注册 -->
                <div class="feature-detail">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-plus-circle"></i> GPU设备注册
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        支持手动添加和自动发现GPU设备，管理设备的基本信息和技术规格。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            GPU设备注册界面截图
                        </div>
                        <p class="screenshot-caption">设备注册表单，支持填写详细的GPU规格信息</p>
                    </div>

                    <div class="info">
                        <strong>支持的GPU类型：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li><strong>NVIDIA系列：</strong>RTX 4090, RTX 4080, RTX 3090, GTX 1080Ti等</li>
                            <li><strong>AMD系列：</strong>RX 7900 XTX, RX 6900 XT, RX 5700 XT等</li>
                            <li><strong>昆仑芯系列：</strong>R200, R300, K100, K200等</li>
                            <li><strong>其他厂商：</strong>支持自定义GPU型号和规格</li>
                        </ul>
                    </div>
                </div>

                <!-- 设备监控 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-monitor-heart-rate"></i> 实时监控
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        实时监控GPU设备的运行状态，包括使用率、温度、内存占用等关键指标。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            GPU监控仪表板界面截图
                        </div>
                        <p class="screenshot-caption">实时显示GPU使用率、温度、内存等关键指标</p>
                    </div>

                    <div class="info">
                        <strong>监控指标：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>GPU使用率和计算负载</li>
                            <li>显存使用情况和可用容量</li>
                            <li>核心温度和风扇转速</li>
                            <li>功耗和电压状态</li>
                            <li>驱动版本和固件信息</li>
                            <li>运行时间和错误统计</li>
                        </ul>
                    </div>
                </div>

                <!-- 任务管理 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-tasks"></i> 测试任务管理
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        为GPU设备分配测试任务，跟踪任务执行状态和性能表现。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            任务管理界面截图
                        </div>
                        <p class="screenshot-caption">显示分配给GPU的测试任务列表和执行状态</p>
                    </div>

                    <div class="info">
                        <strong>任务管理功能：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>查看分配给特定GPU的所有测试任务</li>
                            <li>任务状态跟踪（待开始、进行中、已完成等）</li>
                            <li>任务优先级管理和调度</li>
                            <li>性能基准测试和结果分析</li>
                            <li>任务执行历史和统计报告</li>
                        </ul>
                    </div>
                </div>

                <!-- 分组管理 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-layer-group"></i> 分组管理
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        按照不同维度对GPU设备进行分组，便于批量管理和资源调度。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            GPU分组管理界面截图
                        </div>
                        <p class="screenshot-caption">按厂商分组显示GPU设备，支持多级分组</p>
                    </div>

                    <div class="info">
                        <strong>分组维度：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li><strong>按厂商分组：</strong>NVIDIA、AMD、昆仑芯等</li>
                            <li><strong>按型号分组：</strong>相同型号的GPU设备</li>
                            <li><strong>按用途分组：</strong>训练、推理、测试等</li>
                            <li><strong>按位置分组：</strong>机房、机架、服务器等</li>
                            <li><strong>按状态分组：</strong>在线、离线、维护中等</li>
                            <li><strong>自定义分组：</strong>根据业务需求自定义分组规则</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 快速开始 -->
            <section id="getting-started" class="section">
                <h2 class="section-title">快速开始</h2>
                <p class="section-subtitle">按照以下步骤快速上手GPU设备管理</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">添加GPU设备</h3>
                        <p class="step-description">点击"新增GPU"按钮，填写设备基本信息和技术规格</p>
                    </div>

                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">配置监控</h3>
                        <p class="step-description">设置监控参数和告警阈值，启用实时监控功能</p>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">分配任务</h3>
                        <p class="step-description">为GPU设备分配测试任务，设置优先级和执行计划</p>
                    </div>

                    <div class="step">
                        <div class="step-number">4</div>
                        <h3 class="step-title">监控分析</h3>
                        <p class="step-description">查看设备运行状态和任务执行情况，分析性能数据</p>
                    </div>
                </div>
            </section>

            <!-- 使用教程 -->
            <section id="tutorials" class="section">
                <h2 class="section-title">使用教程</h2>
                <p class="section-subtitle">详细的操作指南和最佳实践</p>

                <!-- 添加GPU设备教程 -->
                <div class="tutorial">
                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #2c3e50;">
                        🖥️ 如何添加GPU设备
                    </h3>

                    <div class="steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <h4 class="step-title">进入设备管理</h4>
                            <p class="step-description">在主页点击"GPU设备管理"卡片或直接访问GPU管理页面</p>
                        </div>

                        <div class="step">
                            <div class="step-number">2</div>
                            <h4 class="step-title">点击新增按钮</h4>
                            <p class="step-description">点击页面右上角的"新增GPU"按钮</p>
                        </div>

                        <div class="step">
                            <div class="step-number">3</div>
                            <h4 class="step-title">填写设备信息</h4>
                            <p class="step-description">输入GPU名称、型号、厂商、显存大小等基本信息</p>
                        </div>

                        <div class="step">
                            <div class="step-number">4</div>
                            <h4 class="step-title">保存设备</h4>
                            <p class="step-description">点击确定按钮保存，系统会自动验证信息并添加设备</p>
                        </div>
                    </div>

                    <div class="tip">
                        <strong>💡 填写建议：</strong> 建议使用标准的GPU型号命名，如"RTX 4090"、"RX 7900 XTX"等，便于系统识别和分组管理。
                    </div>
                </div>

                <!-- 监控配置教程 -->
                <div class="tutorial" style="margin-top: 50px;">
                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #2c3e50;">
                        📊 监控配置与告警设置
                    </h3>

                    <h4 style="color: #667eea; margin-bottom: 15px;">监控参数配置</h4>
                    <ol style="padding-left: 20px; color: #7f8c8d;">
                        <li>在GPU详情页面点击"监控配置"</li>
                        <li>设置监控间隔（建议30秒-5分钟）</li>
                        <li>选择需要监控的指标（使用率、温度、内存等）</li>
                        <li>配置数据保留时间（默认30天）</li>
                    </ol>

                    <h4 style="color: #667eea; margin-bottom: 15px; margin-top: 30px;">告警阈值设置</h4>
                    <ul style="padding-left: 20px; color: #7f8c8d;">
                        <li><strong>温度告警：</strong>建议设置为80°C以上</li>
                        <li><strong>使用率告警：</strong>可设置为90%以上持续10分钟</li>
                        <li><strong>内存告警：</strong>建议设置为95%以上</li>
                        <li><strong>离线告警：</strong>设备超过5分钟无响应</li>
                    </ul>

                    <div class="warning">
                        <strong>⚠️ 注意：</strong> 告警阈值应根据具体的GPU型号和使用场景进行调整。过低的阈值可能导致频繁误报，过高的阈值可能错过重要问题。
                    </div>
                </div>

                <!-- 任务分配教程 -->
                <div class="tutorial" style="margin-top: 50px;">
                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #2c3e50;">
                        🎯 测试任务分配
                    </h3>

                    <p style="color: #7f8c8d; margin-bottom: 20px;">
                        系统支持为GPU设备分配测试任务，并跟踪任务执行状态。
                    </p>

                    <div class="steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <h4 class="step-title">选择GPU设备</h4>
                            <p class="step-description">在GPU列表中点击要分配任务的设备</p>
                        </div>

                        <div class="step">
                            <div class="step-number">2</div>
                            <h4 class="step-title">查看任务列表</h4>
                            <p class="step-description">在设备详情页面查看当前分配的任务</p>
                        </div>

                        <div class="step">
                            <div class="step-number">3</div>
                            <h4 class="step-title">添加新任务</h4>
                            <p class="step-description">点击"添加任务"按钮，填写任务信息</p>
                        </div>

                        <div class="step">
                            <div class="step-number">4</div>
                            <h4 class="step-title">跟踪执行</h4>
                            <p class="step-description">监控任务执行状态和性能表现</p>
                        </div>
                    </div>

                    <div class="info">
                        <strong>📈 任务类型：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>模型训练任务：深度学习模型训练</li>
                            <li>推理测试任务：模型推理性能测试</li>
                            <li>基准测试任务：GPU性能基准测试</li>
                            <li>稳定性测试：长时间运行稳定性测试</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 常见问题 -->
            <section id="faq" class="section">
                <h2 class="section-title">常见问题</h2>
                <p class="section-subtitle">解答使用过程中的常见问题</p>

                <div style="max-width: 800px; margin: 0 auto;">
                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何查看GPU的实时监控数据？</h4>
                        <p class="faq-answer">A: 在GPU列表中点击对应设备，进入详情页面即可查看实时监控数据。数据每30秒自动刷新一次。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 支持哪些GPU厂商的设备？</h4>
                        <p class="faq-answer">A: 系统支持NVIDIA、AMD、昆仑芯等主流GPU厂商的设备。对于其他厂商的设备，可以手动添加并配置相关参数。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何设置GPU设备的告警通知？</h4>
                        <p class="faq-answer">A: 在GPU详情页面的"监控配置"中设置告警阈值，系统会在设备状态异常时自动发送通知。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 可以批量管理多个GPU设备吗？</h4>
                        <p class="faq-answer">A: 可以。系统支持按厂商、型号等维度进行分组管理，可以对同一分组的设备进行批量操作。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何查看GPU的历史性能数据？</h4>
                        <p class="faq-answer">A: 在GPU详情页面点击"历史数据"标签，可以查看指定时间范围内的性能趋势图表。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: GPU设备离线后如何处理？</h4>
                        <p class="faq-answer">A: 系统会自动检测设备离线状态并发送告警。可以在设备列表中查看离线设备，并进行故障排查或维护操作。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何导出GPU使用统计报告？</h4>
                        <p class="faq-answer">A: 在统计页面选择时间范围和设备范围，点击"导出报告"按钮即可生成Excel格式的统计报告。</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 底部 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 GPU设备管理系统. 版权所有.</p>
            <p style="margin-top: 10px; opacity: 0.8;">
                如有问题请联系系统管理员 |
                <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a>
            </p>
        </div>
    </footer>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航栏高亮
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('.nav');
            if (window.scrollY > 100) {
                nav.style.background = 'rgba(255, 255, 255, 0.95)';
                nav.style.backdropFilter = 'blur(10px)';
            } else {
                nav.style.background = 'white';
                nav.style.backdropFilter = 'none';
            }
        });
    </script>
</body>
</html>
