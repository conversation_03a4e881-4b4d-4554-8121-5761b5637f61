/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React, { useState, useEffect } from 'react';
import { Card, Button, Row, Col, Tooltip, Divider, Modal, Form, Input, Select, Space, message, Spin, Upload, Drawer } from 'antd';
import { 
  ThunderboltOutlined, 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  MonitorOutlined,
  DatabaseOutlined,
  FolderOutlined,
  CloudServerOutlined,
  ReloadOutlined,
  FileTextOutlined,
  UploadOutlined,
  PlayCircleOutlined,
  SendOutlined,
  CodeOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { ACEditor } from 'components';
import { http } from 'libs';
import styles from './QuickCommandPanel.module.less';

// 默认的快捷命令
const DEFAULT_COMMANDS = [
  {
    id: 'system_info',
    name: 'System信息',
    command: 'uname -a && cat /etc/os-release',
    category: 'system',
    icon: <CloudServerOutlined />,
    color: '#1890ff'
  },
  {
    id: 'disk_usage',
    name: '磁盘使用',
    command: 'df -h',
    category: 'system',
    icon: <FolderOutlined />,
    color: '#722ed1'
  },
  {
    id: 'process_top',
    name: '进程监控',
    command: 'top -n 1',
    category: 'monitor',
    icon: <MonitorOutlined />,
    color: '#eb2f96'
  },
];

// 分类选项
const CATEGORY_OPTIONS = [
  { value: 'performance', label: '性能' },
  { value: 'function', label: '功能' },
  { value: 'pressure', label: '压力' },
  { value: 'scripts', label: '脚本' },
  { value: 'plans', label: '测试计划' },
  { value: 'other', label: '其他' }
];

function QuickCommandPanel({ hostId, onExecuteCommand, disabled }) {
  const [commands, setCommands] = useState(DEFAULT_COMMANDS);
  const [customCommands, setCustomCommands] = useState([]);
  const [templateCommands, setTemplateCommands] = useState([]);
  const [scriptFiles, setScriptFiles] = useState([]);
  const [commandCards, setCommandCards] = useState([]);
  const [allCardsList, setAllCardsList] = useState([]); // 存储完整的测试计划列表
  const [executing, setExecuting] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [scriptModalVisible, setScriptModalVisible] = useState(false);
  const [cardModalVisible, setCardModalVisible] = useState(false);
  const [cardExecutorVisible, setCardExecutorVisible] = useState(false);
  const [editingCommand, setEditingCommand] = useState(null);
  const [selectedCard, setSelectedCard] = useState(null);
  const [selectedCardForExecution, setSelectedCardForExecution] = useState(null);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [form] = Form.useForm();
  const [scriptForm] = Form.useForm();

  useEffect(() => {
    loadCustomCommands();
    loadTemplateCommands();
    loadScriptFiles();
    loadTestPlans();
  }, [hostId]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadCustomCommands = async () => {
    try {
      // 这里可以从后端加载用户自定义的快捷命令
      // const response = await http.get(`/api/ssh/quick-commands/${hostId}`);
      // setCustomCommands(response.custom_commands || []);
    } catch (error) {
      console.error('Failed to load custom commands:', error);
    }
  };

  const loadTemplateCommands = async () => {
    setLoading(true);
    try {
      const response = await http.get('/api/exec/template/');
      console.log('模版数据响应:', response);
      const templates = response.templates || [];
      
      // 转换模版为快捷命令格式
      const templateCmds = templates.map(template => ({
        id: `template_${template.id}`,
        name: template.name,
        command: template.body,
        category: mapTemplateTypeToCategory(template.type),
        description: template.desc,
        icon: getIconByCategory(mapTemplateTypeToCategory(template.type)),
        color: getColorByCategory(mapTemplateTypeToCategory(template.type)),
        isTemplate: true,
        templateId: template.id,
        interpreter: template.interpreter
      }));
      
      console.log('转换后的模版命令:', templateCmds);
      setTemplateCommands(templateCmds);
      
      if (templateCmds.length > 0) {
        message.success(`加载了 ${templateCmds.length} 个模版命令`);
      } else {
        message.info('暂无模版命令');
      }
    } catch (error) {
      console.error('Failed to load template commands:', error);
      message.error(`加载模版命令失败: ${error.message || error}`);
    } finally {
      setLoading(false);
    }
  };

    // 递归遍历文件树，筛选脚本文件
  const extractScriptFiles = (files, currentPath = '') => {
    let scriptFiles = [];
    
    if (!Array.isArray(files)) {
      console.warn('extractScriptFiles: files is not an array:', files);
      return scriptFiles;
    }
    
    files.forEach(file => {
      if (!file) {
        console.warn('extractScriptFiles: file is null/undefined');
        return;
      }
      
      // 使用文件树API返回的数据结构
      const fileName = file.title || file.name || '';
      const fileKey = file.key || fileName;
      const fullPath = currentPath ? `${currentPath}/${fileName}` : fileName;
      
      console.log('处理文件:', { fileName, fileKey, type: file.type, fullPath });
      
      if (file.type === 'folder' && file.children && Array.isArray(file.children)) {
        // 递归处理子文件夹
        const subScripts = extractScriptFiles(file.children, fullPath);
        scriptFiles = scriptFiles.concat(subScripts);
      } else if (file.type !== 'folder' && fileName) {
        // 检查文件扩展名是否为脚本文件
        const lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith('.sh') || lowerFileName.endsWith('.py') || lowerFileName.endsWith('.bash')) {
          console.log('找到脚本文件:', fileName, 'type:', file.type);
          
          const interpreter = getInterpreterByExtension(fileName);
          // 优化显示名称，使用文件的完整路径
          const displayName = fileKey || fullPath;
          const scriptCommand = {
            id: `script_${fileKey.replace(/[^a-zA-Z0-9]/g, '_')}`,
            name: displayName,
            command: `# 脚本文件: ${fileKey}`,
            category: 'scripts',
            description: `脚本文件: ${fileKey} (${file.size_human || 'N/A'})`,
            icon: <FileTextOutlined />,
            color: lowerFileName.endsWith('.py') ? '#3776ab' : '#4EAA25', // Python蓝色, Shell绿色
            isScript: true,
            scriptName: fileName,
            scriptPath: fileKey, // 使用key作为完整路径
            interpreter: interpreter,
            fileInfo: file
          };
          scriptFiles.push(scriptCommand);
        }
      }
    });
    
    console.log('extractScriptFiles 结果:', scriptFiles);
    return scriptFiles;
  };

  // 加载脚本文件 - 从文件管理模块获取文件树
  const loadScriptFiles = async () => {
    setLoading(true);
    try {
      // 获取文件树数据
      const response = await http.get('/api/file/tree/');
      console.log('文件树响应:', response);
      
      // 正确获取文件树数据
      const fileTree = response.tree || [];
      console.log('文件树数据:', fileTree);
      
      // 递归提取脚本文件
      const extractedScripts = extractScriptFiles(fileTree);
      
      console.log('提取的脚本文件:', extractedScripts);
      setScriptFiles(extractedScripts);
      
      if (extractedScripts.length > 0) {
        message.success(`从文件树中加载了 ${extractedScripts.length} 个脚本文件`);
      } else {
        message.info('未找到脚本文件 (.sh, .py, .bash)');
      }
    } catch (error) {
      console.error('Failed to load script files from tree:', error);
      
      // 如果文件树API失败，尝试使用普通文件列表API
      try {
        const fallbackResponse = await http.get('/api/file/manager/');
        const files = fallbackResponse.files || [];
        console.log('降级使用文件管理API:', fallbackResponse);
        
        // 筛选脚本类型文件
        const scriptFiles = files.filter(file => {
          const fileName = file.name.toLowerCase();
          return (file.type === 'script' || file.type === 'file') && (fileName.endsWith('.sh') || fileName.endsWith('.py') || fileName.endsWith('.bash'));
        }).map(script => {
          const interpreter = getInterpreterByExtension(script.name);
          const fileName = script.name.toLowerCase();
          return {
            id: `script_${script.name.replace(/[^a-zA-Z0-9]/g, '_')}`,
            name: script.name,
            command: `# 脚本文件: ${script.name}`,
            category: 'scripts',
            description: `脚本文件: ${script.name} (${script.size_human || 'N/A'})`,
            icon: <FileTextOutlined />,
            color: fileName.endsWith('.py') ? '#3776ab' : '#4EAA25',
            isScript: true,
            scriptName: script.name,
            scriptPath: script.name,
            interpreter: interpreter,
            fileInfo: script
          };
        });
        
        setScriptFiles(scriptFiles);
        
        if (scriptFiles.length > 0) {
          message.success(`从文件管理API加载了 ${scriptFiles.length} 个脚本文件`);
        } else {
          message.info('未找到脚本文件 (.sh, .py, .bash)');
        }
      } catch (fallbackError) {
        console.error('Failed to load script files from manager:', fallbackError);
        message.error(`加载脚本文件失败: ${fallbackError.message || fallbackError}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // 加载测试计划
  const loadTestPlans = async () => {
    try {
      const response = await http.get('/api/exec/test-plans/');
      console.log('Load test plans response:', response);
      
      // 从response.data获取测试计划数组（API返回格式：{data: [...]}）
      const plansData = response?.data || [];
      console.log('处理后的测试计划数据:', plansData);
      
      // 存储完整的测试计划列表供测试计划执行器使用
      setAllCardsList(plansData);
      console.log('设置allCardsList:', plansData);
      
      // 将测试计划转换为快捷命令格式（保持兼容性）
      const planCommands = plansData.flatMap(plan => {
        console.log(`处理测试计划: ${plan.name}, 命令数量: ${plan.commands?.length || 0}`);
        return plan.commands?.filter(cmd => cmd.enabled).map(cmd => {
          console.log(`  - 命令: ${cmd.label} (${cmd.key})`);
          return {
            id: `plan_${plan.id}_${cmd.key}`,
            name: `${plan.name} - ${cmd.label}`,
            command: cmd.command,
            category: 'plans',
            description: cmd.description || `来自测试计划: ${plan.name}`,
            icon: <ThunderboltOutlined />,
            color: '#722ed1',
            isPlanCommand: true,
            planId: plan.id,
            planName: plan.name,
            commandKey: cmd.key
          };
        }) || [];
      });
      
      setCommandCards(planCommands);
      
      console.log(`✅ 测试计划加载完成: ${plansData.length} 个计划, ${planCommands.length} 个命令`);
      if (plansData.length > 0) {
        message.success(`加载了 ${plansData.length} 个测试计划`);
      }
    } catch (error) {
      console.error('❌ Failed to load test plans:', error);
      console.log('🔄 使用本地测试数据');
      
      // Fallback: 使用本地测试数据
      const testPlan = {
        id: 1,
        name: 'GPU驱动升级测试计划',
        description: '包含驱动文件分发和升级命令的完整测试计划',
        category: 'upgrade',
        files: [
          {
            id: 'file1',
            name: 'nvidia-driver-460.run',
            localPath: '固件/nvidia-driver-460.run',
            targetPath: '/tmp/nvidia-driver-460.run',
            description: 'NVIDIA驱动安装文件',
            enabled: true
          }
        ],
        commands: [
          {
            key: 'prepare',
            label: '环境检查',
            command: 'lsmod | grep nvidia && df -h /tmp',
            description: '检查当前驱动状态和磁盘空间',
            enabled: true
          },
          {
            key: 'install_driver',
            label: '安装驱动',
            command: 'chmod +x /tmp/nvidia-driver-460.run && /tmp/nvidia-driver-460.run --silent',
            description: '执行驱动安装',
            enabled: true
          }
        ]
      };
      
      setAllCardsList([testPlan]);
      message.warning('API调用失败，使用测试数据');
    }
  };

  // 根据文件扩展名获取解释器
  const getInterpreterByExtension = (filename) => {
    const ext = filename.substring(filename.lastIndexOf('.')).toLowerCase();
    const interpreters = {
      '.sh': 'bash',
      '.bash': 'bash',
      '.py': 'python3',
      '.py3': 'python3',
      '.pl': 'perl',
      '.rb': 'ruby',
      '.js': 'node',
    };
    return interpreters[ext] || 'bash';
  };

  // 映射模版类型到分类
  const mapTemplateTypeToCategory = (type) => {
    const typeMapping = {
      '性能': 'performance',
      '功能': 'function', 
      '压力': 'pressure'
    };
    return typeMapping[type] || 'other';
  };

  // 根据分类获取图标
  const getIconByCategory = (category) => {
    const iconMap = {
      performance: <MonitorOutlined />,
      function: <ThunderboltOutlined />,
      pressure: <DatabaseOutlined />,
      scripts: <FileTextOutlined />,
      other: <CloudServerOutlined />
    };
    return iconMap[category] || <CloudServerOutlined />;
  };

  // 根据分类获取颜色
  const getColorByCategory = (category) => {
    const colorMap = {
      performance: '#52c41a',
      function: '#1890ff',
      pressure: '#fa541c', 
      scripts: '#52c41a',
      other: '#722ed1'
    };
    return colorMap[category] || '#1890ff';
  };

  const handleExecuteCommand = async (command) => {
    if (disabled || !onExecuteCommand) return;
    
    setExecuting(prev => ({ ...prev, [command.id]: true }));
    
    try {
      if (command.isScript) {
        // 脚本文件需要先分发再执行
        await handleExecuteScript(command);
      } else {
        await onExecuteCommand(command.command);
        message.success(`执行命令: ${command.name}`);
      }
    } catch (error) {
      message.error(`命令执行失败: ${error.message}`);
    } finally {
      setExecuting(prev => ({ ...prev, [command.id]: false }));
    }
  };

  // 执行脚本文件
  const handleExecuteScript = async (scriptCommand) => {
    if (!hostId) {
      throw new Error('请先选择目标主机');
    }

    try {
      // 1. 分发脚本到目标主机
      message.info(`正在分发脚本 ${scriptCommand.name} 到主机...`);
      
      const distributeResponse = await http.post('/api/file/scripts/distribute/', {
        script_path: scriptCommand.scriptPath, // 使用完整路径
        script_name: scriptCommand.scriptName,
        host_ids: [hostId],
        target_dir: '/tmp'
      });

      if (distributeResponse.success) {
        message.success('脚本分发启动成功');
        
        // 2. 等待分发完成
        message.info('等待脚本分发完成...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 3. 构造执行命令
        const scriptFileName = scriptCommand.scriptName;
        const targetScriptPath = `/tmp/${scriptFileName}`;
        
        // 根据脚本类型选择执行方式
        let executeCmd;
        if (scriptCommand.scriptName.toLowerCase().endsWith('.py')) {
          executeCmd = `ls -la ${targetScriptPath} && chmod +x ${targetScriptPath}`;
        } else {
          executeCmd = `ls -la ${targetScriptPath} && chmod +x ${targetScriptPath}`;
        }
        
        message.info('正在执行脚本...');
        
        // 4. 执行脚本
        await onExecuteCommand(executeCmd);
        
        message.success(`脚本执行完成: ${scriptCommand.name}`);
      } else {
        throw new Error(distributeResponse.message || '脚本分发失败');
      }
    } catch (error) {
      throw new Error(`脚本执行失败: ${error.message}`);
    }
  };

  const handleAddCommand = () => {
    setEditingCommand(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditCommand = (command) => {
    setEditingCommand(command);
    form.setFieldsValue({
      name: command.name,
      command: command.command,
      category: command.category,
      color: command.color
    });
    setModalVisible(true);
  };

  const handleDeleteCommand = (commandId) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个快捷命令吗？',
      onOk: () => {
        setCustomCommands(prev => prev.filter(cmd => cmd.id !== commandId));
        message.success('删除成功');
      }
    });
  };

  const handleSaveCommand = async () => {
    try {
      const values = await form.validateFields();
      const newCommand = {
        ...values,
        id: editingCommand?.id || `custom_${Date.now()}`,
        category: values.category || 'custom',
        icon: getIconByCategory(values.category || 'custom'),
        color: values.color || getColorByCategory(values.category || 'custom')
      };

      if (editingCommand) {
        setCustomCommands(prev => 
          prev.map(cmd => cmd.id === editingCommand.id ? newCommand : cmd)
        );
        message.success('修改成功');
      } else {
        setCustomCommands(prev => [...prev, newCommand]);
        message.success('添加成功');
      }

      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Save command failed:', error);
    }
  };

  // 处理脚本上传
  const handleScriptUpload = async (options) => {
    const { file, onSuccess, onError } = options;
    
    // 检查文件类型
    const allowedTypes = ['.sh', '.py', '.pl', '.rb', '.js', '.bash'];
    const fileExt = file.name.substring(file.name.lastIndexOf('.'));
    if (!allowedTypes.includes(fileExt)) {
      message.error('只支持脚本文件格式: .sh, .py, .pl, .rb, .js, .bash');
      onError('不支持的文件类型');
      return;
    }

    const formData = new FormData();
    formData.append('file', file);
    
    setUploading(true);
    try {
      const response = await http.post('/api/file/manager/', formData);
      message.success('脚本上传成功');
      onSuccess(response);
      loadScriptFiles(); // 重新加载脚本列表
    } catch (error) {
      message.error('脚本上传失败');
      onError(error);
    } finally {
      setUploading(false);
    }
  };

  // 打开脚本上传对话框
  const handleAddScript = () => {
    setScriptModalVisible(true);
    scriptForm.resetFields();
  };

  // 打开命令卡片执行器
  const handleOpenCardExecutor = (card) => {
    setSelectedCardForExecution(card);
    setCardExecutorVisible(true);
  };

  // 执行卡片中的单个命令
  const handleExecuteCardCommand = async (command) => {
    if (disabled || !onExecuteCommand) return;
    
    setExecuting(prev => ({ ...prev, [command.key]: true }));
    
    try {
      await onExecuteCommand(command.command);
      message.success(`执行命令: ${command.label}`);
    } catch (error) {
      message.error(`命令执行失败: ${error.message}`);
    } finally {
      setExecuting(prev => ({ ...prev, [command.key]: false }));
    }
  };

  // 执行完整测试计划（文件分发 + 命令执行）
  const handleExecuteTestPlan = async () => {
    if (!selectedCardForExecution || disabled || !hostId) {
      message.error('请先选择主机和测试计划');
      return;
    }
    
    setExecuting(prev => ({ ...prev, 'all_plan': true }));
    
    try {
      // 调用后端API执行测试计划
      const response = await http.post('/api/exec/test-plan-executions/', {
        plan_id: selectedCardForExecution.id,
        host_ids: [hostId]
      });
      
      if (response.execution_id) {
        message.success('测试计划执行已开始，请查看终端输出');
        
        // 可以在这里添加WebSocket监听执行进度
        // const token = response.token;
        // 监听执行结果...
        
      } else {
        throw new Error('启动测试计划失败');
      }
    } catch (error) {
      message.error(`测试计划执行失败: ${error.message}`);
    } finally {
      setExecuting(prev => ({ ...prev, 'all_plan': false }));
    }
  };

  // 按顺序执行卡片中的所有命令（保持兼容性）
  const handleExecuteAllCardCommands = async () => {
    if (!selectedCardForExecution || disabled || !onExecuteCommand) return;
    
    const enabledCommands = selectedCardForExecution.commands?.filter(cmd => cmd.enabled) || [];
    
    if (enabledCommands.length === 0) {
      message.warning('该测试计划没有启用的命令');
      return;
    }

    message.info(`开始按顺序执行 ${enabledCommands.length} 个命令`);
    
    for (let i = 0; i < enabledCommands.length; i++) {
      const command = enabledCommands[i];
      setExecuting(prev => ({ ...prev, [`all_${command.key}`]: true }));
      
      try {
        await onExecuteCommand(command.command);
        message.success(`执行第 ${i + 1} 个命令: ${command.label}`);
        
        // 命令间等待1秒
        if (i < enabledCommands.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        message.error(`第 ${i + 1} 个命令执行失败: ${error.message}`);
        break; // 如果一个命令失败，停止执行后续命令
      } finally {
        setExecuting(prev => ({ ...prev, [`all_${command.key}`]: false }));
      }
    }
    
    message.success('所有命令执行完成');
  };

  // 合并所有命令（不包含卡片命令，因为卡片现在有专门的区域）
  const allCommands = [...commands, ...customCommands, ...templateCommands, ...scriptFiles];
  console.log(`🔍 命令数量统计:`, {
    默认命令: commands.length,
    自定义命令: customCommands.length,
    模版命令: templateCommands.length,
    脚本文件: scriptFiles.length,
    总计: allCommands.length,
    命令卡片: allCardsList.length + ' 个卡片'
  });
  
  // 按分类过滤命令
  const filteredCommands = selectedCategory === 'all' 
    ? allCommands 
    : allCommands.filter(cmd => cmd.category === selectedCategory);

  // 按类别分组命令
  const groupedCommands = filteredCommands.reduce((groups, command) => {
    const category = command.category || 'other';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(command);
    return groups;
  }, {});

  const categoryNames = {
    system: '系统信息',
    monitor: '性能监控',
    network: '网络检查',
    custom: '自定义命令',
    performance: '性能',
    function: '功能',
    pressure: '压力',
    scripts: '脚本文件',
    plans: '测试计划',
    other: '其他'
  };

  return (
    <div className={styles.quickCommandPanel}>
      <Card 
        title="快捷命令" 
        size="small"
        extra={
          <Space size="small">
            <Button 
              type="link" 
              size="small" 
              icon={<ReloadOutlined />}
              onClick={() => {
                loadTemplateCommands();
                loadScriptFiles();
                loadTestPlans();
              }}
              loading={loading}
            >
              刷新
            </Button>
            <Button 
              type="link" 
              size="small" 
              icon={<UploadOutlined />}
              onClick={handleAddScript}
            >
              上传脚本
            </Button>
            <Button 
              type="link" 
              size="small" 
              icon={<PlusOutlined />}
              onClick={handleAddCommand}
            >
              添加
            </Button>
          </Space>
        }
      >
        <div className={styles.filterSection}>
          <Select
            size="small"
            value={selectedCategory}
            onChange={setSelectedCategory}
            style={{ width: '100%', marginBottom: 8 }}
            placeholder="选择分类"
          >
            <Select.Option value="all">全部</Select.Option>
            {CATEGORY_OPTIONS.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
            <Select.Option value="system">系统信息</Select.Option>
            <Select.Option value="monitor">性能监控</Select.Option>
            <Select.Option value="network">网络检查</Select.Option>
            <Select.Option value="custom">自定义</Select.Option>
          </Select>
        </div>

        <Spin spinning={loading}>
          <div className={styles.commandContainer}>
            {Object.entries(groupedCommands).map(([category, categoryCommands]) => (
              <div key={category} className={styles.commandGroup}>
                <div className={styles.categoryTitle}>
                  {categoryNames[category] || category}
                  {category === 'scripts' && (
                    <span className={styles.categoryCount}>({categoryCommands.length})</span>
                  )}
                </div>
                <Row gutter={[6, 6]}>
                  {categoryCommands.map(command => (
                    <Col span={24} key={command.id}>
                                              <Tooltip 
                        title={
                          <div>
                            <div><strong>名称:</strong> {command.name}</div>
                            {command.isScript ? (
                              <>
                                <div><strong>脚本路径:</strong> {command.scriptPath}</div>
                                <div><strong>解释器:</strong> {command.interpreter}</div>
                                <div><strong>分发目录:</strong> /tmp/</div>
                                <div><strong>执行方式:</strong> 自动赋权并执行</div>
                              </>
                            ) : (
                              <div><strong>命令:</strong> {command.command}</div>
                            )}
                            {command.description && <div><strong>描述:</strong> {command.description}</div>}
                            {command.isTemplate && <div><strong>类型:</strong> 模版命令</div>}
                            {command.isScript && <div><strong>类型:</strong> 脚本文件</div>}
                            {command.isPlanCommand && (
                              <>
                                <div><strong>类型:</strong> 测试计划</div>
                                <div><strong>计划:</strong> {command.planName}</div>
                              </>
                            )}
                          </div>
                        } 
                        placement="top"
                      >
                        <Button
                          type="primary"
                          size="small"
                          block
                          loading={executing[command.id]}
                          disabled={disabled}
                          onClick={() => handleExecuteCommand(command)}
                          className={styles.commandButton}
                          style={{ borderColor: command.color, backgroundColor: command.color }}
                        >
                          <Space size={4}>
                            {command.isScript ? (
                              command.scriptName.toLowerCase().endsWith('.py') ? 
                                <CodeOutlined style={{ color: '#FFD43B' }} /> : 
                                <PlayCircleOutlined style={{ color: '#4EAA25' }} />
                            ) : command.isPlanCommand ? (
                              <ThunderboltOutlined style={{ color: '#722ed1' }} />
                            ) : command.icon}
                            <span className={styles.commandText}>
                              {command.name}
                              {command.isScript && (
                                <span className={styles.scriptBadge}>
                                  {command.scriptName.toLowerCase().endsWith('.py') ? 'Python' : 'Shell'}
                                </span>
                              )}
                              {command.isPlanCommand && (
                                <span className={styles.scriptBadge}>
                                  测试计划
                                </span>
                              )}
                            </span>
                          </Space>
                        </Button>
                      </Tooltip>
                      
                      {!command.isTemplate && !command.isScript && !command.isCardCommand && (
                        <div className={styles.commandActions}>
                          <Button
                            type="link"
                            size="small"
                            icon={<EditOutlined />}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditCommand(command);
                            }}
                          />
                          <Button
                            type="link"
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteCommand(command.id);
                            }}
                          />
                        </div>
                      )}
                    </Col>
                  ))}
                </Row>
                {category !== 'custom' && <Divider style={{ margin: '12px 0' }} />}
              </div>
            ))}
          </div>
        </Spin>
      </Card>

      {/* 测试计划区域 - 改为测试计划按钮形式 */}
      {console.log('🔍 渲染检查 - allCardsList:', allCardsList, 'length:', allCardsList.length, 'type:', typeof allCardsList)}
      

      
      {allCardsList.length > 0 && (
        <Card 
          title="测试计划" 
          size="small"
          style={{ marginTop: 8 }}
          extra={
            <Button 
              type="link" 
              size="small" 
              icon={<ReloadOutlined />}
              onClick={loadTestPlans}
              loading={loading}
            >
              刷新计划
            </Button>
          }
        >
          <Row gutter={[8, 8]}>
            {allCardsList.map(card => (
              <Col span={24} key={card.id}>
                <Button
                  type="default"
                  size="small"
                  block
                  icon={<ThunderboltOutlined />}
                  onClick={() => handleOpenCardExecutor(card)}
                  className={styles.cardButton}
                  style={{ 
                    borderColor: '#722ed1', 
                    color: '#722ed1',
                    textAlign: 'left',
                    height: 'auto',
                    padding: '8px 12px'
                  }}
                >
                  <div>
                    <div style={{ fontWeight: 'bold' }}>{card.name}</div>
                    {card.description && (
                      <div style={{ fontSize: '12px', color: '#999', marginTop: '2px' }}>
                        {card.description}
                      </div>
                    )}
                    <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                      📁 {card.files?.filter(f => f.enabled).length || 0} 个文件 | 🚀 {card.commands?.filter(cmd => cmd.enabled).length || 0} 个命令
                    </div>
                  </div>
                </Button>
              </Col>
            ))}
          </Row>

        </Card>
      )}

      {/* 添加/编辑命令对话框 */}
      <Modal
        title={editingCommand ? '编辑快捷命令' : '添加快捷命令'}
        visible={modalVisible}
        onOk={handleSaveCommand}
        onCancel={() => setModalVisible(false)}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="命令名称"
            rules={[{ required: true, message: '请输入命令名称' }]}
          >
            <Input placeholder="例如: 检查磁盘使用率" />
          </Form.Item>
          
          <Form.Item
            name="command"
            label="命令内容"
            rules={[{ required: true, message: '请输入命令内容' }]}
          >
            <ACEditor
              mode="sh"
              theme="tomorrow"
              width="100%"
              height="80px"
              placeholder="例如: df -h"
              setOptions={{
                showLineNumbers: true,
                showGutter: true,
                showPrintMargin: false,
                highlightActiveLine: true,
                fontSize: 13,
                fontFamily: 'Monaco, Courier New, monospace',
                wrap: true,
                useWorker: false, // 避免语法错误检查干扰
                enableBasicAutocompletion: true,
                enableLiveAutocompletion: true,
                enableSnippets: true,
                tabSize: 2,
                scrollPastEnd: 0.2
              }}
              style={{ 
                border: '1px solid #d9d9d9', 
                borderRadius: '4px'
              }}
            />
          </Form.Item>

          <Form.Item
            name="category"
            label="分类"
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="选择命令分类">
              {CATEGORY_OPTIONS.map(option => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="color"
            label="按钮颜色"
          >
            <Select placeholder="选择按钮颜色">
              <Select.Option value="#1890ff">蓝色</Select.Option>
              <Select.Option value="#52c41a">绿色</Select.Option>
              <Select.Option value="#fa541c">橙色</Select.Option>
              <Select.Option value="#722ed1">紫色</Select.Option>
              <Select.Option value="#eb2f96">粉色</Select.Option>
              <Select.Option value="#13c2c2">青色</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 脚本上传对话框 */}
      <Modal
        title="上传脚本文件"
        visible={scriptModalVisible}
        footer={null}
        onCancel={() => setScriptModalVisible(false)}
        width={600}
      >
        <div style={{ padding: '20px 0' }}>
          <Upload.Dragger
            name="file"
            customRequest={handleScriptUpload}
            showUploadList={false}
            loading={uploading}
            style={{ marginBottom: 16 }}
          >
            <p className="ant-upload-drag-icon">
              <CodeOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
            </p>
            <p className="ant-upload-text">点击或拖拽脚本文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持的脚本格式: .sh, .py, .pl, .rb, .js
            </p>
          </Upload.Dragger>
          
          <div style={{ background: '#f6f8fa', padding: '12px', borderRadius: '6px' }}>
            <div style={{ marginBottom: '8px', fontWeight: 'bold', color: '#666' }}>
              📋 使用说明:
            </div>
            <div style={{ fontSize: '13px', color: '#666', lineHeight: '1.6' }}>
              • 支持上传 <code>.sh</code>、<code>.py</code>、<code>.bash</code> 格式的脚本文件<br/>
              • 脚本上传后会自动添加到快捷命令面板的"脚本"分类<br/>
              • 脚本可以存储在任意目录层级，支持文件树结构<br/>
              • 点击脚本按钮会自动分发到目标主机的 <code>/tmp/</code> 目录<br/>
              • Python脚本使用 <code>python3</code> 执行，Shell脚本使用 <code>bash</code> 执行<br/>
              • 系统会自动赋予执行权限并显示执行结果<br/>
              • 建议脚本文件包含适当的错误处理和输出信息
            </div>
          </div>
        </div>
      </Modal>

      {/* 测试计划执行器 */}
      <Drawer
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span>执行测试计划: {selectedCardForExecution?.name}</span>
            <Button
              icon={<CloseOutlined />}
              onClick={() => setCardExecutorVisible(false)}
            >
              关闭
            </Button>
          </div>
        }
        placement="right"
        size="large"
        visible={cardExecutorVisible}
        onClose={() => setCardExecutorVisible(false)}
        maskClosable={false}
        destroyOnClose={true}
        bodyStyle={{ padding: '16px' }}
      >
        {selectedCardForExecution && (
          <div>
            <div style={{ marginBottom: 16, color: '#666' }}>
              📁 {selectedCardForExecution.files?.filter(f => f.enabled).length || 0} 个文件 | 
              🚀 {selectedCardForExecution.commands?.filter(cmd => cmd.enabled).length || 0} 个命令
            </div>
            
            {/* 文件列表 */}
            {selectedCardForExecution.files?.filter(f => f.enabled).length > 0 && (
              <div style={{ marginBottom: 24 }}>
                <h4 style={{ color: '#1890ff', marginBottom: 12 }}>📁 关联文件</h4>
                <Row gutter={[8, 8]}>
                  {selectedCardForExecution.files?.filter(f => f.enabled).map((file, index) => (
                    <Col span={24} key={file.id}>
                      <Card 
                        size="small" 
                        style={{ marginBottom: 8, borderColor: '#52c41a' }}
                        title={
                          <Space>
                            <span style={{ color: '#52c41a' }}>📄 {file.name}</span>
                          </Space>
                        }
                      >
                        <div style={{ fontSize: '12px' }}>
                          <div><strong>本地路径:</strong> {file.localPath}</div>
                          <div><strong>目标路径:</strong> {file.targetPath}</div>
                          {file.description && (
                            <div style={{ color: '#666', marginTop: 4 }}>
                              <strong>描述:</strong> {file.description}
                            </div>
                          )}
                        </div>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </div>
            )}
            
            {/* 命令列表 */}
            {selectedCardForExecution.commands?.filter(cmd => cmd.enabled).length > 0 && (
              <div>
                <h4 style={{ color: '#1890ff', marginBottom: 12 }}>🚀 执行步骤</h4>
                <Row gutter={[8, 8]}>
                  {selectedCardForExecution.commands?.filter(cmd => cmd.enabled).map((cmd, index) => (
                    <Col span={24} key={cmd.key}>
                      <Card 
                        size="small" 
                        style={{ marginBottom: 8 }}
                        title={
                          <Space>
                            <span style={{ color: '#1890ff' }}>{index + 1}.</span>
                            <span>{cmd.label}</span>
                          </Space>
                        }
                        extra={
                          <Button 
                            type="primary" 
                            size="small"
                            icon={<PlayCircleOutlined />}
                            loading={executing[cmd.key]}
                            disabled={disabled}
                            onClick={() => handleExecuteCardCommand(cmd)}
                          >
                            执行
                          </Button>
                        }
                      >
                        <div style={{ marginBottom: 8 }}>
                          <strong>命令:</strong>
                          <pre style={{ 
                            background: '#f5f5f5', 
                            padding: '8px', 
                            borderRadius: '4px',
                            fontSize: '12px',
                            margin: '4px 0',
                            wordWrap: 'break-word',
                            whiteSpace: 'pre-wrap'
                          }}>
                            {cmd.command || '(命令内容为空)'}
                          </pre>
                        </div>
                        {cmd.description && (
                          <div style={{ color: '#666', fontSize: '12px' }}>
                            <strong>描述:</strong> {cmd.description}
                          </div>
                        )}
                      </Card>
                    </Col>
                  ))}
                </Row>
              </div>
            )}
            
            {/* 执行整个测试计划的按钮 */}
            <div style={{ marginTop: 24, textAlign: 'center', borderTop: '1px solid #f0f0f0', paddingTop: 16 }}>
              <Button 
                type="primary" 
                size="large"
                icon={<ThunderboltOutlined />}
                loading={executing['all_plan']}
                disabled={disabled || !hostId}
                onClick={handleExecuteTestPlan}
                style={{ 
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none',
                  borderRadius: '8px',
                  height: '48px',
                  fontSize: '16px',
                  fontWeight: 'bold'
                }}
              >
                🚀 执行完整测试计划
              </Button>
              <div style={{ fontSize: '12px', color: '#999', marginTop: 8 }}>
                将按顺序分发文件并执行所有命令步骤
              </div>
            </div>
          </div>
        )}
      </Drawer>
    </div>
  );
}

export default QuickCommandPanel; 