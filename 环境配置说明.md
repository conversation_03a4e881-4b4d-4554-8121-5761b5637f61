# Spug 环境配置管理方案

## 🎯 解决方案概述

为了解决开发环境和生产环境配置不同的问题，我们创建了环境配置分离方案：

- **开发环境**：DEBUG=True，开发者后门启用，API端口9999
- **生产环境**：DEBUG=False，开发者后门禁用，API端口8000

## 📁 文件结构

```
spug/
├── spug_api/spug/
│   ├── settings.py          # 基础配置（共同部分）
│   ├── settings_dev.py      # 开发环境特定配置
│   ├── settings_prod.py     # 生产环境特定配置
│   └── settings_current.py  # 当前激活的配置（被.gitignore忽略）
├── spug_web/src/
│   ├── setupProxy.dev.js    # 开发环境代理配置
│   ├── setupProxy.prod.js   # 生产环境代理配置
│   └── setupProxy.js        # 当前激活的配置（被.gitignore忽略）
├── switch_env.py            # 环境切换脚本
└── start_dev.py             # 开发环境启动脚本
```

## 🔧 本地开发使用方法

### 1. 切换到开发环境
```bash
python switch_env.py dev
```

### 2. 启动开发环境
```bash
# 方法1：使用启动脚本（推荐）
python start_dev.py

# 方法2：手动启动
# 终端1 - 启动后端
cd spug_api
python manage.py runserver 0.0.0.0:9999 --settings=spug.settings_dev

# 终端2 - 启动前端
cd spug_web
npm start
```

### 3. 访问地址
- 前端：http://localhost:3000
- 后端API：http://localhost:9999

## 🚀 生产环境部署

### 1. 服务器上切换到生产环境
```bash
cd /opt/spug
python3 switch_env.py prod  # 如果有switch_env.py脚本
# 或者手动复制配置文件
```

### 2. 重启服务
```bash
cd /opt/spug
./simple_restart.sh
```

## 📋 配置差异对比

| 配置项 | 开发环境 | 生产环境 |
|--------|----------|----------|
| DEBUG | True | False |
| ALLOWED_HOSTS | ['*'] | ['************', '127.0.0.1', 'localhost'] |
| 开发者后门 | 启用 | 禁用 |
| API端口 | 9999 | 8000 |
| 前端代理目标 | http://127.0.0.1:9999 | http://127.0.0.1:8000 |

## 🔄 环境切换命令

```bash
# 切换到开发环境
python switch_env.py dev

# 切换到生产环境  
python switch_env.py prod
```

## 📝 注意事项

1. **配置文件管理**：
   - `settings_current.py` 和 `setupProxy.js` 被.gitignore忽略
   - 每次切换环境后需要重启服务

2. **代码同步**：
   - 现在可以安全地进行git pull，不会有配置冲突
   - 本地开发配置不会影响生产环境

3. **服务器部署**：
   - 生产服务器默认使用生产环境配置
   - 重启脚本会自动使用正确的配置

## 🛠️ 故障排查

如果遇到配置问题：

1. **检查当前环境**：
   ```bash
   cat spug_api/spug/settings_current.py | grep DEBUG
   cat spug_web/src/setupProxy.js | grep target
   ```

2. **重新切换环境**：
   ```bash
   python switch_env.py dev  # 或 prod
   ```

3. **重启服务**：
   - 开发环境：重启Django和React服务
   - 生产环境：运行 `./simple_restart.sh`

## ✅ 优势

1. **环境隔离**：开发和生产配置完全分离
2. **版本控制友好**：避免配置文件冲突
3. **一键切换**：简单的脚本命令即可切换环境
4. **向后兼容**：不影响现有的部署流程
