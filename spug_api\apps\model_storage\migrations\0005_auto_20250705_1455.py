# Generated by Django 2.2.28 on 2025-07-05 14:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0004_auto_20250705_1339'),
    ]

    operations = [
        migrations.AlterField(
            model_name='testtask',
            name='framework',
            field=models.CharField(blank=True, choices=[('pytorch', 'PyTorch'), ('tensorflow', 'TensorFlow'), ('onnx', 'ONNX'), ('keras', 'Keras'), ('paddle', 'PaddlePaddle'), ('mindspore', 'MindSpore'), ('other', '其他')], default='pytorch', max_length=32, null=True, verbose_name='框架'),
        ),
        migrations.AlterField(
            model_name='testtask',
            name='model_type',
            field=models.CharField(blank=True, choices=[('inference', '推理'), ('training', '训练'), ('fine_tuning', '微调'), ('evaluation', '评估'), ('optimization', '优化')], default='inference', max_length=32, null=True, verbose_name='模型类型'),
        ),
        migrations.AlterField(
            model_name='testtask',
            name='test_status',
            field=models.CharField(blank=True, choices=[('pending', '待开始'), ('in_progress', '进行中'), ('completed', '已完成'), ('cancelled', '已取消'), ('blocked', '阻塞中'), ('delayed', '已延期')], default='pending', max_length=32, null=True, verbose_name='测试状态'),
        ),
        migrations.AlterModelTable(
            name='riskalert',
            table='model_storage_risk_alerts_new',
        ),
        migrations.AlterModelTable(
            name='testtask',
            table='model_storage_test_tasks_new',
        ),
    ]
