// Modern Container Styles
.modernContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

.headerSection {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32px 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.headerContent {
  width: 100%;
  margin: 0;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.headerIcon {
  width: 64px;
  height: 64px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  
  :global(.anticon) {
    font-size: 32px;
    color: white;
  }
}

.headerTitle {
  color: white !important;
  margin: 0 !important;
  font-weight: 600;
  font-size: 28px;
}

.headerSubtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin-top: 4px;
}

.terminalButton {
  height: 48px;
  padding: 0 24px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }
}

.mainContent {
  width: 100%;
  margin: 0;
  padding: 0 16px 24px;
}

.sidebarCard {
  :global(.ant-card) {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    border: none;
    overflow: hidden;
    height: calc(100vh - 180px);
    
    .ant-card-head {
      background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
      border-bottom: 1px solid #e8f0ff;
      padding: 16px 20px;
      
      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
      }
    }
    
    .ant-card-body {
      padding: 16px;
      height: calc(100% - 60px);
      overflow: auto;
    }
  }
}

.tableCard {
  :global(.ant-card) {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    border: none;
    overflow: hidden;
    height: calc(100vh - 180px);
    
    .ant-card-body {
      padding: 0;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}

// Legacy styles (keeping for compatibility)
.steps {
  width: 350px;
  margin: 0 auto 30px;
}

.tagAdd {
  background: #fff;
  border-style: dashed;
}

.tagNumberInput {
  width: 78px;
  margin-right: 8px;
  vertical-align: top;
}

.tagInput {
  width: 140px;
  margin-right: 8px;
  vertical-align: top;
}

.hostExtendEdit {
  :global(.ant-descriptions-item-content) {
    padding: 4px 16px !important;
  }
}

.formAddress1 {
  display: inline-block;

  :global(.ant-input) {
    border-radius: 8px;
  }
}

.formAddress2 {
  display: inline-block;

  :global(.ant-input-group-addon) {
    border-left: none;
    border-radius: 0 8px 8px 0;
  }

  :global(.ant-input) {
    border-radius: 0;
  }
}

.formAddress3 {
  display: inline-block;

  :global(.ant-input-group-addon) {
    border-left: none;
    border-radius: 0 8px 8px 0;
  }
}

.group {
  height: 100%;
}

.treeNode {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 0;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(24, 144, 255, 0.06);
  }

  .title {
    margin-left: 12px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    font-weight: 500;
    color: #2c3e50;
  }

  .number {
    width: 32px;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 12px;
    font-weight: 600;
    min-width: 24px;
    height: 20px;
    line-height: 16px;
  }
}

.batchSync {
  max-height: calc(100vh - 300px);
  overflow: auto;

  :global(.ant-form-item) {
    margin-bottom: 4px;
  }

  :global(.ant-form-item-extra) {
    padding-top: 0;
  }
}

// Modern Table Styles
.modernTableCard {
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08) !important;
  border: none !important;
  overflow: hidden;
  
  :global(.ant-card-body) {
    padding: 0 !important;
  }
}

.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
  border-bottom: 1px solid #e8f0ff;
  flex-wrap: wrap;
  gap: 16px;
}

.tableHeaderLeft {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
  min-width: 300px;
}

.tableHeaderRight {
  display: flex;
  align-items: center;
}

.searchInput {
  width: 280px;
  height: 40px;
  border-radius: 12px;
  border: 1px solid #e8f0ff;
  background: white;
  
  :global(.ant-input) {
    border: none;
    box-shadow: none;
    font-size: 14px;
    
    &::placeholder {
      color: #bfbfbf;
    }
  }
  
  &:hover, &:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
  }
}

.filterGroup {
  :global(.ant-radio-button-wrapper) {
    border-radius: 8px;
    border: 1px solid #e8f0ff;
    background: white;
    color: #666;
    font-weight: 500;
    
    &:first-child {
      border-radius: 8px 0 0 8px;
    }
    
    &:last-child {
      border-radius: 0 8px 8px 0;
    }
    
    &:hover {
      color: #667eea;
      border-color: #667eea;
    }
  }
  
  :global(.ant-radio-button-wrapper-checked) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
    
    &:hover {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-color: #667eea;
      color: white;
    }
  }
}

.actionButton {
  height: 40px;
  border-radius: 10px;
  border: 1px solid #e8f0ff;
  background: white;
  color: #667eea;
  font-weight: 500;
  
  &:hover {
    background: #667eea;
    border-color: #667eea;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }
}

.primaryButton {
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  
  &:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
  }
}

.dropdownMenu {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: none;
  overflow: hidden;
  
  :global(.ant-dropdown-menu-item) {
    padding: 12px 16px;
    
    &:hover {
      background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
    }
  }
}

.modernTable {
  :global(.ant-table) {
    background: transparent;
  }
  
  :global(.ant-table-thead > tr > th) {
    background: #fafbff;
    border-bottom: 2px solid #e8f0ff;
    color: #2c3e50;
    font-weight: 600;
    font-size: 14px;
    padding: 16px 24px;
  }
  
  :global(.ant-table-tbody > tr) {
    transition: all 0.2s ease;
    
    &:hover {
      background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
  }
  
  :global(.ant-table-tbody > tr > td) {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f2f5;
  }
}

.hostInfo {
  .hostName {
    margin-bottom: 4px;
  }
  
  .hostNameLink {
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
    
    &:hover {
      color: #667eea;
    }
  }
  
  .hostMeta {
    color: #8c8c8c;
    font-size: 13px;
  }
}

.ipAddresses {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.statusTag {
  border-radius: 12px;
  font-weight: 500;
  padding: 4px 12px;
  border: none;
  
  &.ant-tag-success {
    background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
    color: white;
  }
  
  &.ant-tag-warning {
    background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
    color: white;
  }
}

.actionButtons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  
  .editButton {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    color: #0369a1;
    border-radius: 8px;
    min-width: 60px;
    height: 32px;
    padding: 4px 12px;
    font-size: 13px;
    
    &:hover {
      background: #0369a1;
      border-color: #0369a1;
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(3, 105, 161, 0.3);
    }
  }
  
  .gpuButton {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    border-radius: 8px;
    min-width: 80px;
    height: 32px;
    padding: 4px 12px;
    font-size: 13px;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
    
    &:hover {
      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
  }
  
  .deleteButton {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    border-radius: 8px;
    min-width: 60px;
    height: 32px;
    padding: 4px 12px;
    font-size: 13px;
    
    &:hover {
      background: #dc2626;
      border-color: #dc2626;
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
    }
  }
}

.tablePagination {
  padding: 16px 24px;
  background: #fafbff;
  border-top: 1px solid #e8f0ff;
  
  :global(.ant-pagination-item) {
    border-radius: 8px;
    border: 1px solid #e8f0ff;
    
    &:hover {
      border-color: #667eea;
    }
  }
  
  :global(.ant-pagination-item-active) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    
    a {
      color: white;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .headerSection {
    padding: 20px 12px;
  }
  
  .headerContent {
    flex-direction: column;
    gap: 16px;
    text-align: center;
    padding: 0 12px;
  }
  
  .mainContent {
    padding: 0 12px 16px;
  }
  
  .headerTitle {
    font-size: 24px !important;
  }
  
  .headerSubtitle {
    font-size: 14px;
  }
  
  .tableHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .tableHeaderLeft {
    flex-direction: column;
    min-width: auto;
  }
  
  .searchInput {
    width: 100%;
  }
  
  .actionButtons {
    flex-direction: column;
    
    .editButton,
    .gpuButton,
    .deleteButton {
      width: 100%;
      margin-bottom: 4px;
    }
  }
}

// Modern Group Card Styles
.modernGroupCard {
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08) !important;
  border: none !important;
  overflow: hidden;
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  
  :global(.ant-card-body) {
    padding: 0 !important;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.groupHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
  border-bottom: 1px solid #e8f0ff;
}

.groupTitle {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  
  .groupTitleIcon {
    font-size: 20px;
    color: #667eea;
  }
}

.groupControls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dragSwitch {
  :global(.ant-switch) {
    background: #e8f0ff;
    
    &.ant-switch-checked {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }
  
  :global(.ant-switch-inner) {
    font-size: 12px;
    font-weight: 500;
  }
}

.groupContent {
  flex: 1;
  padding: 16px 24px 24px;
  overflow: auto;
}

.groupSpin {
  :global(.ant-spin-container) {
    height: 100%;
  }
}

.modernTree {
  :global(.ant-tree) {
    background: transparent;
  }
  
  :global(.ant-tree-treenode) {
    padding: 4px 0;
    
    &:hover {
      background: rgba(102, 126, 234, 0.06);
      border-radius: 8px;
    }
  }
  
  :global(.ant-tree-node-selected) {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%) !important;
    border-radius: 8px;
    
    .treeNode {
      .title {
        color: #667eea;
        font-weight: 600;
      }
    }
  }
  
  :global(.ant-tree-switcher) {
    display: flex;
    align-items: center;
    justify-content: center;
    
    .anticon {
      color: #667eea;
    }
  }
  
  :global(.ant-tree-indent-unit) {
    width: 20px;
  }
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  height: 200px;
  
  .emptyIcon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }
  
  .emptyText {
    font-size: 16px;
    color: #8c8c8c;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  .emptySubtext {
    font-size: 14px;
    color: #bfbfbf;
  }
}

// Enhanced tree node styles
.treeNode {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
  margin: 2px 0;

  &:hover {
    background: rgba(102, 126, 234, 0.06);
  }

  .title {
    margin-left: 12px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    font-weight: 500;
    color: #2c3e50;
    font-size: 14px;
  }

  .number {
    min-width: 24px;
    height: 20px;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 2px 8px;
    font-size: 11px;
    font-weight: 600;
    line-height: 16px;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
  }
  
  :global(.anticon) {
    color: #667eea;
    font-size: 16px;
  }
}