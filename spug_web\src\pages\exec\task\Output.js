/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { PageHeader } from 'antd';
import {
  LoadingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CodeOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import style from './index.module.less';
import { http, X_TOKEN } from 'libs';
import store from './store';
import gStore from 'gStore';
import ModernLogViewer from './ModernLogViewer';

let gCurrent;

function OutView(props) {
  const [current, setCurrent] = useState(Object.keys(store.outputs)[0]);
  const [logContent, setLogContent] = useState('### WebSocket connecting ...');

  useEffect(() => {
    store.tag = '';
    gCurrent = current;
    
    // 更新日志内容
    if (store.outputs[current]) {
      setLogContent(store.outputs[current].data);
    }
    
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [current])

  useEffect(() => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const socket = new WebSocket(`${protocol}//${window.location.host}/api/ws/subscribe/${store.token}/?x-token=${X_TOKEN}`);
    socket.onopen = () => {
      const message = '### Waiting for scheduling ...'
      for (let key of Object.keys(store.outputs)) {
        store.outputs[key].data = message
      }
      setLogContent(message);
      socket.send('ok');
      
      // 发送终端尺寸
      const formData = {
        token: store.token,
        cols: 80,
        rows: 24
      }
      http.patch('/api/exec/do/', formData)
    }
    socket.onmessage = e => {
      if (e.data === 'pong') {
        socket.send('ping')
      } else {
        _handleData(e.data)
      }
    }
    socket.onclose = () => {
      const failMessage = 'Websocket connection failed!';
      for (let key of Object.keys(store.outputs)) {
        if (store.outputs[key].status === -2) {
          store.outputs[key].status = -1
        }
        store.outputs[key].data += '\n' + failMessage
      }
      setLogContent(prev => prev + '\n' + failMessage);
    }
    return () => socket && socket.close()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  function _handleData(message) {
    const {key, data, status} = JSON.parse(message);
    if (status !== undefined) {
      store.outputs[key].status = status;
    }
    if (data) {
      store.outputs[key].data += data
      if (String(key) === gCurrent) {
        setLogContent(store.outputs[key].data);
      }
    }
  }

  function handleSwitch(key) {
    setCurrent(key)
    gCurrent = key
    setLogContent(store.outputs[key].data)
  }

  function openTerminal(key) {
    window.open(`/ssh?id=${key}`)
  }

  const {tag, items, counter} = store
  return (
    <div className={style.output}>
      <div className={style.side}>
        <PageHeader onBack={props.onBack} title="执行详情"/>
        <div className={style.tags}>
          <div
            className={`${style.item} ${tag === '0' ? style.pendingOn : style.pending}`}
            onClick={() => store.updateTag('0')}>
            <ClockCircleOutlined/>
            <div>{counter['0']}</div>
          </div>
          <div
            className={`${style.item} ${tag === '1' ? style.successOn : style.success}`}
            onClick={() => store.updateTag('1')}>
            <CheckCircleOutlined/>
            <div>{counter['1']}</div>
          </div>
          <div
            className={`${style.item} ${tag === '2' ? style.failOn : style.fail}`}
            onClick={() => store.updateTag('2')}>
            <ExclamationCircleOutlined/>
            <div>{counter['2']}</div>
          </div>
        </div>

        <div className={style.list}>
          {items.map(([key, item]) => (
            <div key={key} className={[style.item, key === current ? style.active : ''].join(' ')}
                 onClick={() => handleSwitch(key)}>
              {item.status === -2 ? (
                <LoadingOutlined style={{color: '#1890ff'}}/>
              ) : item.status === 0 ? (
                <CheckCircleOutlined style={{color: '#52c41a'}}/>
              ) : (
                <ExclamationCircleOutlined style={{color: 'red'}}/>
              )}
              <div className={style.text}>{item.title}</div>
            </div>
          ))}
        </div>
      </div>
      <div className={style.body}>
        <div className={style.header}>
          <div className={style.title}>{store.outputs[current].title}</div>
          <CodeOutlined className={style.icon} onClick={() => openTerminal(current)}/>
        </div>
        <ModernLogViewer 
          content={logContent}
          onBack={props.onBack}
        />
      </div>
    </div>
  )
}

export default observer(OutView)