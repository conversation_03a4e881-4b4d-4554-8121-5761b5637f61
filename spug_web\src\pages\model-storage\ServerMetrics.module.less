// 服务器监控现代化样式
.serverMetricsContainer {
  .compactCard {
    margin-bottom: 0;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    backdrop-filter: blur(20px);
    overflow: hidden;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
    }
    
    :global(.ant-card-head) {
      background: transparent;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding: 16px 20px;
      
      :global(.ant-card-head-title) {
        color: white;
        font-weight: 600;
        font-size: 20px;
      }
    }
    
    :global(.ant-card-body) {
      padding: 20px;
      background: rgba(255, 255, 255, 0.95);
    }
  }
  
  .fullCard {
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    border: none;
    background: white;
    overflow: hidden;
    
    :global(.ant-card-head) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-bottom: none;
      padding: 20px 24px;
      
      :global(.ant-card-head-title) {
        color: white;
        font-weight: 600;
        font-size: 18px;
      }
    }
    
    :global(.ant-card-body) {
      padding: 24px;
    }
  }
  
  .metricCard {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    background: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }
    
    :global(.ant-card-body) {
      padding: 20px;
      text-align: center;
    }
  }
  
  .cpuCard {
    .metricCard();
    border-left: 4px solid #1890ff;
  }
  
  .memoryCard {
    .metricCard();
    border-left: 4px solid #52c41a;
  }
  
  .diskCard {
    .metricCard();
    border-left: 4px solid #fa8c16;
  }
  
  .metricTitle {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-top: 12px;
  }
  
  .compactMetric {
    text-align: center;
    
    .compactProgress {
      :global(.ant-progress-circle) {
        :global(.ant-progress-text) {
          color: #2d3748;
          font-weight: 600;
          font-size: 16px;
        }
      }
    }
    
    .compactTitle {
      font-size: 14px;
      margin-top: 4px;
      color: #4a5568;
      font-weight: 500;
    }
  }
  
  .hostInfo {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .hostText {
      color: rgba(255, 255, 255, 0.9);
      font-size: 14px;
    }
    
    .updateTime {
      color: rgba(255, 255, 255, 0.7);
      font-size: 14px;
    }
  }
  
  .fullHostInfo {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .hostText {
      color: rgba(255, 255, 255, 0.9);
      font-size: 16px;
    }
    
    .updateTime {
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
    }
  }
  
  // 进度条自定义样式
  :global(.ant-progress-circle) {
    :global(.ant-progress-text) {
      font-weight: 600;
      font-size: 16px;
    }
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    .compactCard {
      :global(.ant-card-body) {
        padding: 16px;
      }
    }
    
    .fullCard {
      :global(.ant-card-body) {
        padding: 16px;
      }
    }
    
    .metricCard {
      :global(.ant-card-body) {
        padding: 16px;
      }
    }
    
    .metricTitle {
      font-size: 14px;
    }
  }
}

// 状态指示器
.statusIndicator {
  &.normal {
    :global(.ant-progress-circle-path) {
      stroke: #52c41a;
    }
  }
  
  &.warning {
    :global(.ant-progress-circle-path) {
      stroke: #fa8c16;
    }
  }
  
  &.danger {
    :global(.ant-progress-circle-path) {
      stroke: #ff4d4f;
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulseAnimation {
  animation: pulse 2s infinite;
}