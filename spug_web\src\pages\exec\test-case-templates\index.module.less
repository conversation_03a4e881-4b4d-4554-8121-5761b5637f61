.container {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.searchBar {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 16px;
}

.tableCard {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.templateCard {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background: white;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #1890ff;
  }
}

.templateHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.templateTitle {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.templateDescription {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.templateMeta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.templateContent {
  margin-top: 12px;
}

.contentSection {
  margin-bottom: 12px;
}

.sectionLabel {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  margin-bottom: 4px;
}

.sectionContent {
  font-size: 14px;
  color: #262626;
  line-height: 1.5;
  white-space: pre-wrap;
  background: #fafafa;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.actions {
  display: flex;
  gap: 8px;
}

.statusTag {
  &.enabled {
    background: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }
  
  &.disabled {
    background: #fff2f0;
    border-color: #ffccc7;
    color: #ff4d4f;
  }
}

.categoryTag {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.modalForm {
  .ant-form-item-label {
    font-weight: 500;
  }
}

.formRow {
  display: flex;
  gap: 16px;
  
  .ant-form-item {
    flex: 1;
  }
}

.textareaField {
  .ant-input {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
  }
}

.emptyState {
  text-align: center;
  padding: 60px 20px;
  color: #999;
  
  .ant-empty-image {
    margin-bottom: 16px;
  }
  
  .emptyTitle {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
  }
  
  .emptyDescription {
    font-size: 14px;
    color: #999;
  }
}

.loadingState {
  text-align: center;
  padding: 60px 20px;
  
  .ant-spin {
    margin-bottom: 16px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .searchBar {
    flex-direction: column;
    align-items: stretch;
    
    .ant-input,
    .ant-select {
      width: 100% !important;
    }
  }
  
  .templateHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .formRow {
    flex-direction: column;
  }
}

// 表格样式优化
:global {
  .ant-table-thead > tr > th {
    background: #fafafa;
    font-weight: 600;
  }
  
  .ant-table-tbody > tr:hover > td {
    background: #f5f5f5;
  }
  
  .ant-table-cell {
    padding: 12px 16px;
  }
}
