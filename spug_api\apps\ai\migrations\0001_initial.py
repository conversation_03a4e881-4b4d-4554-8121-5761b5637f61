# Generated by Django 2.2.28 on 2025-07-27 13:18

from django.db import migrations, models
import libs.mixins


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AIAnalysisLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filename', models.CharField(max_length=255, verbose_name='文件名')),
                ('content_hash', models.CharField(max_length=64, verbose_name='内容哈希')),
                ('analysis_type', models.CharField(default='log_analysis', max_length=50, verbose_name='分析类型')),
                ('analysis_result', models.TextField(verbose_name='分析结果')),
                ('success', models.BooleanField(default=True, verbose_name='分析成功')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('processing_time', models.FloatField(default=0, verbose_name='处理时间(秒)')),
                ('token_usage', models.IntegerField(default=0, verbose_name='Token使用量')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': 'AI分析日志',
                'verbose_name_plural': 'AI分析日志',
                'db_table': 'ai_analysis_log',
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='AIConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ai_enabled', models.BooleanField(default=False, verbose_name='启用AI分析')),
                ('api_base', models.CharField(default='https://api-inference.modelscope.cn/v1', max_length=255, verbose_name='API基础地址')),
                ('api_key', models.CharField(blank=True, max_length=255, verbose_name='API密钥')),
                ('model_name', models.CharField(default='qwen/Qwen2.5-72B-Instruct', max_length=100, verbose_name='模型名称')),
                ('max_tokens', models.IntegerField(default=4000, verbose_name='最大Token数')),
                ('temperature', models.FloatField(default=0.7, verbose_name='温度参数')),
                ('cache_enabled', models.BooleanField(default=True, verbose_name='启用缓存')),
                ('cache_ttl', models.IntegerField(default=3600, verbose_name='缓存过期时间(秒)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': 'AI配置',
                'verbose_name_plural': 'AI配置',
                'db_table': 'ai_config',
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.AddIndex(
            model_name='aianalysislog',
            index=models.Index(fields=['filename', 'content_hash'], name='ai_analysis_filenam_3e4091_idx'),
        ),
        migrations.AddIndex(
            model_name='aianalysislog',
            index=models.Index(fields=['created_at'], name='ai_analysis_created_5020d2_idx'),
        ),
    ]
