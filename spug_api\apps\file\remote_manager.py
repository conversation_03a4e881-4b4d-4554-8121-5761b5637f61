# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.

import os
import platform
import subprocess
import tempfile
import time
import shutil
import uuid
from pathlib import Path
from urllib.parse import urlparse
import logging
from datetime import datetime
from django.conf import settings
from paramiko import SSHClient, AutoAddPolicy, SSHException

try:
    from smbprotocol.connection import Connection
    from smbprotocol.session import Session
    from smbprotocol.tree import TreeConnect
    from smbprotocol.file import File
    from smbprotocol.open import CreateDisposition, CreateOptions, FileAttributes, ShareAccess
    from smbprotocol.exceptions import SMBException
    SMB_AVAILABLE = True
except ImportError:
    SMB_AVAILABLE = False

logger = logging.getLogger('apps.file')

class RemoteFileManager:
    """远程文件管理器"""
    
    def __init__(self, remote_path, username, password, domain=None):
        self.remote_path = remote_path
        self.username = username
        self.password = password
        self.domain = domain
        self.client = None
        self.sftp = None
        self.connection_type = None
        
        # 解析Windows网络路径
        if self.remote_path.startswith('\\\\') or self.remote_path.startswith('//'):
            path_parts = self.remote_path.replace('\\', '/').strip('/').split('/')
            if len(path_parts) >= 2:
                self.server = path_parts[0]
                self.share = path_parts[1]
                self.sub_path = '/'.join(path_parts[2:]) if len(path_parts) > 2 else ''
            else:
                self.server = None
                self.share = None
                self.sub_path = ''
    
    def connect(self):
        """连接到远程服务器"""
        # 检查是否为Windows网络共享路径
        if self.remote_path.startswith('\\\\') or self.remote_path.startswith('//'):
            return self._connect_smb()
        
        # 默认SSH连接
        try:
            self.client = SSHClient()
            self.client.set_missing_host_key_policy(AutoAddPolicy())
            self.client.connect(
                hostname=self.domain or self.remote_path.split(':')[0],
                username=self.username,
                password=self.password,
                timeout=10
            )
            self.sftp = self.client.open_sftp()
            return True
        except SSHException as e:
            logger.error(f"连接远程服务器失败: {e}")
            return False
        except Exception as e:
            logger.error(f"连接远程服务器时发生错误: {e}")
            return False
    
    def _connect_smb(self):
        """连接到Windows网络共享"""
        if not self.server or not self.share:
            logger.error("无效的网络共享路径")
            return False
        
        self.connection_type = 'smb'
        
        # Windows环境使用net use命令
        if platform.system() == 'Windows':
            return self._connect_windows()
        else:
            # Linux环境使用SMB协议库
            return self._connect_linux()
    
    def disconnect(self):
        """断开连接"""
        if self.connection_type == 'smb':
            if platform.system() == 'Windows':
                self._disconnect_windows()
            else:
                self._disconnect_linux()
        else:
            if self.sftp:
                self.sftp.close()
            if self.client:
                self.client.close()
    
    def list_files(self, path=""):
        """列出远程文件夹中的文件"""
        if self.connection_type == 'smb':
            if platform.system() == 'Windows':
                return self._list_files_windows(path)
            else:
                return self._list_files_linux(path)
        
        # SSH连接的处理
        if not self.sftp:
            return []
        
        try:
            remote_path = self._get_full_path(path)
            files = []
            
            for item in self.sftp.listdir_attr(remote_path):
                is_dir = item.st_mode & 0o40000 != 0
                files.append({
                    'name': item.filename,
                    'size': item.st_size,
                    'type': 'dir' if is_dir else 'file',
                    'modified': datetime.fromtimestamp(item.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
            
            return files
        except Exception as e:
            logger.error(f"列出远程文件失败: {e}")
            return []
    
    def find_file_by_name(self, directory, filename, recursive=True, max_depth=5):
        """在指定目录中查找文件，支持递归查找
        
        Args:
            directory: 开始查找的目录
            filename: 要查找的文件名
            recursive: 是否递归搜索子目录
            max_depth: 最大递归深度
            
        Returns:
            str: 找到的文件完整路径，如果未找到则返回None
        """
        logger.info(f"开始在目录 {directory} 中查找文件 {filename} {'(递归)' if recursive else '(非递归)'}")
        
        # 对于SMB连接，使用Windows文件系统API查找
        if self.connection_type == 'smb' and platform.system() == 'Windows':
            try:
                # 确保目录存在
                if not os.path.exists(directory):
                    logger.error(f"查找目录不存在: {directory}")
                    return None
                
                logger.info(f"目录存在，开始查找过程")
                
                # 首先在当前目录中查找
                potential_path = os.path.join(directory, filename)
                logger.info(f"检查当前目录文件: {potential_path}")
                if os.path.exists(potential_path) and os.path.isfile(potential_path):
                    logger.info(f"在当前目录找到文件: {potential_path}")
                    return potential_path
                else:
                    logger.info(f"当前目录中不存在该文件")
                
                # 如果不递归，直接返回None
                if not recursive:
                    logger.info(f"非递归模式，停止搜索")
                    return None
                
                logger.info(f"开始递归搜索子目录...")
                
                # 遍历所有子目录
                search_count = 0
                for root, dirs, files in os.walk(directory):
                    search_count += 1
                    logger.info(f"搜索第{search_count}个目录: {root}")
                    logger.info(f"  - 包含{len(dirs)}个子目录: {dirs[:5]}" + ("..." if len(dirs) > 5 else ""))
                    logger.info(f"  - 包含{len(files)}个文件: {files[:5]}" + ("..." if len(files) > 5 else ""))
                    
                    if filename in files:
                        found_path = os.path.join(root, filename)
                        logger.info(f"✅ 在子目录找到文件: {found_path}")
                        return found_path
                    
                    # 限制搜索深度，避免搜索过深
                    if search_count >= 50:
                        logger.warning(f"搜索目录数量超过50个，停止搜索以避免性能问题")
                        break
                
                logger.warning(f"在目录 {directory} 及其{search_count}个子目录中未找到文件 {filename}")
                return None
            except Exception as e:
                logger.error(f"查找文件时出错: {str(e)}")
                return None
        
        # 对于SFTP连接，通过SFTP API查找
        elif self.sftp:
            try:
                # 首先检查文件是否在当前目录
                try:
                    file_path = os.path.join(directory, filename).replace('\\', '/')
                    self.sftp.stat(file_path)
                    logger.info(f"在当前目录找到文件: {file_path}")
                    return file_path
                except:
                    pass  # 文件不在当前目录，继续搜索
                
                # 如果不递归，直接返回None
                if not recursive:
                    logger.info(f"非递归模式，仅检查当前目录")
                    return None
                
                def search_recursive(current_dir, current_depth=0):
                    if current_depth > max_depth:
                        return None
                    
                    try:
                        # 获取目录中的所有文件和子目录
                        items = self.sftp.listdir_attr(current_dir)
                        
                        # 先检查当前目录中的文件
                        for item in items:
                            if not (item.st_mode & 0o40000) and item.filename == filename:  # 是文件且名称匹配
                                found_path = os.path.join(current_dir, item.filename).replace('\\', '/')
                                logger.info(f"在目录 {current_dir} 找到文件: {found_path}")
                                return found_path
                        
                        # 然后递归检查子目录
                        for item in items:
                            if item.st_mode & 0o40000:  # 是目录
                                subdir = os.path.join(current_dir, item.filename).replace('\\', '/')
                                found = search_recursive(subdir, current_depth + 1)
                                if found:
                                    return found
                        
                        return None
                    except Exception as e:
                        logger.warning(f"搜索目录 {current_dir} 时出错: {str(e)}")
                        return None
                
                # 开始递归搜索
                return search_recursive(directory)
                
            except Exception as e:
                logger.error(f"通过SFTP查找文件时出错: {str(e)}")
                return None
        
        logger.error("没有可用的连接来查找文件")
        return None

    def download_file(self, remote_file_path, local_file_path):
        """下载远程文件
        
        Args:
            remote_file_path: 远程文件的完整路径
            local_file_path: 本地保存的文件路径
            
        Returns:
            bool: 下载是否成功
        """
        logger.info(f"开始下载远程文件: {remote_file_path} 到 {local_file_path}")
        
        if self.connection_type == 'smb':
            # Windows网络共享处理
            try:
                # 确保目标目录存在
                os.makedirs(os.path.dirname(local_file_path), exist_ok=True)
                
                # 对于Windows环境，直接使用文件复制
                if platform.system() == 'Windows':
                    # 确保路径格式正确（Windows格式）
                    remote_file_path = remote_file_path.replace('/', '\\')
                    
                    # 检查文件是否存在
                    if not os.path.exists(remote_file_path):
                        logger.error(f"远程文件不存在: {remote_file_path}")
                        
                        # 尝试查找文件的真实路径
                        base_dir = os.path.dirname(remote_file_path)
                        filename = os.path.basename(remote_file_path)
                        found_path = self.find_file_by_name(base_dir, filename)
                        
                        if found_path:
                            logger.info(f"找到文件的实际路径: {found_path}")
                            remote_file_path = found_path
                        else:
                            # 尝试从父目录查找
                            parent_dir = os.path.dirname(base_dir)
                            if parent_dir and parent_dir != base_dir:
                                found_path = self.find_file_by_name(parent_dir, filename)
                                if found_path:
                                    logger.info(f"在父目录中找到文件: {found_path}")
                                    remote_file_path = found_path
                                else:
                                    return False
                            else:
                                return False
                    
                    # 复制文件
                    logger.info(f"Windows环境: 复制文件 {remote_file_path} 到 {local_file_path}")
                    shutil.copy2(remote_file_path, local_file_path)
                    
                    # 验证文件是否成功复制
                    if os.path.exists(local_file_path) and os.path.getsize(local_file_path) > 0:
                        logger.info(f"文件下载成功: {local_file_path}")
                        return True
                    else:
                        logger.error(f"文件复制失败或文件为空: {local_file_path}")
                        return False
                else:
                    # Linux环境下的SMB处理
                    # ...实现省略
                    logger.error("Linux环境下的SMB文件下载未实现")
                    return False
            except Exception as e:
                logger.error(f"SMB下载文件失败: {str(e)}")
                return False
        
        # SSH连接处理
        if not self.sftp:
            logger.error("SFTP连接未初始化")
            return False
        
        try:
            logger.info(f"SSH/SFTP: 下载文件 {remote_file_path} 到 {local_file_path}")
            
            # 确保目标目录存在
            os.makedirs(os.path.dirname(local_file_path), exist_ok=True)
            
            # Windows环境下的特殊处理
            if platform.system() == 'Windows':
                # 尝试直接访问文件（如果是本地文件系统或已映射的网络驱动器）
                if os.path.exists(remote_file_path):
                    try:
                        logger.info(f"Windows环境: 检测到远程文件可直接访问，使用文件复制: {remote_file_path}")
                        shutil.copy2(remote_file_path, local_file_path)
                        
                        if os.path.exists(local_file_path) and os.path.getsize(local_file_path) > 0:
                            logger.info(f"文件复制成功: {local_file_path}")
                            return True
                    except Exception as e:
                        logger.warning(f"直接复制文件失败，尝试使用SFTP: {str(e)}")
                        # 继续使用SFTP尝试
            
            # 使用SFTP下载
            try:
                # 检查文件是否存在
                try:
                    self.sftp.stat(remote_file_path)
                except:
                    logger.error(f"SFTP: 远程文件不存在: {remote_file_path}")
                    
                    # 尝试查找文件的真实路径
                    base_dir = os.path.dirname(remote_file_path)
                    filename = os.path.basename(remote_file_path)
                    found_path = self.find_file_by_name(base_dir, filename)
                    
                    if found_path:
                        logger.info(f"找到文件的实际路径: {found_path}")
                        remote_file_path = found_path
                    else:
                        # 尝试从父目录查找
                        parent_dir = os.path.dirname(base_dir)
                        if parent_dir and parent_dir != base_dir:
                            found_path = self.find_file_by_name(parent_dir, filename)
                            if found_path:
                                logger.info(f"在父目录中找到文件: {found_path}")
                                remote_file_path = found_path
                            else:
                                raise Exception("找不到远程文件")
                        else:
                            raise Exception("找不到远程文件")
                
                self.sftp.get(remote_file_path, local_file_path)
            except Exception as e:
                # 如果SFTP下载失败，尝试调整路径格式再试一次
                logger.warning(f"SFTP下载失败，尝试调整路径格式: {str(e)}")
                
                # 尝试替换路径分隔符
                alt_remote_path = remote_file_path.replace('\\', '/')
                if alt_remote_path != remote_file_path:
                    logger.info(f"尝试使用替代路径: {alt_remote_path}")
                    try:
                        self.sftp.get(alt_remote_path, local_file_path)
                    except Exception as e2:
                        logger.error(f"使用替代路径下载也失败: {str(e2)}")
                        return False
                else:
                    # 没有替代路径可尝试
                    return False
            
            # 验证文件是否成功下载
            if os.path.exists(local_file_path) and os.path.getsize(local_file_path) > 0:
                logger.info(f"文件下载成功: {local_file_path}")
                return True
            else:
                logger.error(f"文件下载失败或文件为空: {local_file_path}")
                return False
        except Exception as e:
            logger.error(f"SFTP下载文件失败: {str(e)}")
            return False
    
    def upload_file(self, local_file, remote_path, filename):
        """上传文件到远程服务器"""
        if not self.sftp:
            return False
        
        try:
            full_remote_path = self._get_full_path(remote_path)
            remote_file = os.path.join(full_remote_path, filename).replace('\\', '/')
            
            # 上传文件
            self.sftp.put(local_file, remote_file)
            
            return True
        except Exception as e:
            logger.error(f"上传文件到远程服务器失败: {e}")
            return False
    
    def _get_full_path(self, path):
        """获取完整的远程路径"""
        base_path = self.remote_path.split(':')[1] if ':' in self.remote_path else self.remote_path
        if not path:
            return base_path
        return os.path.join(base_path, path).replace('\\', '/')
    
    def _connect_windows(self):
        """Windows环境下的连接 - 使用net use命令"""
        try:
            # 构建网络驱动器路径
            network_path = f"\\\\{self.server}\\{self.share}"
            
            # 构建完整的目标路径（包含子路径）
            if self.sub_path:
                target_path = os.path.join(network_path, self.sub_path).replace('/', '\\')
            else:
                target_path = network_path
            
            # 首先检查是否已经连接
            check_cmd = ['net', 'use']
            check_result = subprocess.run(check_cmd, capture_output=True, text=True, shell=True)
            
            if check_result.returncode == 0 and network_path in check_result.stdout:
                # 已经连接，检查是否可以访问目标路径
                if os.path.exists(target_path):
                    return True
                else:
                    # 连接存在但无法访问目标路径，可能是权限问题或路径不存在
                    if os.path.exists(network_path):
                        # 网络路径可访问但子路径不存在
                        logger.error(f"网络共享连接成功，但目标路径不存在: {target_path}")
                        return False
                    else:
                        # 连接存在但无法访问，可能需要重新连接
                        self._disconnect_windows()
            
            # 优先尝试不认证的连接（使用当前用户身份）
            cmd = ['net', 'use', network_path]
            
            # 先尝试无认证连接
            result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                # 无认证连接成功，检查目标路径是否可访问
                if os.path.exists(target_path):
                    return True
                else:
                    logger.error(f"网络共享连接成功，但目标路径不存在: {target_path}")
                    return False
            
            # 如果无认证连接失败且提供了用户名密码，尝试认证连接
            if self.username and self.password:
                # 先断开可能存在的连接（避免1219错误）
                self._disconnect_windows()
                
                cmd = [
                    'net', 'use', network_path,
                    f'/user:{self.domain}\\{self.username}' if self.domain else f'/user:{self.username}',
                    self.password
                ]
                
                # 执行认证连接
                result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
            else:
                # 既没有无认证连接成功，也没有提供认证信息
                print(f"无认证连接失败，且未提供认证信息: {result.stderr}")
                return False
            
            if result.returncode == 0:
                # 认证连接成功，检查目标路径是否可访问
                if os.path.exists(target_path):
                    return True
                else:
                    logger.error(f"网络共享连接成功，但目标路径不存在: {target_path}")
                    return False
            elif "already connected" in result.stderr or "已连接" in result.stderr or "1219" in result.stderr:
                # 已经连接或多重连接错误，检查是否可以访问目标路径
                if os.path.exists(target_path):
                    return True
                else:
                    logger.error(f"网络共享已连接，但目标路径不存在: {target_path}")
                    return False
            else:
                print(f"连接失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"Windows连接异常: {str(e)}")
            return False
    
    def _connect_linux(self):
        """Linux环境下的连接 - 使用SMB协议库"""
        if not SMB_AVAILABLE:
            raise ImportError("需要安装 smbprotocol: pip install smbprotocol")
        
        try:
            self.connection = Connection(uuid.uuid4(), self.server, 445)
            self.connection.connect()
            
            self.session = Session(self.connection, self.username, self.password, domain=self.domain)
            self.session.connect()
            
            self.tree = TreeConnect(self.session, f"\\\\{self.server}\\{self.share}")
            self.tree.connect()
            
            return True
        except Exception as e:
            print(f"Linux连接异常: {str(e)}")
            return False
    
    def _disconnect_windows(self):
        """Windows断开连接"""
        try:
            network_path = f"\\\\{self.server}\\{self.share}"
            subprocess.run(['net', 'use', network_path, '/delete', '/y'], 
                         capture_output=True, shell=True)
        except:
            pass
    
    def _disconnect_linux(self):
        """Linux断开连接"""
        try:
            if hasattr(self, 'tree'):
                self.tree.disconnect()
            if hasattr(self, 'session'):
                self.session.disconnect()
            if hasattr(self, 'connection'):
                self.connection.disconnect()
        except:
            pass
    
    def _list_files_windows(self, path):
        """Windows下列出文件"""
        try:
            # 基础网络路径
            network_path = f"\\\\{self.server}\\{self.share}"
            
            # 构建完整路径，包含初始化时解析的子路径
            if self.sub_path:
                # 如果有子路径，先添加子路径
                base_path = os.path.join(network_path, self.sub_path).replace('/', '\\')
            else:
                base_path = network_path
            
            # 再添加当前浏览的路径
            if path:
                full_path = os.path.join(base_path, path).replace('/', '\\')
            else:
                full_path = base_path
            
            files = []
            
            # 使用dir命令列出文件
            cmd = ['dir', '/B', '/A', full_path]
            result = subprocess.run(cmd, capture_output=True, text=True, shell=True, encoding='gbk')
            
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        item_path = os.path.join(full_path, line.strip())
                        
                        # 获取详细信息
                        cmd_detail = ['dir', '/Q', item_path]
                        detail_result = subprocess.run(cmd_detail, capture_output=True, text=True, shell=True, encoding='gbk')
                        
                        is_dir = os.path.isdir(item_path) if os.path.exists(item_path) else '<DIR>' in detail_result.stdout
                        
                        files.append({
                            'name': line.strip(),
                            'type': 'folder' if is_dir else self._get_file_type(line.strip()),
                            'size': 0 if is_dir else self._get_file_size_windows(item_path),
                            'size_human': self._format_size(0 if is_dir else self._get_file_size_windows(item_path)),
                            'modified': self._get_file_mtime_windows(item_path),
                            'is_file': not is_dir
                        })
            
            return files
            
        except Exception as e:
            print(f"Windows文件列表获取失败: {str(e)}")
            return []
    
    def _list_files_linux(self, path):
        """Linux下列出文件（使用SMB协议）"""
        if not hasattr(self, 'tree'):
            return []
        
        try:
            files = []
            
            # 使用SMB协议列出文件
            query_path = path.replace('\\', '/') if path else ''
            
            # 这里需要使用smbprotocol的具体API
            # 由于复杂性，建议使用简化的方法或者mount方式
            
            return files
            
        except Exception as e:
            print(f"Linux文件列表获取失败: {str(e)}")
            return []
    
    def _get_file_size_windows(self, file_path):
        """获取Windows文件大小"""
        try:
            if os.path.exists(file_path):
                return os.path.getsize(file_path)
            return 0
        except:
            return 0
    
    def _get_file_mtime_windows(self, file_path):
        """获取Windows文件修改时间"""
        try:
            if os.path.exists(file_path):
                return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(os.path.getmtime(file_path)))
            return ''
        except:
            return ''
    
    def _get_file_type(self, filename):
        """判断文件类型"""
        ext = os.path.splitext(filename)[1].lower()
        
        script_exts = ['.sh', '.py', '.pl', '.rb', '.js', '.bat', '.cmd']
        code_exts = ['.html', '.css', '.js', '.json', '.xml', '.yml', '.yaml', '.ts', '.go', '.java', '.c', '.cpp']
        text_exts = ['.txt', '.md', '.log', '.conf', '.cfg', '.ini', '.properties']
        image_exts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico']
        archive_exts = ['.zip', '.tar', '.gz', '.bz2', '.7z', '.rar']
        doc_exts = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']
        
        if ext in script_exts:
            return 'script'
        elif ext in code_exts:
            return 'code'
        elif ext in text_exts:
            return 'text'
        elif ext in image_exts:
            return 'image'
        elif ext in archive_exts:
            return 'archive'
        elif ext in doc_exts:
            return 'document'
        else:
            return 'unknown'
    
    def _format_size(self, size):
        """格式化文件大小"""
        if size == 0:
            return '0 B'
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.1f} {units[unit_index]}"


class RemoteMountManager:
    """远程挂载管理器 - 用于Linux环境的CIFS挂载"""
    
    def __init__(self):
        self.mount_points = {}
    
    def mount_remote_folder(self, remote_path, username=None, password=None, domain=None):
        """
        挂载远程文件夹（Linux环境）
        
        Args:
            remote_path: 远程路径
            username: 用户名
            password: 密码
            domain: 域名
            
        Returns:
            str: 本地挂载点路径
        """
        if platform.system().lower() == 'windows':
            raise OSError("Windows环境请使用RemoteFileManager")
        
        # 创建临时挂载点
        mount_point = tempfile.mkdtemp(prefix='spug_remote_')
        
        try:
            # 构建mount.cifs命令
            cmd = ['sudo', 'mount', '-t', 'cifs', remote_path, mount_point]
            
            if username and password:
                credentials_file = tempfile.NamedTemporaryFile(mode='w', delete=False)
                credentials_file.write(f"username={username}\n")
                credentials_file.write(f"password={password}\n")
                if domain:
                    credentials_file.write(f"domain={domain}\n")
                credentials_file.close()
                
                cmd.extend(['-o', f'credentials={credentials_file.name},uid=1000,gid=1000,iocharset=utf8'])
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.mount_points[remote_path] = mount_point
                return mount_point
            else:
                os.rmdir(mount_point)
                raise Exception(f"挂载失败: {result.stderr}")
                
        except Exception as e:
            if os.path.exists(mount_point):
                os.rmdir(mount_point)
            raise e
    
    def unmount_remote_folder(self, remote_path):
        """卸载远程文件夹"""
        if remote_path in self.mount_points:
            mount_point = self.mount_points[remote_path]
            
            try:
                subprocess.run(['sudo', 'umount', mount_point], capture_output=True)
                os.rmdir(mount_point)
                del self.mount_points[remote_path]
            except:
                pass
    
    def cleanup(self):
        """清理所有挂载点"""
        for remote_path in list(self.mount_points.keys()):
            self.unmount_remote_folder(remote_path) 