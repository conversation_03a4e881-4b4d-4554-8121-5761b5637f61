.compareContent {
  .modeCard {
    margin-bottom: 16px;
    
    .modeLabel {
      font-weight: 500;
      margin-right: 8px;
    }
  }
  
  .summaryRow {
    margin-bottom: 16px;
  }
  
  .conclusionCard {
    margin-top: 16px;
    
    .conclusion {
      p {
        margin-bottom: 12px;
        font-size: 14px;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          font-size: 14px;
          line-height: 1.5;
          
          strong {
            color: #1890ff;
          }
        }
      }
    }
  }
}

.columnHeader {
  text-align: center;
  
  .planName {
    font-weight: 500;
    font-size: 13px;
    color: #1890ff;
  }
  
  .taskName {
    font-size: 11px;
    color: #666;
    margin-top: 2px;
  }
}

.statValue {
  font-weight: 500;
}

.metricValue {
  font-weight: 500;
  
  small {
    color: #666;
    margin-left: 4px;
  }
} 