<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试用例集管理 - 用户手册</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .version-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-top: 20px;
        }

        /* 导航栏 */
        .nav {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: #2c3e50;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #06b6d4;
        }

        /* 主要内容区域 */
        .main-content {
            padding: 60px 0;
        }

        .section {
            margin-bottom: 80px;
        }

        .section-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 50px;
        }

        /* 功能卡片 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            border: 1px solid #e9ecef;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #06b6d4;
        }

        .feature-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .feature-description {
            color: #7f8c8d;
            line-height: 1.6;
        }

        /* 步骤指南 */
        .steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .step {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            position: relative;
            border: 2px solid #e9ecef;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .step:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .step-number {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: #06b6d4;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .step-title {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .step-description {
            color: #7f8c8d;
        }

        /* 截图样式 */
        .screenshot {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            text-align: center;
            border: 2px dashed #dee2e6;
        }

        .screenshot img {
            max-width: 100%;
            border-radius: 8px;
        }

        .screenshot-placeholder {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .screenshot-caption {
            color: #6c757d;
            font-style: italic;
        }

        /* 提示框 */
        .tip, .warning, .info {
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid;
        }

        .tip {
            background: #f0f9ff;
            border-color: #0ea5e9;
            color: #0c4a6e;
        }

        .warning {
            background: #fef3c7;
            border-color: #f59e0b;
            color: #92400e;
        }

        .info {
            background: #ecfdf5;
            border-color: #10b981;
            color: #065f46;
        }

        /* 底部 */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .steps {
                grid-template-columns: 1fr;
            }
        }
        
        /* FAQ样式 */
        .faq-item {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .faq-question {
            color: #06b6d4;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .faq-answer {
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1><i class="fas fa-list-check"></i> 测试用例集管理</h1>
                <p>专业的测试用例集合管理与执行跟踪平台</p>
                <div class="version-badge">v1.0 用户手册</div>
            </div>
        </div>
    </header>

    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container">
            <div class="nav-content">
                <div class="logo">
                    <strong>用户手册</strong>
                </div>
                <ul class="nav-links">
                    <li><a href="#overview">系统概述</a></li>
                    <li><a href="#features">功能介绍</a></li>
                    <li><a href="#getting-started">快速开始</a></li>
                    <li><a href="#tutorials">使用教程</a></li>
                    <li><a href="#faq">常见问题</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 系统概述 -->
            <section id="overview" class="section">
                <h2 class="section-title">系统概述</h2>
                <p class="section-subtitle">测试用例集管理系统是一个专业的测试用例组织和管理平台，提供用例集创建、编辑、执行跟踪和结果分析功能</p>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-folder-plus"></i>
                        </div>
                        <h3 class="feature-title">用例集管理</h3>
                        <p class="feature-description">创建和管理测试用例集合，支持分类组织和批量操作</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-edit"></i>
                        </div>
                        <h3 class="feature-title">用例编辑</h3>
                        <p class="feature-description">详细的测试用例编辑功能，包括前置条件、测试步骤和预期结果</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <h3 class="feature-title">执行跟踪</h3>
                        <p class="feature-description">跟踪测试用例的执行状态和结果，支持进度管理</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-sort"></i>
                        </div>
                        <h3 class="feature-title">排序管理</h3>
                        <p class="feature-description">灵活的用例排序功能，支持拖拽调整执行顺序</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <h3 class="feature-title">统计分析</h3>
                        <p class="feature-description">提供详细的执行统计和通过率分析报告</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">协作管理</h3>
                        <p class="feature-description">支持多人协作编辑和执行，权限控制和操作记录</p>
                    </div>
                </div>
            </section>

            <!-- 功能介绍 -->
            <section id="features" class="section">
                <h2 class="section-title">功能介绍</h2>
                <p class="section-subtitle">详细了解测试用例集管理系统的各项功能特性</p>

                <!-- 用例集创建 -->
                <div class="feature-detail">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #06b6d4;">
                        <i class="fas fa-plus-square"></i> 测试用例集创建
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        创建和管理测试用例集合，支持基本信息设置和用例组织。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            测试用例集创建界面截图
                        </div>
                        <p class="screenshot-caption">用例集创建表单，包含名称、描述等基本信息</p>
                    </div>

                    <div class="info">
                        <strong>用例集基本信息：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li><strong>用例集名称：</strong>简洁明确的用例集标识</li>
                            <li><strong>描述信息：</strong>详细说明用例集的用途和范围</li>
                            <li><strong>创建时间：</strong>自动记录创建时间戳</li>
                            <li><strong>创建人员：</strong>记录用例集创建者信息</li>
                            <li><strong>状态管理：</strong>草稿、审核中、已发布等状态</li>
                        </ul>
                    </div>
                </div>

                <!-- 用例编辑 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #06b6d4;">
                        <i class="fas fa-edit"></i> 测试用例编辑
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        详细的测试用例编辑功能，支持结构化的用例信息管理。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            测试用例编辑界面截图
                        </div>
                        <p class="screenshot-caption">用例编辑器支持前置条件、测试步骤和预期结果的详细编辑</p>
                    </div>

                    <div class="info">
                        <strong>用例结构：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li><strong>前置条件：</strong>执行测试前需要满足的条件</li>
                            <li><strong>测试步骤：</strong>详细的操作步骤和执行路径</li>
                            <li><strong>预期结果：</strong>期望的测试结果和验证标准</li>
                            <li><strong>优先级：</strong>高、中、低优先级分类</li>
                            <li><strong>标签分类：</strong>支持自定义标签管理</li>
                        </ul>
                    </div>
                </div>

                <!-- 执行跟踪 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #06b6d4;">
                        <i class="fas fa-tasks"></i> 执行跟踪管理
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        跟踪测试用例的执行状态，支持进度管理和结果记录。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            执行跟踪界面截图
                        </div>
                        <p class="screenshot-caption">显示用例执行进度和状态，支持侧边栏待办事项管理</p>
                    </div>

                    <div class="info">
                        <strong>执行状态：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li><strong>未开始：</strong>尚未开始执行的用例</li>
                            <li><strong>执行中：</strong>正在执行的用例</li>
                            <li><strong>已通过：</strong>执行成功的用例</li>
                            <li><strong>未通过：</strong>执行失败的用例</li>
                            <li><strong>已跳过：</strong>暂时跳过的用例</li>
                            <li><strong>需重测：</strong>需要重新测试的用例</li>
                        </ul>
                    </div>
                </div>

                <!-- 排序管理 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #06b6d4;">
                        <i class="fas fa-sort"></i> 用例排序管理
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        灵活的用例排序功能，支持拖拽调整和批量排序。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            用例排序界面截图
                        </div>
                        <p class="screenshot-caption">支持拖拽排序和批量调整用例执行顺序</p>
                    </div>

                    <div class="info">
                        <strong>排序功能：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>拖拽排序：直接拖拽调整用例顺序</li>
                            <li>批量排序：选择多个用例进行批量调整</li>
                            <li>优先级排序：按优先级自动排序</li>
                            <li>依赖关系：设置用例间的依赖关系</li>
                            <li>自定义规则：支持自定义排序规则</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 快速开始 -->
            <section id="getting-started" class="section">
                <h2 class="section-title">快速开始</h2>
                <p class="section-subtitle">按照以下步骤快速上手测试用例集管理</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">创建用例集</h3>
                        <p class="step-description">点击"新建用例集"，填写基本信息创建测试用例集</p>
                    </div>

                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">添加测试用例</h3>
                        <p class="step-description">在用例集中添加具体的测试用例，设置前置条件和测试步骤</p>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">调整执行顺序</h3>
                        <p class="step-description">使用排序功能调整用例的执行顺序和优先级</p>
                    </div>

                    <div class="step">
                        <div class="step-number">4</div>
                        <h3 class="step-title">执行跟踪</h3>
                        <p class="step-description">开始执行测试，跟踪进度并记录执行结果</p>
                    </div>
                </div>
            </section>

            <!-- 使用教程 -->
            <section id="tutorials" class="section">
                <h2 class="section-title">使用教程</h2>
                <p class="section-subtitle">详细的操作指南和最佳实践</p>

                <!-- 创建用例集教程 -->
                <div class="tutorial">
                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #2c3e50;">
                        📋 如何创建测试用例集
                    </h3>

                    <div class="steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <h4 class="step-title">进入管理页面</h4>
                            <p class="step-description">在主页点击"测试用例集"卡片或直接访问用例集管理页面</p>
                        </div>

                        <div class="step">
                            <div class="step-number">2</div>
                            <h4 class="step-title">点击新建按钮</h4>
                            <p class="step-description">点击页面右上角的"新建用例集"按钮</p>
                        </div>

                        <div class="step">
                            <div class="step-number">3</div>
                            <h4 class="step-title">填写基本信息</h4>
                            <p class="step-description">输入用例集名称和详细描述信息</p>
                        </div>

                        <div class="step">
                            <div class="step-number">4</div>
                            <h4 class="step-title">保存用例集</h4>
                            <p class="step-description">点击确定按钮保存，系统会自动创建用例集</p>
                        </div>
                    </div>

                    <div class="tip">
                        <strong>💡 命名建议：</strong> 建议使用有意义的名称，如"登录功能测试用例集"、"支付流程测试用例集"等，便于识别和管理。
                    </div>
                </div>

                <!-- 用例编辑教程 -->
                <div class="tutorial" style="margin-top: 50px;">
                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #2c3e50;">
                        ✏️ 测试用例编辑详解
                    </h3>

                    <h4 style="color: #06b6d4; margin-bottom: 15px;">前置条件设置</h4>
                    <ul style="padding-left: 20px; color: #7f8c8d; margin-bottom: 20px;">
                        <li>明确测试环境要求（浏览器版本、操作系统等）</li>
                        <li>列出必需的测试数据和账号信息</li>
                        <li>说明系统状态和配置要求</li>
                        <li>描述依赖的其他测试用例或功能</li>
                    </ul>

                    <h4 style="color: #06b6d4; margin-bottom: 15px; margin-top: 30px;">测试步骤编写</h4>
                    <ul style="padding-left: 20px; color: #7f8c8d; margin-bottom: 20px;">
                        <li>使用清晰的动作词汇（点击、输入、选择等）</li>
                        <li>步骤要具体明确，避免模糊表述</li>
                        <li>按照实际操作顺序编写步骤</li>
                        <li>包含必要的验证点和检查项</li>
                    </ul>

                    <h4 style="color: #06b6d4; margin-bottom: 15px; margin-top: 30px;">预期结果定义</h4>
                    <ul style="padding-left: 20px; color: #7f8c8d; margin-bottom: 20px;">
                        <li>明确描述期望的系统响应</li>
                        <li>包含界面变化和数据更新</li>
                        <li>定义成功和失败的判断标准</li>
                        <li>考虑边界情况和异常处理</li>
                    </ul>

                    <div class="warning">
                        <strong>⚠️ 注意：</strong> 测试用例应该具有可重复性和独立性，避免依赖特定的执行环境或其他用例的执行结果。
                    </div>
                </div>

                <!-- 执行跟踪教程 -->
                <div class="tutorial" style="margin-top: 50px;">
                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #2c3e50;">
                        🎯 执行跟踪与进度管理
                    </h3>

                    <p style="color: #7f8c8d; margin-bottom: 20px;">
                        系统提供完整的执行跟踪功能，帮助团队管理测试进度和结果。
                    </p>

                    <div class="steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <h4 class="step-title">开始执行</h4>
                            <p class="step-description">选择用例集，点击"开始执行"进入执行模式</p>
                        </div>

                        <div class="step">
                            <div class="step-number">2</div>
                            <h4 class="step-title">更新状态</h4>
                            <p class="step-description">根据执行结果更新每个用例的状态</p>
                        </div>

                        <div class="step">
                            <div class="step-number">3</div>
                            <h4 class="step-title">记录结果</h4>
                            <p class="step-description">详细记录执行过程中的问题和发现</p>
                        </div>

                        <div class="step">
                            <div class="step-number">4</div>
                            <h4 class="step-title">生成报告</h4>
                            <p class="step-description">完成执行后生成测试报告和统计分析</p>
                        </div>
                    </div>

                    <div class="info">
                        <strong>📈 状态管理：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>实时更新执行进度和通过率</li>
                            <li>支持批量状态更新操作</li>
                            <li>自动记录执行时间和操作人员</li>
                            <li>提供侧边栏待办事项管理</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 常见问题 -->
            <section id="faq" class="section">
                <h2 class="section-title">常见问题</h2>
                <p class="section-subtitle">解答使用过程中的常见问题</p>

                <div style="max-width: 800px; margin: 0 auto;">
                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何批量导入测试用例？</h4>
                        <p class="faq-answer">A: 目前系统支持手动创建用例。批量导入功能正在开发中，将支持Excel和CSV格式的用例导入。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 可以复制现有的用例集吗？</h4>
                        <p class="faq-answer">A: 可以。在用例集列表中点击"复制"按钮，系统会创建一个包含所有用例的新用例集副本。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何设置用例之间的依赖关系？</h4>
                        <p class="faq-answer">A: 在用例编辑页面的"前置条件"中描述依赖关系，或使用排序功能调整执行顺序来体现依赖关系。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 执行结果可以导出吗？</h4>
                        <p class="faq-answer">A: 可以。在执行完成后，点击"导出报告"按钮可以生成包含执行结果和统计信息的Excel报告。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何查看用例集的执行历史？</h4>
                        <p class="faq-answer">A: 在用例集详情页面点击"执行历史"标签，可以查看所有历史执行记录和结果统计。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 支持多人同时编辑用例集吗？</h4>
                        <p class="faq-answer">A: 系统支持多人协作，但同一时间只能有一人编辑特定用例。其他用户可以查看实时更新的内容。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何删除不需要的测试用例？</h4>
                        <p class="faq-answer">A: 在用例列表中选择要删除的用例，点击"删除"按钮。注意：删除操作不可恢复，请谨慎操作。</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 底部 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 测试用例集管理系统. 版权所有.</p>
            <p style="margin-top: 10px; opacity: 0.8;">
                如有问题请联系系统管理员 |
                <a href="mailto:<EMAIL>" style="color: #06b6d4;"><EMAIL></a>
            </p>
        </div>
    </footer>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航栏高亮
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('.nav');
            if (window.scrollY > 100) {
                nav.style.background = 'rgba(255, 255, 255, 0.95)';
                nav.style.backdropFilter = 'blur(10px)';
            } else {
                nav.style.background = 'white';
                nav.style.backdropFilter = 'none';
            }
        });
    </script>
</body>
</html>
