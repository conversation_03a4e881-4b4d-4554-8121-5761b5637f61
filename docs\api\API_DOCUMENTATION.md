# Spug Platform API Documentation

## Table of Contents

1. [Project Overview](#project-overview)
2. [Backend APIs](#backend-apis)
3. [Frontend Components](#frontend-components)
4. [Shared Libraries](#shared-libraries)
5. [Data Models](#data-models)
6. [Authentication & Authorization](#authentication--authorization)
7. [Examples & Usage](#examples--usage)

## Project Overview

Spug is a lightweight, agent-free automation operation and maintenance platform designed for small and medium-sized enterprises. It integrates host management, batch execution, online terminals, application deployment, scheduled tasks, configuration centers, monitoring, and alerting.

### Technology Stack
- **Backend**: Python 3.6+, Django 2.2
- **Frontend**: Node 12.14, React 16.11, Ant Design
- **Database**: SQLite

---

## Backend APIs

### Core API Modules

#### 1. Host Management APIs (`/api/host/`)

**Models**: `Host`, `HostExtend`, `Group`

##### Host Operations

```python
# GET /api/host/ - List all hosts
# POST /api/host/ - Create new host
# PUT /api/host/{id}/ - Update host
# DELETE /api/host/{id}/ - Delete host
```

**Example Usage**:
```python
# Create a new host
import requests

host_data = {
    "name": "Web Server 01",
    "hostname": "*************",
    "port": 22,
    "username": "root",
    "desc": "Production web server"
}

response = requests.post('/api/host/', json=host_data, headers={
    'X-Token': 'your_token_here'
})
```

**Host Model Fields**:
- `name`: Host display name
- `hostname`: IP address or domain
- `port`: SSH port (default: 22)
- `username`: SSH username
- `pkey`: Private key for authentication
- `desc`: Description
- `is_verified`: Verification status

##### Host Groups

```python
# GET /api/host/group/ - List host groups
# POST /api/host/group/ - Create group
# PUT /api/host/group/{id}/ - Update group
# DELETE /api/host/group/{id}/ - Delete group
```

#### 2. Execution APIs (`/api/exec/`)

##### Task Execution

```python
# POST /api/exec/task/ - Execute commands on hosts
# GET /api/exec/task/{id}/ - Get execution results
# POST /api/exec/template/ - Save execution template
# GET /api/exec/template/ - List templates
```

**Example - Execute Command**:
```python
task_data = {
    "host_ids": [1, 2, 3],
    "command": "ls -la /var/log",
    "run_as": "root"
}

response = requests.post('/api/exec/task/', json=task_data)
```

##### File Transfer

```python
# POST /api/exec/transfer/ - Upload/download files
# GET /api/exec/transfer/{id}/ - Get transfer status
```

#### 3. Configuration APIs (`/api/config/`)

##### Environment Management

```python
# GET /api/config/environment/ - List environments
# POST /api/config/environment/ - Create environment
# PUT /api/config/environment/{id}/ - Update environment
```

##### Service Configuration

```python
# GET /api/config/service/ - List services
# POST /api/config/service/ - Create service configuration
```

**Example - Create Environment**:
```python
env_data = {
    "name": "Production",
    "key": "prod",
    "desc": "Production environment"
}

response = requests.post('/api/config/environment/', json=env_data)
```

#### 4. Deployment APIs (`/api/deploy/`)

##### Application Deployment

```python
# GET /api/deploy/app/ - List deploy configurations
# POST /api/deploy/app/ - Create deploy config
# POST /api/deploy/{deploy_id}/release/ - Trigger deployment
```

##### Repository Management

```python
# GET /api/deploy/repository/ - List repositories
# POST /api/deploy/repository/ - Add repository
```

#### 5. Monitoring APIs (`/api/monitor/`)

```python
# GET /api/monitor/ - List monitoring rules
# POST /api/monitor/ - Create monitoring rule
# GET /api/monitor/{id}/data/ - Get monitoring data
```

#### 6. Schedule APIs (`/api/schedule/`)

```python
# GET /api/schedule/ - List scheduled tasks
# POST /api/schedule/ - Create scheduled task
# PUT /api/schedule/{id}/ - Update task
# POST /api/schedule/{id}/trigger/ - Manual trigger
```

**Example - Create Scheduled Task**:
```python
schedule_data = {
    "name": "Daily Backup",
    "command": "backup.sh",
    "targets": [1, 2],  # host IDs
    "trigger": "cron",
    "trigger_args": "0 2 * * *"  # Daily at 2 AM
}

response = requests.post('/api/schedule/', json=schedule_data)
```

#### 7. Alarm APIs (`/api/alarm/`)

##### Alarm Management

```python
# GET /api/alarm/ - List alarms
# POST /api/alarm/ - Create alarm rule
# PUT /api/alarm/{id}/ - Update alarm
```

##### Contact Management

```python
# GET /api/alarm/contact/ - List contacts
# POST /api/alarm/contact/ - Add contact
```

#### 8. System Management APIs (`/api/system/`)

##### Account Management

```python
# GET /api/account/ - List users
# POST /api/account/ - Create user
# PUT /api/account/{id}/ - Update user
```

##### Role Management

```python
# GET /api/role/ - List roles
# POST /api/role/ - Create role
# PUT /api/role/{id}/ - Update permissions
```

---

## Frontend Components

### Core Components (`/src/components/`)

#### 1. TableCard Component

**Location**: `src/components/TableCard.js`

A comprehensive table component with search, filtering, column management, and batch operations.

**Props**:
- `title`: Table title
- `columns`: Column definitions
- `dataSource`: Table data
- `actions`: Header action buttons
- `batchActions`: Batch operation buttons
- `selected`: Selected rows
- `onReload`: Reload callback
- `rowSelection`: Row selection config
- `pagination`: Pagination config

**Example Usage**:
```jsx
import { TableCard } from '@/components';

function HostList() {
  const [hosts, setHosts] = useState([]);
  const [loading, setLoading] = useState(false);
  
  const columns = [
    { title: 'Name', dataIndex: 'name', key: 'name' },
    { title: 'Hostname', dataIndex: 'hostname', key: 'hostname' },
    { title: 'Status', dataIndex: 'is_verified', key: 'status',
      render: (verified) => verified ? 'Verified' : 'Unverified' }
  ];
  
  return (
    <TableCard
      title="Host List"
      columns={columns}
      dataSource={hosts}
      loading={loading}
      rowKey="id"
      onReload={() => fetchHosts()}
    />
  );
}
```

#### 2. Search Component

**Location**: `src/components/SearchForm.js`

**Props**:
- `keys`: Array of search field options
- `onChange`: Search callback

**Example**:
```jsx
<TableCard.Search
  keys={['name/Name', 'hostname/IP Address']}
  onChange={(key, value) => handleSearch(key, value)}
/>
```

#### 3. AuthButton Component

**Location**: `src/components/AuthButton.js`

Button component with permission checking.

**Props**:
- `auth`: Required permission string
- `type`: Button type
- `children`: Button content

**Example**:
```jsx
<AuthButton auth="host.host.add" type="primary">
  Add Host
</AuthButton>
```

#### 4. ACEditor Component

**Location**: `src/components/ACEditor.js`

Code editor component with syntax highlighting.

**Props**:
- `mode`: Language mode (bash, python, etc.)
- `value`: Editor content
- `onChange`: Change callback
- `readOnly`: Read-only mode

**Example**:
```jsx
<ACEditor
  mode="bash"
  value={script}
  onChange={setScript}
  height="300px"
/>
```

#### 5. AppSelector Component

**Location**: `src/components/AppSelector.js`

Application selection dropdown.

**Props**:
- `value`: Selected app ID
- `onChange`: Selection callback
- `placeholder`: Placeholder text

#### 6. DragUpload Component

**Location**: `src/components/DragUpload/`

Drag and drop file upload component.

**Props**:
- `accept`: Accepted file types
- `multiple`: Allow multiple files
- `onUpload`: Upload callback

**Example**:
```jsx
<DragUpload
  accept=".sh,.py"
  onUpload={(files) => handleFileUpload(files)}
>
  Drop files here or click to select
</DragUpload>
```

#### 7. FileTree Component

**Location**: `src/components/FileTree/`

File tree browser component.

**Props**:
- `data`: Tree data structure
- `onSelect`: Selection callback
- `selectable`: Enable selection

### Layout Components (`/src/layout/`)

#### Navigation Component

Provides main navigation menu with permission-based rendering.

#### Header Component

Top header with user info, notifications, and global actions.

### Page Components (`/src/pages/`)

#### Dashboard (`/src/pages/dashboard/`)

System overview and statistics.

#### Host Management (`/src/pages/host/`)

- Host list and management
- Host groups organization
- SSH terminal interface
- GPU dashboard for monitoring

#### Execution (`/src/pages/exec/`)

- Task execution interface
- Template management
- File transfer operations
- Command cards for test plans

#### Configuration (`/src/pages/config/`)

- Environment management
- Service configuration
- Application settings

---

## Shared Libraries

### Frontend Libraries (`/src/libs/`)

#### 1. HTTP Client (`http.js`)

Axios-based HTTP client with authentication and error handling.

**Key Features**:
- Automatic token attachment
- Response/request interceptors
- Error handling with user notifications

**Usage**:
```javascript
import http from '@/libs/http';

// GET request
const hosts = await http.get('/api/host/');

// POST request
const result = await http.post('/api/host/', hostData);

// Custom headers
const data = await http.get('/api/data/', {
  headers: { 'Custom-Header': 'value' }
});
```

#### 2. Function Utilities (`functools.js`)

**Key Functions**:

##### `hasPermission(strCode)`
Check if user has specific permissions.

```javascript
import { hasPermission } from '@/libs/functools';

// Single permission
if (hasPermission('host.host.add')) {
  // Show add button
}

// Multiple permissions (OR)
if (hasPermission('host.host.add|host.host.edit')) {
  // User has either add or edit permission
}

// Multiple permissions (AND)  
if (hasPermission('host.host.add&deploy.app.view')) {
  // User has both permissions
}
```

##### `includes(str, keywords)`
Enhanced string/array search.

```javascript
import { includes } from '@/libs/functools';

includes('Hello World', 'hello'); // true (case insensitive)
includes(['item1', 'item2'], ['item', 'test']); // true
```

##### `cleanCommand(text)`
Clean command input by removing carriage returns.

```javascript
const cleanCmd = cleanCommand(userInput);
```

##### `human_datetime(date)`, `human_date(date)`, `human_time(date)`
Format dates for display.

```javascript
const now = new Date();
console.log(human_datetime(now)); // "2023-12-01 14:30:00"
console.log(human_date(now));     // "2023-12-01"
console.log(human_time(now));     // "14:30:00"
```

##### `uniqueId()`
Generate unique identifiers.

```javascript
const id = uniqueId(); // "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx"
```

#### 3. Router Helper (`router.js`)

Navigation utilities and route management.

### Backend Libraries (`/spug_api/libs/`)

#### 1. SSH Library (`ssh.py`)

SSH connection management and command execution.

**Key Classes**:

##### `SSH(hostname, port, username, pkey)`

```python
from libs.ssh import SSH

# Create connection
ssh = SSH('*************', 22, 'root', private_key)

# Execute command
result = ssh.exec_command('ls -la')
print(result.stdout)

# Execute with environment
ssh.exec_command_with_stream('deploy.sh', env={'ENV': 'prod'})

# File upload/download
ssh.put_file(local_path, remote_path)
ssh.get_file(remote_path, local_path)
```

#### 2. Parser Library (`parser.py`)

Request data parsing and validation.

**Key Classes**:

##### `JsonParser`

```python
from libs.parser import JsonParser

def my_view(request):
    form, error = JsonParser(
        Argument('name', help='Host name is required'),
        Argument('hostname', help='IP address is required'),
        Argument('port', type=int, default=22)
    ).parse(request.body)
    
    if error:
        return JsonResponse(error)
    
    # Use validated data
    host = Host.objects.create(**form)
```

#### 3. Utilities (`utils.py`)

Common utility functions.

**Key Functions**:

##### `human_datetime()`
Get current timestamp in human-readable format.

```python
from libs.utils import human_datetime

timestamp = human_datetime()  # "2023-12-01 14:30:00"
```

##### `AttrDict`
Dictionary with attribute access.

```python
from libs.utils import AttrDict

config = AttrDict({'host': '127.0.0.1', 'port': 22})
print(config.host)  # "127.0.0.1"
```

#### 4. Decorators (`decorators.py`)

Common decorators for views.

##### `@auth_required`
Require authentication for view.

```python
from libs.decorators import auth_required

@auth_required
def protected_view(request):
    return JsonResponse({'data': 'Protected data'})
```

##### `@has_permission`
Check specific permissions.

```python
from libs.decorators import has_permission

@has_permission('host.host.view')
def host_list(request):
    hosts = Host.objects.all()
    return JsonResponse([h.to_dict() for h in hosts])
```

---

## Data Models

### Host Models

#### Host Model
```python
class Host(models.Model):
    name = models.CharField(max_length=100)           # Display name
    hostname = models.CharField(max_length=50)        # IP/Domain
    port = models.IntegerField(null=True)            # SSH port
    username = models.CharField(max_length=50)        # SSH username
    pkey = models.TextField(null=True)               # Private key
    desc = models.CharField(max_length=255, null=True) # Description
    is_verified = models.BooleanField(default=False)  # Verification status
    created_at = models.CharField(max_length=20)      # Creation time
    created_by = models.ForeignKey(User)              # Creator
```

**Methods**:
- `get_ssh()`: Get SSH connection instance
- `to_view()`: Convert to frontend-friendly format

#### HostExtend Model
Extended host information including cloud provider details.

```python
class HostExtend(models.Model):
    host = models.OneToOneField(Host)
    instance_id = models.CharField(max_length=64)     # Cloud instance ID
    cpu = models.IntegerField()                       # CPU cores
    memory = models.FloatField()                      # Memory in GB
    disk = models.CharField(max_length=255)           # Disk info (JSON)
    os_name = models.CharField(max_length=50)         # OS name
    private_ip_address = models.CharField(max_length=255) # Private IPs
    public_ip_address = models.CharField(max_length=255)  # Public IPs
```

#### Group Model
Host grouping for organization.

```python
class Group(models.Model):
    name = models.CharField(max_length=50)
    parent_id = models.IntegerField(default=0)
    sort_id = models.IntegerField(default=0)
    hosts = models.ManyToManyField(Host)
```

---

## Authentication & Authorization

### Frontend Authentication

#### Token Management
```javascript
import { X_TOKEN, updatePermissions } from '@/libs/functools';

// Check if user is logged in
if (X_TOKEN) {
  // User is authenticated
}

// Update permissions after login
updatePermissions();
```

#### Permission Checking
```javascript
import { hasPermission } from '@/libs/functools';

// Component level
if (hasPermission('host.host.add')) {
  return <AddButton />;
}

// Using AuthButton
<AuthButton auth="host.host.add">Add Host</AuthButton>

// Using AuthDiv
<AuthDiv auth="host.host.view">
  <HostList />
</AuthDiv>
```

### Backend Authentication

#### Decorators
```python
from libs.decorators import auth_required, has_permission

@auth_required
@has_permission('host.host.view')
def host_list(request):
    return JsonResponse(Host.objects.all().values())
```

#### Permission Format
Permissions follow the format: `{app}.{model}.{action}`

**Examples**:
- `host.host.view` - View hosts
- `host.host.add` - Add hosts
- `exec.task.do` - Execute tasks
- `deploy.app.view` - View deployment configs

---

## Examples & Usage

### Complete Host Management Example

#### Backend API
```python
# views.py
from libs.decorators import auth_required, has_permission
from libs.parser import JsonParser, Argument

@auth_required
@has_permission('host.host.view')
def get_hosts(request):
    hosts = Host.objects.all()
    return JsonResponse([h.to_view() for h in hosts])

@auth_required
@has_permission('host.host.add')
def create_host(request):
    form, error = JsonParser(
        Argument('name', help='Name is required'),
        Argument('hostname', help='Hostname is required'),
        Argument('port', type=int, default=22),
        Argument('username', default='root'),
        Argument('desc', required=False)
    ).parse(request.body)
    
    if error:
        return JsonResponse(error, status=400)
    
    # Test SSH connection
    try:
        ssh = SSH(form.hostname, form.port, form.username, form.pkey)
        ssh.ping()
        form['is_verified'] = True
    except Exception as e:
        form['is_verified'] = False
    
    form['created_by'] = request.user
    host = Host.objects.create(**form)
    
    return JsonResponse(host.to_view())
```

#### Frontend Component
```jsx
// HostManager.jsx
import React, { useState, useEffect } from 'react';
import { TableCard, AuthButton } from '@/components';
import { Modal, Form, Input, InputNumber, message } from 'antd';
import http from '@/libs/http';

function HostManager() {
  const [hosts, setHosts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();

  const columns = [
    { title: 'Name', dataIndex: 'name', key: 'name' },
    { title: 'Hostname', dataIndex: 'hostname', key: 'hostname' },
    { title: 'Port', dataIndex: 'port', key: 'port' },
    { title: 'Username', dataIndex: 'username', key: 'username' },
    { title: 'Status', dataIndex: 'is_verified', key: 'status',
      render: (verified) => verified ? 'Verified' : 'Unverified' },
    { title: 'Description', dataIndex: 'desc', key: 'desc' }
  ];

  const fetchHosts = async () => {
    setLoading(true);
    try {
      const data = await http.get('/api/host/');
      setHosts(data);
    } catch (error) {
      message.error('Failed to fetch hosts');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = async (values) => {
    try {
      const host = await http.post('/api/host/', values);
      setHosts([...hosts, host]);
      setVisible(false);
      form.resetFields();
      message.success('Host added successfully');
    } catch (error) {
      message.error('Failed to add host');
    }
  };

  useEffect(() => {
    fetchHosts();
  }, []);

  return (
    <div>
      <TableCard
        title="Host Management"
        columns={columns}
        dataSource={hosts}
        loading={loading}
        rowKey="id"
        onReload={fetchHosts}
        actions={[
          <AuthButton 
            key="add"
            auth="host.host.add" 
            type="primary"
            onClick={() => setVisible(true)}
          >
            Add Host
          </AuthButton>
        ]}
      />
      
      <Modal
        title="Add Host"
        visible={visible}
        onCancel={() => setVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleAdd} layout="vertical">
          <Form.Item name="name" label="Name" rules={[{required: true}]}>
            <Input />
          </Form.Item>
          <Form.Item name="hostname" label="Hostname" rules={[{required: true}]}>
            <Input />
          </Form.Item>
          <Form.Item name="port" label="Port" initialValue={22}>
            <InputNumber min={1} max={65535} />
          </Form.Item>
          <Form.Item name="username" label="Username" initialValue="root">
            <Input />
          </Form.Item>
          <Form.Item name="desc" label="Description">
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}

export default HostManager;
```

### Batch Command Execution Example

```jsx
// ExecutionPanel.jsx
import React, { useState } from 'react';
import { Form, Input, Select, Button, Card, message } from 'antd';
import { ACEditor, AuthButton } from '@/components';
import http from '@/libs/http';

function ExecutionPanel() {
  const [hosts, setHosts] = useState([]);
  const [command, setCommand] = useState('');
  const [results, setResults] = useState([]);
  const [executing, setExecuting] = useState(false);

  const executeCommand = async () => {
    if (!hosts.length || !command.trim()) {
      message.warning('Please select hosts and enter command');
      return;
    }

    setExecuting(true);
    try {
      const result = await http.post('/api/exec/task/', {
        host_ids: hosts,
        command: command,
        run_as: 'root'
      });
      
      setResults(result.outputs || []);
      message.success('Command executed successfully');
    } catch (error) {
      message.error('Execution failed');
    } finally {
      setExecuting(false);
    }
  };

  return (
    <div>
      <Card title="Batch Command Execution">
        <Form layout="vertical">
          <Form.Item label="Select Hosts">
            <Select
              mode="multiple"
              placeholder="Choose hosts"
              value={hosts}
              onChange={setHosts}
            >
              {/* Host options would be loaded here */}
            </Select>
          </Form.Item>
          
          <Form.Item label="Command">
            <ACEditor
              mode="bash"
              value={command}
              onChange={setCommand}
              height="200px"
              placeholder="Enter your command here..."
            />
          </Form.Item>
          
          <Form.Item>
            <AuthButton
              auth="exec.task.do"
              type="primary"
              loading={executing}
              onClick={executeCommand}
            >
              Execute Command
            </AuthButton>
          </Form.Item>
        </Form>
      </Card>

      {results.length > 0 && (
        <Card title="Execution Results" style={{marginTop: 16}}>
          {results.map((result, index) => (
            <Card.Grid key={index} style={{width: '100%'}}>
              <h4>{result.host}</h4>
              <pre>{result.stdout}</pre>
              {result.stderr && (
                <pre style={{color: 'red'}}>{result.stderr}</pre>
              )}
            </Card.Grid>
          ))}
        </Card>
      )}
    </div>
  );
}
```

### Configuration Management Example

```python
# Configuration API
@auth_required
@has_permission('config.config.view')
def get_configs(request):
    app = request.GET.get('app')
    env = request.GET.get('env')
    
    if not app or not env:
        return JsonResponse({'error': 'App and env required'}, status=400)
    
    configs = compose_configs(app, env)
    format_type = request.GET.get('format', 'json')
    
    if format_type == 'json':
        return JsonResponse(configs)
    elif format_type == 'env':
        output = '\n'.join([f'{k}={v}' for k, v in configs.items()])
        return HttpResponse(output, content_type='text/plain')
    
    return JsonResponse({'error': 'Invalid format'}, status=400)
```

This documentation provides comprehensive coverage of the Spug platform's APIs, components, and utilities with practical examples for implementation. Each section includes usage patterns, parameter descriptions, and working code examples to facilitate development and integration.