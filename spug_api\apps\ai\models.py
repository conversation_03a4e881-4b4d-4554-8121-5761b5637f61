"""
AI智能分析相关模型
"""
from django.db import models
from libs import ModelMixin


class AIConfig(models.Model, ModelMixin):
    """AI配置模型"""
    
    # 基础配置
    ai_enabled = models.BooleanField(default=False, verbose_name='启用AI分析')
    api_base = models.CharField(max_length=255, default='https://api-inference.modelscope.cn/v1', verbose_name='API基础地址')
    api_key = models.CharField(max_length=255, blank=True, verbose_name='API密钥')
    model_name = models.CharField(max_length=100, default='qwen/Qwen2.5-72B-Instruct', verbose_name='模型名称')
    
    # 模型参数
    max_tokens = models.IntegerField(default=4000, verbose_name='最大Token数')
    temperature = models.FloatField(default=0.7, verbose_name='温度参数')
    
    # 缓存配置
    cache_enabled = models.BooleanField(default=True, verbose_name='启用缓存')
    cache_ttl = models.IntegerField(default=3600, verbose_name='缓存过期时间(秒)')
    
    # 系统字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'ai_config'
        verbose_name = 'AI配置'
        verbose_name_plural = verbose_name
        
    def __str__(self):
        return f'AI配置 - {self.model_name}'


class AIAnalysisLog(models.Model, ModelMixin):
    """AI分析日志模型"""
    
    # 分析信息
    filename = models.CharField(max_length=255, verbose_name='文件名')
    content_hash = models.CharField(max_length=64, verbose_name='内容哈希')
    analysis_type = models.CharField(max_length=50, default='log_analysis', verbose_name='分析类型')
    
    # 分析结果
    analysis_result = models.TextField(verbose_name='分析结果')
    success = models.BooleanField(default=True, verbose_name='分析成功')
    error_message = models.TextField(blank=True, verbose_name='错误信息')
    
    # 性能指标
    processing_time = models.FloatField(default=0, verbose_name='处理时间(秒)')
    token_usage = models.IntegerField(default=0, verbose_name='Token使用量')
    
    # 系统字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'ai_analysis_log'
        verbose_name = 'AI分析日志'
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['filename', 'content_hash']),
            models.Index(fields=['created_at']),
        ]
        
    def __str__(self):
        return f'AI分析 - {self.filename}'
