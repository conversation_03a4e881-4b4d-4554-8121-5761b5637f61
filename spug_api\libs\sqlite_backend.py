#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义SQLite数据库后端，用于设置优化的PRAGMA选项
"""

from django.db.backends.sqlite3.base import DatabaseWrapper as BaseDatabaseWrapper
from django.db.backends.sqlite3.base import Database
import logging

logger = logging.getLogger(__name__)

class DatabaseWrapper(BaseDatabaseWrapper):
    """自定义SQLite数据库包装器"""
    
    def get_new_connection(self, conn_params):
        """创建新的数据库连接并设置优化参数"""
        # 调用父类方法创建连接
        conn = super().get_new_connection(conn_params)
        
        # 设置SQLite优化参数
        try:
            cursor = conn.cursor()
            
            # 设置WAL模式（Write-Ahead Logging）提高并发性能
            cursor.execute("PRAGMA journal_mode=WAL;")
            
            # 设置同步模式为NORMAL，平衡性能和安全性
            cursor.execute("PRAGMA synchronous=NORMAL;")
            
            # 增加缓存大小（页数）
            cursor.execute("PRAGMA cache_size=1000;")
            
            # 临时表存储在内存中
            cursor.execute("PRAGMA temp_store=MEMORY;")
            
            # 设置忙等待超时（毫秒）
            cursor.execute("PRAGMA busy_timeout=30000;")
            
            # 启用外键约束
            cursor.execute("PRAGMA foreign_keys=ON;")
            
            # 设置页面大小（如果是新数据库）
            cursor.execute("PRAGMA page_size=4096;")
            
            cursor.close()
            
            logger.debug("SQLite数据库优化参数设置完成")
            
        except Exception as e:
            logger.warning(f"设置SQLite优化参数失败: {e}")
        
        return conn
    
    def init_connection_state(self):
        """初始化连接状态"""
        super().init_connection_state()
        
        # 确保连接参数正确设置
        try:
            with self.connection.cursor() as cursor:
                # 验证WAL模式是否启用
                cursor.execute("PRAGMA journal_mode;")
                journal_mode = cursor.fetchone()[0]
                
                if journal_mode.upper() != 'WAL':
                    logger.warning(f"WAL模式未启用，当前模式: {journal_mode}")
                else:
                    logger.debug("WAL模式已启用")
                    
        except Exception as e:
            logger.warning(f"验证数据库设置失败: {e}")

class DatabaseFeatures(BaseDatabaseWrapper.features_class):
    """自定义数据库特性"""
    
    # 启用一些优化特性
    supports_over_clause = True
    supports_frame_range_clause = True
    supports_aggregate_filter_clause = True
    supports_timezones = False
    supports_transactions = True
    
    # 并发相关设置
    test_db_allows_multiple_connections = True
