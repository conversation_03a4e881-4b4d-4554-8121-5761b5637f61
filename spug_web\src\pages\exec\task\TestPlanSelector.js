/**
 * 测试计划选择器
 */
import React from 'react';
import { observer } from 'mobx-react';
import { SyncOutlined } from '@ant-design/icons';
import { Modal, Table, Input, Button, Select, Tag } from 'antd';
import { SearchForm } from 'components';
import { http } from 'libs';

@observer
class TestPlanSelector extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      selectedRows: [],
      testPlans: [],
      loading: false,
      f_name: '',
      f_category: ''
    }
  }

  componentDidMount() {
    this.fetchTestPlans();
  }

  fetchTestPlans = () => {
    this.setState({ loading: true });
    // 获取测试计划时请求替换变量后的数据
    http.get('/api/exec/test-plans/?replace_variables=true')
      .then(res => {
        // 由于HTTP库的处理，res直接就是测试计划数组
        const testPlans = Array.isArray(res) ? res : (res.data || []);
        this.setState({
          testPlans: testPlans,
          loading: false
        });
      })
      .catch(error => {
        this.setState({ loading: false });
      });
  };

  handleClick = (record) => {
    this.setState({selectedRows: [record]});
  };

  handleSubmit = () => {
    if (this.state.selectedRows.length > 0) {
      const plan = this.state.selectedRows[0];
      
      // 将测试计划转换为执行任务格式
      const commands = plan.commands || [];
      let combinedCommand = '';
      
      // 合并所有步骤的命令
      commands.forEach((cmd, index) => {
        if (cmd.enabled !== false) {
          combinedCommand += `# 步骤${index + 1}: ${cmd.label || cmd.name || `步骤${index + 1}`}\n`;
          
          // 如果有文件需要传输，添加文件传输说明
          if (cmd.files && cmd.files.length > 0) {
            combinedCommand += `# 关联文件: ${cmd.files.map(f => f.name).join(', ')}\n`;
          }
          
          combinedCommand += `${cmd.command || cmd.body}\n\n`;
        }
      });
      
      // 构造类似模板的对象
      const templateLike = {
        id: plan.id,
        name: plan.name,
        type: 'test-plan',
        body: combinedCommand.trim(),
        interpreter: 'sh', // 默认使用shell
        host_ids: [], // 测试计划不预设主机
        parameters: [],
        desc: plan.description,
        // 添加测试计划特有字段
        isTestPlan: true,
        originalPlan: plan
      };
      
      this.props.onOk(templateLike);
    }
    this.props.onCancel();
  };

  get filteredData() {
    const { testPlans, f_name, f_category } = this.state;
    return testPlans.filter(item => {
      if (f_name && !item.name.toLowerCase().includes(f_name.toLowerCase())) {
        return false;
      }
      if (f_category && item.category !== f_category) {
        return false;
      }
      return true;
    });
  }

  get categories() {
    const { testPlans } = this.state;
    const categories = [...new Set(testPlans.map(item => item.category).filter(Boolean))];
    return categories;
  }

  columns = [
    {
      title: '名称',
      dataIndex: 'name',
      ellipsis: true
    }, 
    {
      title: '分类',
      dataIndex: 'category',
      render: v => v ? <Tag color="blue">{v}</Tag> : '-'
    },
    {
      title: '步骤数',
      dataIndex: 'commands',
      render: v => `${(v || []).length}个步骤`
    }, 
    {
      title: '关联文件',
      dataIndex: 'commands',
      render: commands => {
        const totalFiles = (commands || []).reduce((total, cmd) => {
          return total + ((cmd.files || []).length);
        }, 0);
        return totalFiles > 0 ? `${totalFiles}个文件` : '无';
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true
    }
  ];

  render() {
    const { selectedRows, loading, f_name, f_category } = this.state;
    
    return (
      <Modal
        visible
        width={1000}
        title="选择测试计划"
        onCancel={this.props.onCancel}
        onOk={this.handleSubmit}
        maskClosable={false}>
        <SearchForm>
          <SearchForm.Item span={8} title="计划分类">
            <Select 
              allowClear 
              placeholder="请选择" 
              value={f_category} 
              onChange={v => this.setState({ f_category: v })}>
              {this.categories.map(item => (
                <Select.Option value={item} key={item}>{item}</Select.Option>
              ))}
            </Select>
          </SearchForm.Item>
          <SearchForm.Item span={8} title="计划名称">
            <Input 
              allowClear 
              value={f_name} 
              onChange={e => this.setState({ f_name: e.target.value })} 
              placeholder="请输入"/>
          </SearchForm.Item>
          <SearchForm.Item span={8}>
            <Button type="primary" icon={<SyncOutlined/>} onClick={this.fetchTestPlans}>刷新</Button>
          </SearchForm.Item>
        </SearchForm>
        <Table
          rowKey="id"
          rowSelection={{
            selectedRowKeys: selectedRows.map(item => item.id),
            type: 'radio',
            onChange: (_, selectedRows) => this.setState({selectedRows})
          }}
          dataSource={this.filteredData}
          loading={loading}
          onRow={record => {
            return {
              onClick: () => this.handleClick(record)
            }
          }}
          columns={this.columns}/>
      </Modal>
    )
  }
}

export default TestPlanSelector