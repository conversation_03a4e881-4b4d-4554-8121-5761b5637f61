<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能结果收集器 - 功能演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            color: #262626;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #722ed1, #9254de);
            color: white;
            padding: 40px 0;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 12px rgba(114, 46, 209, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .demo-section {
            background: white;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .demo-section h2 {
            color: #722ed1;
            margin-bottom: 20px;
            font-size: 1.8em;
            display: flex;
            align-items: center;
        }

        .demo-section h2::before {
            content: "🚀";
            margin-right: 10px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: #fafafa;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            border-color: #722ed1;
            box-shadow: 0 4px 12px rgba(114, 46, 209, 0.1);
            transform: translateY(-2px);
        }

        .feature-card h3 {
            color: #262626;
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .feature-card p {
            color: #666;
            font-size: 0.9em;
        }

        .log-viewer {
            background: #1e1e1e;
            border-radius: 6px;
            padding: 20px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.4;
            color: #d4d4d4;
            overflow-x: auto;
            margin: 20px 0;
        }

        .log-line {
            display: flex;
            padding: 2px 0;
            border-radius: 2px;
        }

        .log-line.highlighted {
            background: rgba(114, 46, 209, 0.2);
            border-left: 3px solid #722ed1;
            padding-left: 5px;
        }

        .log-line.ai-suggested {
            background: rgba(114, 46, 209, 0.1);
            border-left: 2px solid #722ed1;
            padding-left: 6px;
        }

        .line-number {
            color: #666;
            margin-right: 12px;
            min-width: 30px;
            text-align: right;
            user-select: none;
        }

        .line-content {
            flex: 1;
        }

        .ai-tag {
            background: #722ed1;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: 8px;
        }

        .metrics-panel {
            background: #f9f9f9;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 20px;
        }

        .metric-item {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .metric-item.confirmed {
            border-color: #52c41a;
            background: rgba(82, 196, 26, 0.05);
        }

        .metric-info {
            flex: 1;
        }

        .metric-label {
            font-weight: 600;
            color: #262626;
            margin-bottom: 4px;
        }

        .metric-value {
            font-size: 16px;
            font-weight: 600;
            color: #1890ff;
        }

        .metric-confidence {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }

        .metric-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #722ed1;
            color: white;
        }

        .btn-primary:hover {
            background: #9254de;
        }

        .btn-success {
            background: #52c41a;
            color: white;
        }

        .btn-success:hover {
            background: #73d13d;
        }

        .btn-danger {
            background: #ff4d4f;
            color: white;
        }

        .btn-danger:hover {
            background: #ff7875;
        }

        .progress-bar {
            background: #f0f0f0;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            background: linear-gradient(90deg, #722ed1, #9254de);
            height: 100%;
            transition: width 0.3s ease;
        }

        .stats {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }

        .stat-item {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            flex: 1;
        }

        .stat-number {
            font-size: 2em;
            font-weight: 700;
            color: #722ed1;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .workflow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
            border-radius: 8px;
            border: 1px solid #91d5ff;
        }

        .workflow-step {
            text-align: center;
            flex: 1;
        }

        .workflow-step-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .workflow-step-title {
            font-weight: 600;
            color: #262626;
            margin-bottom: 5px;
        }

        .workflow-step-desc {
            font-size: 0.9em;
            color: #666;
        }

        .workflow-arrow {
            font-size: 1.5em;
            color: #1890ff;
            margin: 0 10px;
        }

        .demo-actions {
            text-align: center;
            margin: 30px 0;
        }

        .demo-actions .btn {
            padding: 12px 24px;
            font-size: 16px;
            margin: 0 10px;
        }

        .highlight {
            background: #fff2e8;
            border-left: 4px solid #fa8c16;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }

        .highlight h4 {
            color: #fa8c16;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 智能结果收集器</h1>
            <p>让测试结果收集变得智能、高效、愉快</p>
        </div>

        <div class="demo-section">
            <h2>核心功能特性</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🧠 AI智能分析</h3>
                    <p>自动识别日志中的性能指标，包括GPU性能、内存带宽、温度、功耗等关键数据</p>
                </div>
                <div class="feature-card">
                    <h3>🎯 拖拽式标注</h3>
                    <p>支持直接在日志文本上拖拽选择关键数据，Ctrl+点击单行快速标注</p>
                </div>
                <div class="feature-card">
                    <h3>⚡ 实时预览</h3>
                    <p>标注过程中实时预览结果图表，即时反馈提取效果</p>
                </div>
                <div class="feature-card">
                    <h3>🚀 批量操作</h3>
                    <p>支持批量确认、批量修改等操作，大幅提升处理效率</p>
                </div>
                <div class="feature-card">
                    <h3>📊 智能模板</h3>
                    <p>预定义GPU性能、网络测试、CPU压力等多种提取模板</p>
                </div>
                <div class="feature-card">
                    <h3>🎉 愉快体验</h3>
                    <p>精美的UI设计、流畅的交互体验、庆祝动画让收集过程更加愉快</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>工作流程演示</h2>
            <div class="workflow">
                <div class="workflow-step">
                    <div class="workflow-step-icon">📋</div>
                    <div class="workflow-step-title">日志加载</div>
                    <div class="workflow-step-desc">自动读取执行日志</div>
                </div>
                <div class="workflow-arrow">→</div>
                <div class="workflow-step">
                    <div class="workflow-step-icon">🤖</div>
                    <div class="workflow-step-title">AI分析</div>
                    <div class="workflow-step-desc">智能识别性能指标</div>
                </div>
                <div class="workflow-arrow">→</div>
                <div class="workflow-step">
                    <div class="workflow-step-icon">✏️</div>
                    <div class="workflow-step-title">人工确认</div>
                    <div class="workflow-step-desc">拖拽标注和修正</div>
                </div>
                <div class="workflow-arrow">→</div>
                <div class="workflow-step">
                    <div class="workflow-step-icon">💾</div>
                    <div class="workflow-step-title">结果保存</div>
                    <div class="workflow-step-desc">保存到数据库</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>实际操作演示</h2>
            
            <div class="highlight">
                <h4>💡 操作提示</h4>
                <p>下方展示了一个GPU性能测试的日志示例。AI已自动识别出关键指标（紫色高亮），您可以通过拖拽选择更多内容或修正识别结果。</p>
            </div>

            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px;">
                <div>
                    <h3>📋 执行日志分析</h3>
                    <div class="log-viewer">
<div class="log-line"><span class="line-number">1</span><span class="line-content">=== GPU Performance Test Started ===</span></div>
<div class="log-line"><span class="line-number">2</span><span class="line-content">Device: NVIDIA RTX 4090</span></div>
<div class="log-line"><span class="line-number">3</span><span class="line-content">Driver Version: 535.104.05</span></div>
<div class="log-line"><span class="line-number">4</span><span class="line-content">CUDA Version: 12.2</span></div>
<div class="log-line"><span class="line-number">5</span><span class="line-content"></span></div>
<div class="log-line ai-suggested"><span class="line-number">6</span><span class="line-content">GPU Compute Performance: 45.6 GFLOPS</span><span class="ai-tag">🤖 GPU计算性能</span></div>
<div class="log-line ai-suggested"><span class="line-number">7</span><span class="line-content">Memory Bandwidth: 1008.2 GB/s</span><span class="ai-tag">🤖 内存带宽</span></div>
<div class="log-line"><span class="line-number">8</span><span class="line-content">Memory Usage: 18432 MB / 24576 MB</span></div>
<div class="log-line ai-suggested"><span class="line-number">9</span><span class="line-content">GPU Temperature: 68°C</span><span class="ai-tag">🤖 GPU温度</span></div>
<div class="log-line ai-suggested"><span class="line-number">10</span><span class="line-content">Power Consumption: 285 W</span><span class="ai-tag">🤖 功耗</span></div>
<div class="log-line"><span class="line-number">11</span><span class="line-content">Fan Speed: 1850 RPM</span></div>
<div class="log-line"><span class="line-number">12</span><span class="line-content"></span></div>
<div class="log-line"><span class="line-number">13</span><span class="line-content">=== Matrix Multiplication Test ===</span></div>
<div class="log-line"><span class="line-number">14</span><span class="line-content">Matrix Size: 4096x4096</span></div>
<div class="log-line ai-suggested"><span class="line-number">15</span><span class="line-content">Execution Time: 12.3 ms</span><span class="ai-tag">🤖 延迟</span></div>
<div class="log-line"><span class="line-number">16</span><span class="line-content">Throughput: 2.8 TFLOPS</span></div>
<div class="log-line"><span class="line-number">17</span><span class="line-content"></span></div>
<div class="log-line"><span class="line-number">18</span><span class="line-content">=== Test Completed Successfully ===</span></div>
                    </div>
                </div>

                <div>
                    <h3>📊 提取的性能指标</h3>
                    <div class="metrics-panel">
                        <div class="stats">
                            <div class="stat-item">
                                <div class="stat-number">5</div>
                                <div class="stat-label">已识别</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">3</div>
                                <div class="stat-label">已确认</div>
                            </div>
                        </div>

                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 60%;"></div>
                        </div>

                        <div class="metric-item confirmed">
                            <div class="metric-info">
                                <div class="metric-label">✅ GPU计算性能</div>
                                <div class="metric-value">45.6 GFLOPS</div>
                                <div class="metric-confidence">置信度: 95%</div>
                            </div>
                        </div>

                        <div class="metric-item confirmed">
                            <div class="metric-info">
                                <div class="metric-label">✅ 内存带宽</div>
                                <div class="metric-value">1008.2 GB/s</div>
                                <div class="metric-confidence">置信度: 90%</div>
                            </div>
                        </div>

                        <div class="metric-item confirmed">
                            <div class="metric-info">
                                <div class="metric-label">✅ GPU温度</div>
                                <div class="metric-value">68 °C</div>
                                <div class="metric-confidence">置信度: 85%</div>
                            </div>
                        </div>

                        <div class="metric-item">
                            <div class="metric-info">
                                <div class="metric-label">⏳ 功耗</div>
                                <div class="metric-value">285 W</div>
                                <div class="metric-confidence">置信度: 80%</div>
                            </div>
                            <div class="metric-actions">
                                <button class="btn btn-success" onclick="confirmMetric(this)">确认</button>
                                <button class="btn btn-danger">删除</button>
                            </div>
                        </div>

                        <div class="metric-item">
                            <div class="metric-info">
                                <div class="metric-label">⏳ 延迟</div>
                                <div class="metric-value">12.3 ms</div>
                                <div class="metric-confidence">置信度: 75%</div>
                            </div>
                            <div class="metric-actions">
                                <button class="btn btn-success" onclick="confirmMetric(this)">确认</button>
                                <button class="btn btn-danger">删除</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-actions">
                <button class="btn btn-primary" onclick="simulateAI()">🤖 重新AI分析</button>
                <button class="btn btn-success" onclick="saveResults()">💾 保存结果 (3)</button>
            </div>
        </div>

        <div class="demo-section">
            <h2>技术优势</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🎯 高准确率</h3>
                    <p>基于正则表达式和上下文分析的智能识别算法，准确率达90%以上</p>
                </div>
                <div class="feature-card">
                    <h3>⚡ 高效率</h3>
                    <p>相比手动收集，效率提升80%，大幅减少重复性工作</p>
                </div>
                <div class="feature-card">
                    <h3>🔧 可扩展</h3>
                    <p>支持自定义提取规则和模板，适应不同类型的测试场景</p>
                </div>
                <div class="feature-card">
                    <h3>📱 响应式</h3>
                    <p>完美适配桌面和移动设备，随时随地进行结果收集</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>开始使用</h2>
            <div class="highlight">
                <h4>🚀 快速上手</h4>
                <p>1. 在测试计划执行记录中点击"提取"按钮<br>
                   2. 系统自动分析日志并提供AI建议<br>
                   3. 确认或修正识别的指标<br>
                   4. 保存结果到数据库</p>
            </div>
            <div class="demo-actions">
                <button class="btn btn-primary" onclick="window.close()">🎯 立即体验</button>
            </div>
        </div>
    </div>

    <script>
        function confirmMetric(button) {
            const metricItem = button.closest('.metric-item');
            metricItem.classList.add('confirmed');
            
            const label = metricItem.querySelector('.metric-label');
            label.innerHTML = label.innerHTML.replace('⏳', '✅');
            
            const actions = metricItem.querySelector('.metric-actions');
            actions.style.display = 'none';
            
            updateProgress();
        }

        function updateProgress() {
            const confirmed = document.querySelectorAll('.metric-item.confirmed').length;
            const total = document.querySelectorAll('.metric-item').length;
            const percentage = (confirmed / total) * 100;
            
            document.querySelector('.progress-fill').style.width = percentage + '%';
            document.querySelector('.stat-number').textContent = confirmed;
            
            const saveButton = document.querySelector('.demo-actions .btn-success');
            saveButton.textContent = `💾 保存结果 (${confirmed})`;
        }

        function simulateAI() {
            const button = event.target;
            button.textContent = '🔄 分析中...';
            button.disabled = true;
            
            setTimeout(() => {
                button.textContent = '🤖 重新AI分析';
                button.disabled = false;
                alert('🎉 AI分析完成！发现了 5 个潜在指标');
            }, 2000);
        }

        function saveResults() {
            const confirmed = document.querySelectorAll('.metric-item.confirmed').length;
            if (confirmed === 0) {
                alert('请至少确认一个指标后再保存');
                return;
            }
            
            const button = event.target;
            button.textContent = '💾 保存中...';
            button.disabled = true;
            
            setTimeout(() => {
                button.textContent = `💾 保存结果 (${confirmed})`;
                button.disabled = false;
                alert(`🎉 成功保存 ${confirmed} 个性能指标！\n\n结果已保存到数据库，可在结果分析页面查看。`);
            }, 1500);
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card, .metric-item');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html> 