/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import { observable, computed, action, configure } from 'mobx';
import http from 'libs/http';

configure({enforceActions: 'never'});

class Store {
  @observable records = [];
  @observable record = {};
  @observable idMap = {};
  @observable isFetching = false;
  @observable formVisible = false;

  @observable f_name;

  constructor() {
  }

  @computed get dataSource() {
    let records = this.records;
    if (this.f_name) records = records.filter(x => x.name.toLowerCase().includes(this.f_name.toLowerCase()));
    return records
  }

  @action
  fetchRecords = () => {
    this.isFetching = true;
    return http.get('/api/config/package/')
      .then(res => {
        this.records = res;
        for (let item of res) {
          this.idMap[item.id] = item
        }
      })
      .finally(() => this.isFetching = false)
  };

  @action
  showForm = (info = {}) => {
    this.formVisible = true;
    this.record = info
  }
}

export default new Store()
