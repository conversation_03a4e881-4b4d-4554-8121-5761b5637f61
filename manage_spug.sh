#!/bin/bash

# Spug 服务管理脚本
# 使用官方supervisor方式管理服务

case "${1:-status}" in
    "start")
        echo "启动Spug服务..."
        supervisorctl start spug-api spug-ws spug-worker spug-monitor spug-scheduler
        systemctl start nginx
        ;;
    "stop")
        echo "停止Spug服务..."
        supervisorctl stop spug-api spug-ws spug-worker spug-monitor spug-scheduler
        ;;
    "restart")
        echo "重启Spug服务..."
        supervisorctl restart spug-api spug-ws spug-worker spug-monitor spug-scheduler
        nginx -t && systemctl reload nginx
        ;;
    "status")
        echo "=== Spug服务状态 ==="
        supervisorctl status
        echo ""
        echo "=== Nginx状态 ==="
        systemctl status nginx --no-pager -l
        echo ""
        echo "=== 端口监听状态 ==="
        netstat -tlnp | grep -E "(9001|9002|80)"
        ;;
    "logs")
        echo "=== 查看日志 ==="
        echo "API日志:"
        tail -10 /opt/spug/logs/api.log
        echo ""
        echo "WebSocket日志:"
        tail -10 /opt/spug/logs/ws.log
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动所有Spug服务"
        echo "  stop    - 停止所有Spug服务"
        echo "  restart - 重启所有Spug服务"
        echo "  status  - 查看服务状态"
        echo "  logs    - 查看服务日志"
        exit 1
        ;;
esac
