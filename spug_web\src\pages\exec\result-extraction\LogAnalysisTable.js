import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Button, Upload, message, Space, Spin, Progress, Tag, Tooltip, Table } from 'antd';
import { UploadOutlined, BarChartOutlined, DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import { useHistory } from 'react-router-dom';
import * as XLSX from 'xlsx';

// 智能提取模板 - 基于您提供的固定格式，支持多个空格
const LOG_EXTRACTION_TEMPLATE = {
  patterns: [
    { key: 'successful_requests', pattern: /Successful requests:\s+([\d,]+)/, type: 'number' },
    { key: 'benchmark_duration', pattern: /Benchmark duration \(s\):\s+([\d.]+)/, type: 'number' },
    { key: 'total_input_tokens', pattern: /Total input tokens:\s+([\d,]+)/, type: 'number' },
    { key: 'total_generated_tokens', pattern: /Total generated tokens:\s+([\d,]+)/, type: 'number' },
    { key: 'request_throughput', pattern: /Request throughput \(req\/s\):\s+([\d.]+)/, type: 'number' },
    { key: 'output_token_throughput', pattern: /Output token throughput \(tok\/s\):\s+([\d.]+)/, type: 'number' },
    { key: 'total_token_throughput', pattern: /Total Token throughput \(tok\/s\):\s+([\d.]+)/, type: 'number' },
    { key: 'mean_ttft', pattern: /Mean TTFT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'median_ttft', pattern: /Median TTFT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'p99_ttft', pattern: /P99 TTFT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'mean_tpot', pattern: /Mean TPOT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'median_tpot', pattern: /Median TPOT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'p99_tpot', pattern: /P99 TPOT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'mean_itl', pattern: /Mean ITL \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'median_itl', pattern: /Median ITL \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'p99_itl', pattern: /P99 ITL \(ms\):\s+([\d.]+)/, type: 'number' }
  ]
};

// VTable 列配置
const TABLE_COLUMNS = [
  {
    field: 'filename',
    title: '文件名',
    width: 200,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f2f5' },
    style: { textAlign: 'left' }
  },
  {
    field: 'successful_requests',
    title: '成功请求数',
    width: 120,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#e6f7ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  },
  {
    field: 'benchmark_duration',
    title: '基准时长(s)',
    width: 120,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f6ffed' },
    style: { textAlign: 'center' }
  },
  {
    field: 'total_input_tokens',
    title: '输入Token总数',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2e8' },
    style: { textAlign: 'center' }
  },
  {
    field: 'total_generated_tokens',
    title: '生成Token总数',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2e8' },
    style: { textAlign: 'center' }
  },
  {
    field: 'request_throughput',
    title: '请求吞吐量(req/s)',
    width: 150,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' }
  },
  {
    field: 'output_token_throughput',
    title: '输出Token吞吐量(tok/s)',
    width: 180,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' }
  },
  {
    field: 'total_token_throughput',
    title: '总Token吞吐量(tok/s)',
    width: 170,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' }
  },
  {
    field: 'mean_ttft',
    title: '平均TTFT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff1f0' },
    style: { textAlign: 'center', color: '#cf1322' }
  },
  {
    field: 'median_ttft',
    title: '中位TTFT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff1f0' },
    style: { textAlign: 'center', color: '#cf1322' }
  },
  {
    field: 'p99_ttft',
    title: 'P99 TTFT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff1f0' },
    style: { textAlign: 'center', color: '#cf1322' }
  },
  {
    field: 'mean_tpot',
    title: '平均TPOT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#feffe6' },
    style: { textAlign: 'center', color: '#d48806' }
  },
  {
    field: 'median_tpot',
    title: '中位TPOT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#feffe6' },
    style: { textAlign: 'center', color: '#d48806' }
  },
  {
    field: 'p99_tpot',
    title: 'P99 TPOT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#feffe6' },
    style: { textAlign: 'center', color: '#d48806' }
  },
  {
    field: 'mean_itl',
    title: '平均ITL(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  },
  {
    field: 'median_itl',
    title: '中位ITL(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  },
  {
    field: 'p99_itl',
    title: 'P99 ITL(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  }
];

const LogAnalysisTable = ({ selectedFiles, hostId, containerName }) => {
  const [loading, setLoading] = useState(false);
  const [extractedData, setExtractedData] = useState([]);
  const [progress, setProgress] = useState(0);
  const [tableInstance, setTableInstance] = useState(null);
  const containerRef = useRef(null);
  const history = useHistory();

  // 处理后端解析的数据（不再需要前端解析）
  const processBackendData = useCallback((parsedData, filename) => {


    // 如果后端已经解析了数据，直接使用
    if (parsedData && typeof parsedData === 'object') {

      return parsedData;
    }

    // 如果后端没有解析数据，返回默认结构

    const defaultResult = { filename };
    LOG_EXTRACTION_TEMPLATE.patterns.forEach(({ key, type }) => {
      defaultResult[key] = type === 'number' ? 0 : '-';
    });


    return defaultResult;
  }, []);

  // 跳转到新的VTable页面进行分析
  const handleAnalyzeInNewPage = () => {
    if (!selectedFiles || selectedFiles.length === 0) {
      message.warning('请先选择要分析的日志文件');
      return;
    }

    // 将分析参数存储到sessionStorage，供新页面使用
    const analysisParams = {
      selectedFiles,
      hostId,
      containerName,
      timestamp: Date.now()
    };

    sessionStorage.setItem('logAnalysisParams', JSON.stringify(analysisParams));

    // 跳转到新的VTable分析页面
    history.push('/exec/result-extraction/vtable-analysis');
  };

  // 跳转到新的Ant Design Table页面进行分析
  const handleAnalyzeInAntdTable = () => {
    if (!selectedFiles || selectedFiles.length === 0) {
      message.warning('请先选择要分析的日志文件');
      return;
    }

    // 构建URL参数
    const params = new URLSearchParams({
      hostId: hostId,
      containerName: containerName,
      selectedFiles: encodeURIComponent(JSON.stringify(selectedFiles))
    });

    // 在新标签页中打开Ant Design Table分析页面
    window.open(`/exec/result-extraction/antd-table-analysis?${params.toString()}`, '_blank');
  };

  // 获取文件内容并提取数据
  const processFiles = useCallback(async () => {
    if (!selectedFiles || selectedFiles.length === 0) {
      message.warning('请先选择要分析的日志文件');
      return;
    }

    setLoading(true);
    setProgress(0);
    const results = [];

    try {
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        setProgress(Math.round(((i + 1) / selectedFiles.length) * 100));

        try {


          // 调用API获取文件内容和解析数据
          const apiUrl = `/api/exec/docker-file-content/?host_id=${hostId}&container_name=${containerName}&file_path=${encodeURIComponent(file.fullPath)}&token=1`;


          const response = await fetch(apiUrl);


          if (response.ok) {
            const responseData = await response.json();


            // 检查spug的标准响应格式: {data: {...}, error: ''}
            if (responseData.error) {

              // 添加错误数据行
              results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '错误'])) });
            } else if (responseData.data) {
              const data = responseData.data;


              if (data.success) {
                // 使用后端解析的数据
                const processedData = processBackendData(data.parsed_data, file.name);


                results.push(processedData);
              } else {

                // 添加空数据行以保持文件记录
                results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '-'])) });
              }
            } else {

              // 添加空数据行以保持文件记录
              results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '格式错误'])) });
            }
          } else {

            results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '网络错误'])) });
          }
        } catch (error) {

          // 添加错误数据行
          results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '错误'])) });
        }
      }

      setExtractedData(results);
      message.success(`成功分析了 ${results.length} 个日志文件`);
    } catch (error) {

      message.error('处理文件时出错');
    } finally {
      setLoading(false);
      setProgress(0);
    }
  }, [selectedFiles, hostId, containerName, processBackendData]);

  // 转换为Ant Design Table的列配置
  const antdColumns = TABLE_COLUMNS.map(col => ({
    title: col.title,
    dataIndex: col.field,
    key: col.field,
    width: col.width,
    sorter: col.sort ? (a, b) => {
      const aVal = a[col.field] || 0;
      const bVal = b[col.field] || 0;
      return typeof aVal === 'number' ? aVal - bVal : String(aVal).localeCompare(String(bVal));
    } : false,
    align: col.style?.textAlign || 'left',
    render: (text, record) => {
      if (typeof text === 'number' && text % 1 !== 0) {
        return text.toFixed(2);
      }
      return text;
    }
  }));

  // 导出数据
  const exportData = useCallback(() => {
    if (extractedData.length === 0) {
      message.warning('没有数据可导出');
      return;
    }

    const csvContent = [
      // CSV 头部
      TABLE_COLUMNS.map(col => col.title).join(','),
      // CSV 数据行
      ...extractedData.map(row => 
        TABLE_COLUMNS.map(col => row[col.field] || '').join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `log_analysis_${new Date().getTime()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    message.success('数据导出成功');
  }, [extractedData]);

  return (
    <Card 
      title={
        <Space>
          <BarChartOutlined />
          <span>日志性能分析表格</span>
          <Tag color="blue">{extractedData.length} 条记录</Tag>
        </Space>
      }
      extra={
        <Space>
          <Button
            type="primary"
            icon={<BarChartOutlined />}
            onClick={handleAnalyzeInAntdTable}
            disabled={!selectedFiles || selectedFiles.length === 0}
          >
            Ant Table分析 (推荐)
          </Button>
          <Button
            icon={<BarChartOutlined />}
            onClick={handleAnalyzeInNewPage}
            disabled={!selectedFiles || selectedFiles.length === 0}
          >
            VTable分析 (旧版)
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={exportData}
            disabled={extractedData.length === 0}
          >
            导出数据
          </Button>
        </Space>
      }
      style={{ marginTop: 16 }}
    >
      {loading && (
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Progress percent={progress} status="active" />
            <p>正在分析日志文件... ({progress}%)</p>
          </div>
        </div>
      )}
      
      {!loading && extractedData.length === 0 && (
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
          <BarChartOutlined style={{ fontSize: 48, marginBottom: 16 }} />
          <p>请选择日志文件并点击"开始分析"按钮</p>
        </div>
      )}

      {!loading && extractedData.length > 0 && (
        <Table
          columns={antdColumns}
          dataSource={extractedData}
          rowKey={(record, index) => index}
          scroll={{ x: 1200, y: 500 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100']
          }}
          size="small"
        />
      )}
    </Card>
  );
};

export default LogAnalysisTable;
