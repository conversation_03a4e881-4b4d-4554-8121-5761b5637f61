/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React, { useState, useEffect, useCallback } from 'react';
import { Card, Button, Space, Row, Col, Tag, Spin, message } from 'antd';
import { ReloadOutlined, FullscreenOutlined } from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import { http } from 'libs';
import HostBasicInfo from './components/HostBasicInfo';
import GpuBasicInfo from './components/GpuBasicInfo';
import GpuDriverInfo from './components/GpuDriverInfo';
import GpuPerformanceInfo from './components/GpuPerformanceInfo';
import MonitorDiagnosticInfo from './components/MonitorDiagnosticInfo';
import styles from './index.module.less';

function GpuDashboard() {
  const { id: hostId } = useParams();
  const [gpuData, setGpuData] = useState({});
  const [loading, setLoading] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);
  const [hostName, setHostName] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const refreshInterval = 30; // 30秒

  const loadGpuData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await http.get(`/api/host/gpu-dashboard/${hostId}/`);
      // 确保响应数据是对象格式
      if (response && typeof response === 'object') {
        console.log('[DEBUG] 接收到的GPU数据:', response);
        console.log('[DEBUG] GPU基础信息:', response.gpu_basic);
        if (response.gpu_basic && response.gpu_basic.gpus) {
          console.log('[DEBUG] GPU数量:', response.gpu_basic.gpus.length);
          console.log('[DEBUG] 第一个GPU信息:', response.gpu_basic.gpus[0]);
        }
        setGpuData(response);
        setLastUpdateTime(new Date());
      } else {
        console.warn('GPU数据格式不正确:', response);
        setGpuData({});
      }
    } catch (error) {
      console.error('Load GPU data error:', error);
      
      // 根据错误类型显示不同的消息
      if (error.response && error.response.status === 401) {
        message.error('认证失败，请重新登录');
      } else if (error.response && error.response.data && error.response.data.error) {
        message.error(`加载GPU数据失败: ${error.response.data.error}`);
      } else {
        message.error('加载GPU数据失败');
      }
      
      // 设置空数据防止组件报错
      setGpuData({});
    } finally {
      setLoading(false);
    }
  }, [hostId]);

  const loadHostInfo = useCallback(async () => {
    try {
      const response = await http.get(`/api/host/${hostId}/`);
      if (response && response.name) {
        setHostName(response.name);
      }
    } catch (error) {
      console.error('Load host info error:', error);
    }
  }, [hostId]);

  useEffect(() => {
    if (hostId) {
      loadHostInfo();
      loadGpuData();
    }
  }, [hostId, loadHostInfo, loadGpuData]);

  useEffect(() => {
    let timer;
    if (autoRefresh && hostId) {
      timer = setInterval(() => {
        loadGpuData();
      }, refreshInterval * 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [autoRefresh, refreshInterval, hostId, loadGpuData]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      setLoading(false);
    };
  }, []);

  const handleRefresh = () => {
    loadGpuData();
  };



  const toggleFullscreen = () => {
    const element = document.documentElement;
    
    if (!isFullscreen) {
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }
    
    setIsFullscreen(!isFullscreen);
  };



  return (
    <div className={styles.gpuDashboard}>
      <Card
        title={
          <Space>
            <span>
              {hostName} GPU监控大屏
              {hostId && (
                <Tag color="blue" style={{ marginLeft: 8 }}>
                  {hostId}
                </Tag>
              )}
            </span>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              刷新数据
            </Button>
            <Button
              type="primary"
              icon={<FullscreenOutlined />}
              onClick={toggleFullscreen}
            >
              全屏显示
            </Button>
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        <div style={{ marginBottom: 16, textAlign: 'right', fontSize: '12px', color: '#888' }}>
          最近更新: {lastUpdateTime ? lastUpdateTime.toLocaleString() : '未更新'}
        </div>

        <Spin spinning={loading}>
          <Row gutter={[16, 16]}>
            {/* 主机基本信息 */}
            <Col span={24}>
              <HostBasicInfo data={gpuData.host_basic || {}} loading={loading} />
            </Col>

            {/* GPU基础信息 */}
            <Col span={24}>
              <GpuBasicInfo data={gpuData.gpu_basic || {}} loading={loading} />
            </Col>

            {/* GPU驱动信息 */}
            <Col span={24}>
              <GpuDriverInfo data={gpuData.gpu_driver || {}} loading={loading} />
            </Col>

            {/* GPU性能状态 */}
            <Col span={24}>
              <GpuPerformanceInfo data={gpuData.gpu_performance || {}} loading={loading} />
            </Col>

            {/* 监控诊断信息 */}
            <Col span={24}>
              <MonitorDiagnosticInfo data={gpuData.monitor_diagnostic || {}} loading={loading} />
            </Col>
          </Row>
        </Spin>
      </Card>
      
      {/* 调试信息 */}
      <div style={{ marginTop: 20, padding: 16, background: '#f0f2f5', borderRadius: 4 }}>
        <h3>调试信息</h3>
        <p>主机ID: {hostId}</p>
        <p>数据加载状态: {loading ? '加载中' : '已加载'}</p>
        <p>数据更新时间: {lastUpdateTime ? lastUpdateTime.toLocaleString() : '未更新'}</p>
        <p>GPU数量: {gpuData?.gpu_basic?.gpus?.length || 0}</p>
        <p>驱动版本: {gpuData?.gpu_driver?.driver_version || '未知'}</p>
        <Button onClick={() => console.log('当前GPU数据:', gpuData)}>
          在控制台打印数据
        </Button>
      </div>
    </div>
  );
}

export default GpuDashboard; 