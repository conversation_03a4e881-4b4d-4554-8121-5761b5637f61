import React, { useEffect } from 'react';
import { Card, Row, Col, Progress, Typography } from 'antd';
import { observer } from 'mobx-react';
import { CloudServerOutlined, DesktopOutlined, DatabaseOutlined, HddOutlined } from '@ant-design/icons';
import store from './store';
import styles from './ServerMetrics.module.less';

export default observer(function ServerMetrics({ compact = false }) {
  useEffect(() => {
    store.fetchMetrics();
    const timer = setInterval(() => {
      store.fetchMetrics();
    }, 5000);
    return () => {
      clearInterval(timer);
    };
  }, []);



  const formatPercent = (value) => {
    if (!value || isNaN(value)) return 0;
    return Math.round(value);
  };

  const getStatusClass = (value) => {
    if (value > 80) return 'danger';
    if (value > 60) return 'warning';
    return 'normal';
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (compact) {
    return (
      <div className={styles.serverMetricsContainer}>
        <Card
          size="small"
          className={styles.compactCard}
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <CloudServerOutlined style={{ fontSize: '20px' }} />
                <span>服务器监控</span>
              </div>
              <div className={styles.hostInfo}>
                {store.metrics.targetHost && (
                  <span className={styles.hostText}>
                    {store.metrics.targetHost}
                  </span>
                )}
              </div>
            </div>
          }
        >
          <Row gutter={16}>
            <Col span={8}>
              <div className={styles.compactMetric}>
                <div className={`${styles.compactProgress} ${styles.statusIndicator} ${styles[getStatusClass(store.metrics.cpu)]}`}>
                  <Progress
                    type="circle"
                    percent={formatPercent(store.metrics.cpu)}
                    format={percent => `${percent}%`}
                    width={60}
                    status={store.metrics.cpu > 80 ? 'exception' : 'normal'}
                    strokeColor={store.metrics.cpu > 80 ? '#ff4d4f' : store.metrics.cpu > 60 ? '#fa8c16' : '#1890ff'}
                  />
                </div>
                <div className={styles.compactTitle}>
                  <DesktopOutlined style={{ marginRight: '4px' }} />
                  CPU
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles.compactMetric}>
                <div className={`${styles.compactProgress} ${styles.statusIndicator} ${styles[getStatusClass(store.metrics.memory)]}`}>
                  <Progress
                    type="circle"
                    percent={formatPercent(store.metrics.memory)}
                    format={percent => `${percent}%`}
                    width={60}
                    status={store.metrics.memory > 80 ? 'exception' : 'normal'}
                    strokeColor={store.metrics.memory > 80 ? '#ff4d4f' : store.metrics.memory > 60 ? '#fa8c16' : '#52c41a'}
                  />
                </div>
                <div className={styles.compactTitle}>
                  <DatabaseOutlined style={{ marginRight: '4px' }} />
                  内存
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles.compactMetric}>
                <div className={`${styles.compactProgress} ${styles.statusIndicator} ${styles[getStatusClass(store.metrics.disk)]}`}>
                  <Progress
                    type="circle"
                    percent={formatPercent(store.metrics.disk)}
                    format={percent => `${percent}%`}
                    width={60}
                    status={store.metrics.disk > 80 ? 'exception' : 'normal'}
                    strokeColor={store.metrics.disk > 80 ? '#ff4d4f' : store.metrics.disk > 60 ? '#fa8c16' : '#fa8c16'}
                  />
                </div>
                <div className={styles.compactTitle}>
                  <HddOutlined style={{ marginRight: '10px' }} />
                  HDD磁盘
                </div>
              </div>
            </Col>
          </Row>
        </Card>
      </div>
    );
  }

  return (
    <div className={styles.serverMetricsContainer}>
      <Card
        className={styles.fullCard}
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <CloudServerOutlined style={{ fontSize: '18px' }} />
              <span>服务器指标</span>
            </div>
            <div className={styles.fullHostInfo}>
              {store.metrics.targetHost && (
                <span className={styles.hostText}>目标主机: {store.metrics.targetHost}</span>
              )}
              {store.metrics.timestamp && (
                <span className={styles.updateTime}>
                  更新时间: {formatTimestamp(store.metrics.timestamp)}
                </span>
              )}
            </div>
          </div>
        }
      >
        <Row gutter={24}>
          <Col span={8}>
            <Card className={styles.cpuCard}>
              <div style={{ textAlign: 'center' }}>
                <Progress
                  type="dashboard"
                  percent={formatPercent(store.metrics.cpu)}
                  format={percent => `${percent}%`}
                  status={store.metrics.cpu > 80 ? 'exception' : 'normal'}
                  strokeColor={store.metrics.cpu > 80 ? '#ff4d4f' : store.metrics.cpu > 60 ? '#fa8c16' : '#1890ff'}
                  gapDegree={30}
                />
                <div className={styles.metricTitle}>
                  <DesktopOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                  CPU 使用率
                </div>
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card className={styles.memoryCard}>
              <div style={{ textAlign: 'center' }}>
                <Progress
                  type="dashboard"
                  percent={formatPercent(store.metrics.memory)}
                  format={percent => `${percent}%`}
                  status={store.metrics.memory > 80 ? 'exception' : 'normal'}
                  strokeColor={store.metrics.memory > 80 ? '#ff4d4f' : store.metrics.memory > 60 ? '#fa8c16' : '#52c41a'}
                  gapDegree={30}
                />
                <div className={styles.metricTitle}>
                  <DatabaseOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
                  内存使用率
                </div>
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card className={styles.diskCard}>
              <div style={{ textAlign: 'center' }}>
                <Progress
                  type="dashboard"
                  percent={formatPercent(store.metrics.disk)}
                  format={percent => `${percent}%`}
                  status={store.metrics.disk > 80 ? 'exception' : 'normal'}
                  strokeColor={store.metrics.disk > 80 ? '#ff4d4f' : store.metrics.disk > 60 ? '#fa8c16' : '#fa8c16'}
                  gapDegree={30}
                />
                <div className={styles.metricTitle}>
                  <HddOutlined style={{ marginRight: '8px', color: '#fa8c16' }} />
                  磁盘使用率
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
});