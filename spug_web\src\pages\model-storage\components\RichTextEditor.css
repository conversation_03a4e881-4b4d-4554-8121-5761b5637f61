.rich-text-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  transition: all 0.3s;
}

.rich-text-editor.focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.rich-text-editor.disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 6px 6px 0 0;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-group:not(:last-child)::after {
  content: '';
  width: 1px;
  height: 20px;
  background: #e8e8e8;
  margin: 0 8px;
}

.editor-content {
  padding: 12px;
  min-height: 200px;
  outline: none;
  line-height: 1.6;
  font-size: 14px;
  overflow-y: auto;
  position: relative;
}

.editor-content:empty::before {
  content: attr(data-placeholder);
  color: #bfbfbf;
  position: absolute;
  pointer-events: none;
}

.editor-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 10px 0;
  display: block;
}

.editor-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0;
}

.editor-content table td,
.editor-content table th {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.editor-content table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.editor-content ul,
.editor-content ol {
  padding-left: 20px;
  margin: 10px 0;
}

.editor-content li {
  margin: 4px 0;
}

.editor-content a {
  color: #1890ff;
  text-decoration: none;
}

.editor-content a:hover {
  text-decoration: underline;
}

.editor-footer {
  padding: 8px 12px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 0 0 6px 6px;
}

.tip {
  font-size: 12px;
  color: #999;
}

/* 拖拽悬停效果 */
.editor-content.drag-over {
  border: 2px dashed #1890ff;
  background: rgba(24, 144, 255, 0.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    padding: 6px 8px;
  }
  
  .toolbar-group {
    gap: 2px;
  }
  
  .editor-content {
    padding: 8px;
    font-size: 13px;
  }
}
