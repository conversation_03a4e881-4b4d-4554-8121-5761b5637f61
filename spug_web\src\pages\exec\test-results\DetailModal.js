import React, { useState, useEffect, useRef } from 'react';
import { Modal, Descriptions, Table, Tag, Progress, Card, Row, Col, Statistic, Empty, Spin, Input, Select, Space, Tabs, Divider, Button } from 'antd';
import { Bar<PERSON>hartOutlined, LineChartOutlined, TrophyOutlined, ClockCircleOutlined, SearchOutlined, FilterOutlined, TableOutlined, DownloadOutlined } from '@ant-design/icons';
// import * as VTable from '@visactor/vtable';
import store from './store';
import styles from './DetailModal.module.less';

export default function DetailModal({ visible, onCancel, record }) {
  const [loading, setLoading] = useState(false);
  const [detailData, setDetailData] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedConfidence, setSelectedConfidence] = useState('all');

  useEffect(() => {
    if (visible && record) {
      loadDetailData();
    }
  }, [visible, record]);

  const loadDetailData = async () => {
    setLoading(true);
    try {
      const res = await store.fetchResultDetail(record.id);
      setDetailData(res);
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  const getMetricTypeColor = (type) => {
    const colorMap = {
      'performance': 'blue',
      'memory': 'green', 
      'temperature': 'orange',
      'power': 'red',
      'latency': 'purple',
      'throughput': 'cyan',
      'bandwidth': 'magenta',
      'default': 'default'
    };
    return colorMap[type] || colorMap.default;
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.6) return 'warning';
    return 'error';
  };

  // 筛选指标数据
  const getFilteredMetrics = () => {
    if (!detailData?.metrics) return [];

    return detailData.metrics.filter(metric => {
      // 搜索文本筛选
      const matchesSearch = !searchText ||
        metric.name?.toLowerCase().includes(searchText.toLowerCase()) ||
        metric.label?.toLowerCase().includes(searchText.toLowerCase()) ||
        metric.value?.toString().toLowerCase().includes(searchText.toLowerCase());

      // 分类筛选
      const matchesCategory = selectedCategory === 'all' ||
        metric.type === selectedCategory ||
        metric.category === selectedCategory;

      // 置信度筛选
      const matchesConfidence = selectedConfidence === 'all' ||
        (selectedConfidence === 'high' && metric.confidence >= 0.8) ||
        (selectedConfidence === 'medium' && metric.confidence >= 0.6 && metric.confidence < 0.8) ||
        (selectedConfidence === 'low' && metric.confidence < 0.6);

      return matchesSearch && matchesCategory && matchesConfidence;
    });
  };

  // 获取所有可用的分类
  const getAvailableCategories = () => {
    if (!detailData?.metrics) return [];
    const categories = [...new Set(detailData.metrics.map(m => m.type || m.category).filter(Boolean))];
    return categories;
  };

  // 指标表格列配置
  const metricsColumns = [
    {
      title: '指标名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text) => <strong>{text}</strong>
    },
    {
      title: '数值',
      dataIndex: 'value',
      key: 'value',
      width: 120,
      render: (value, record) => (
        <span className={styles.metricValue}>
          {value} {record.unit && <small>{record.unit}</small>}
        </span>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => (
        <Tag color={getMetricTypeColor(type)}>
          {type || '其他'}
        </Tag>
      )
    },
    {
      title: 'AI置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      width: 120,
      render: (confidence) => {
        const value = Math.round((confidence || 0) * 100);
        return (
          <Progress
            percent={value}
            size="small"
            status={getConfidenceColor(confidence)}
            format={(percent) => `${percent}%`}
          />
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'confirmed',
      key: 'confirmed',
      width: 80,
      render: (confirmed) => (
        <Tag color={confirmed ? 'success' : 'default'}>
          {confirmed ? '已确认' : '待确认'}
        </Tag>
      )
    },
    // {
    //   title: '来源位置',
    //   dataIndex: 'source',
    //   key: 'source',
    //   ellipsis: true,
    //   render: (source) => (
    //     <span className={styles.sourceText}>
    //       {source ? `行 ${source.lineNumber}: ${source.text}` : '未知'}
    //     </span>
    //   )
    // }
  ];

  if (!record) return null;

  return (
    <Modal
      title={`测试结果详情 - ${record.plan_name}`}
      visible={visible}
      onCancel={onCancel}
      width={1200}
      footer={null}
      bodyStyle={{ padding: '16px' }}
    >
      <Spin spinning={loading}>
        {detailData ? (
          <div className={styles.detailContent}>
            {/* 基本信息 */}
            <Card className={styles.infoCard}>
              <Descriptions title="基本信息" bordered column={2}>
                <Descriptions.Item label="测试计划">{detailData.plan_name}</Descriptions.Item>
                <Descriptions.Item label="关联GPU">
                  {detailData.gpu_name ? (
                    <span style={{ color: '#1890ff' }}>
                      {detailData.gpu_name}
                    </span>
                  ) : (
                    <span style={{ color: '#999' }}>未关联</span>
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="执行记录ID">{detailData.execution_id}</Descriptions.Item>
                <Descriptions.Item label="提取时间">{detailData.created_at}</Descriptions.Item>
                <Descriptions.Item label="提取人">{detailData.created_by}</Descriptions.Item>
                <Descriptions.Item label="总指标数">{detailData.total_metrics}</Descriptions.Item>
                <Descriptions.Item label="已确认指标">{detailData.confirmed_metrics}</Descriptions.Item>
                <Descriptions.Item label="成功率">{detailData.success_rate}%</Descriptions.Item>
              </Descriptions>
            </Card>

            {/* 统计卡片 */}
            <Row gutter={16} className={styles.statsRow}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总指标数"
                    value={detailData.total_metrics}
                    prefix={<BarChartOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="确认指标"
                    value={detailData.confirmed_metrics}
                    prefix={<TrophyOutlined />}
                    valueStyle={{ color: '#3f8600' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="成功率"
                    value={detailData.success_rate}
                    suffix="%"
                    prefix={<LineChartOutlined />}
                    valueStyle={{ 
                      color: detailData.success_rate >= 80 ? '#3f8600' : '#cf1322'
                    }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="AI置信度"
                    value={Math.round(detailData.ai_confidence * 100)}
                    suffix="%"
                    prefix={<ClockCircleOutlined />}
                    valueStyle={{ 
                      color: detailData.ai_confidence >= 0.8 ? '#3f8600' : '#cf1322'
                    }}
                  />
                </Card>
              </Col>
            </Row>

            {/* 指标详情表格 */}
            <Card
              title={
                <Space>
                  <span>指标详情</span>
                  <Tag color="blue">{getFilteredMetrics().length} / {detailData.metrics?.length || 0}</Tag>
                </Space>
              }
              className={styles.metricsCard}
              extra={
                <Space>
                  <Input
                    placeholder="搜索指标名称或值"
                    prefix={<SearchOutlined />}
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    style={{ width: 200 }}
                    allowClear
                  />
                  <Select
                    placeholder="选择分类"
                    value={selectedCategory}
                    onChange={setSelectedCategory}
                    style={{ width: 120 }}
                    suffixIcon={<FilterOutlined />}
                  >
                    <Select.Option value="all">全部分类</Select.Option>
                    {getAvailableCategories().map(category => (
                      <Select.Option key={category} value={category}>
                        {category}
                      </Select.Option>
                    ))}
                  </Select>
                  <Select
                    placeholder="置信度"
                    value={selectedConfidence}
                    onChange={setSelectedConfidence}
                    style={{ width: 100 }}
                  >
                    <Select.Option value="all">全部</Select.Option>
                    <Select.Option value="high">高 (≥80%)</Select.Option>
                    <Select.Option value="medium">中 (60-80%)</Select.Option>
                    <Select.Option value="low">低 (&lt;60%)</Select.Option>
                  </Select>
                </Space>
              }
            >
              {detailData.metrics && detailData.metrics.length > 0 ? (
                <Table
                  columns={metricsColumns}
                  dataSource={getFilteredMetrics()}
                  rowKey={(record, index) => index}
                  pagination={{
                    pageSize: 15,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 个指标`,
                    pageSizeOptions: ['10', '15', '20', '50']
                  }}
                  scroll={{ x: 800 }}
                  size="small"
                />
              ) : (
                <Empty description="暂无指标数据" />
              )}
            </Card>

            {/* 原始日志 */}
            {detailData.raw_log && (
              <Card title="原始日志" className={styles.logCard}>
                <div className={styles.logContent}>
                  <pre>{detailData.raw_log}</pre>
                </div>
              </Card>
            )}
          </div>
        ) : (
          <Empty description="加载中..." />
        )}
      </Spin>
    </Modal>
  );
}