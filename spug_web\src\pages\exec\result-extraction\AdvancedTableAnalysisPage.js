import React, { useState, useEffect, useMemo } from 'react';
import { 
  Card, 
  Table, 
  Input, 
  Button, 
  Space, 
  Tag, 
  Progress, 
  Tooltip,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  message,
  Spin
} from 'antd';
import { 
  SearchOutlined, 
  DownloadOutlined, 
  ReloadOutlined,
  FilterOutlined,
  Bar<PERSON><PERSON>Outlined,
  LineChartOutlined
} from '@ant-design/icons';
import { 
  useReactTable, 
  getCoreRowModel, 
  getSortedRowModel, 
  getFilteredRowModel, 
  flexRender, 
  createColumnHelper 
} from '@tanstack/react-table';
import * as XLSX from 'xlsx';
import http from 'libs/http';
import styles from './AdvancedTableAnalysisPage.module.less';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

function AdvancedTableAnalysisPage() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [globalFilter, setGlobalFilter] = useState('');
  const [sorting, setSorting] = useState([]);
  const [filters, setFilters] = useState({
    status: '',
    planName: '',
    modelName: '',
    gpuName: '',
    dateRange: null
  });

  // 获取数据
  const fetchData = async () => {
    setLoading(true);
    try {
      // 检查URL参数
      const urlParams = new URLSearchParams(window.location.search);
      const resultId = urlParams.get('result_id');

      let response;
      if (resultId) {
        // 如果有特定的结果ID，只获取该结果
        response = await http.get(`/api/exec/test-results/${resultId}/`, {
          params: { token: 1 }
        });
        // 将单个结果包装成数组
        setData([response.data || response]);
      } else {
        // 否则获取所有结果
        response = await http.get('/api/exec/test-results/', {
          params: {
            ...filters,
            start_date: filters.dateRange?.[0]?.format('YYYY-MM-DD'),
            end_date: filters.dateRange?.[1]?.format('YYYY-MM-DD'),
            token: 1 // 开发者后门
          }
        });
        setData(response.data || []);
      }
    } catch (error) {
      message.error('获取数据失败');
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // 解析日志数据为表格格式
  const parseLogData = (record) => {
    let parsedData = [];

    try {
      // 首先尝试解析 raw_log
      if (record.raw_log) {
        const rawData = JSON.parse(record.raw_log);
        if (Array.isArray(rawData)) {
          parsedData = rawData.map((item, index) => ({
            key: index,
            序号: index + 1,
            ...item
          }));
        }
      }

      // 如果 raw_log 没有数据，尝试解析 metrics
      if (parsedData.length === 0 && record.metrics) {
        const metricsData = typeof record.metrics === 'string' ? JSON.parse(record.metrics) : record.metrics;
        if (Array.isArray(metricsData)) {
          parsedData = metricsData.map((metric, index) => ({
            key: index,
            序号: index + 1,
            指标名称: metric.name || metric.label || `指标${index + 1}`,
            指标值: metric.value,
            单位: metric.unit || '',
            置信度: metric.confidence ? `${Math.round(metric.confidence * 100)}%` : '',
            类别: metric.category || '',
            文件名: metric.file_name || '',
            吞吐量: metric.throughput || '',
            延迟: metric.latency || ''
          }));
        }
      }
    } catch (error) {
      console.error('解析日志数据失败:', error);
    }

    return parsedData;
  };

  // 动态生成表格列
  const generateColumns = (data) => {
    if (!data || data.length === 0) return [];

    const firstRow = data[0];
    const columns = Object.keys(firstRow)
      .filter(key => key !== 'key') // 过滤掉key字段
      .map(key => ({
        title: key,
        dataIndex: key,
        key: key,
        width: key === '序号' ? 80 : 150,
        render: (text) => {
          if (text === null || text === undefined) return '-';
          if (typeof text === 'object') return JSON.stringify(text);
          return String(text);
        },
        sorter: (a, b) => {
          const aVal = a[key];
          const bVal = b[key];
          if (typeof aVal === 'number' && typeof bVal === 'number') {
            return aVal - bVal;
          }
          return String(aVal || '').localeCompare(String(bVal || ''));
        }
      }));

    return columns;
  };

  // 统计数据
  const stats = useMemo(() => {
    if (!data || data.length === 0) return {};
    
    const total = data.length;
    const completed = data.filter(item => item.status === 'completed').length;
    const running = data.filter(item => item.status === 'running').length;
    const failed = data.filter(item => item.status === 'failed').length;
    const avgMetrics = data.reduce((sum, item) => sum + (item.total_metrics || 0), 0) / total;
    const avgConfirmed = data.reduce((sum, item) => sum + (item.confirmed_metrics || 0), 0) / total;
    
    return {
      total,
      completed,
      running,
      failed,
      avgMetrics: Math.round(avgMetrics * 100) / 100,
      avgConfirmed: Math.round(avgConfirmed * 100) / 100,
      completionRate: Math.round((completed / total) * 100)
    };
  }, [data]);

  // 定义列
  const columns = useMemo(() => [
    {
      accessorKey: 'id',
      header: 'ID',
      size: 80,
      cell: ({ getValue }) => (
        <span className={styles.idCell}>{getValue()}</span>
      )
    },
    {
      accessorKey: 'plan_name',
      header: '测试计划',
      size: 200,
      cell: ({ getValue }) => (
        <Tooltip title={getValue()}>
          <div className={styles.planName}>{getValue()}</div>
        </Tooltip>
      )
    },
    {
      accessorKey: 'model_name',
      header: '模型名称',
      size: 150,
      cell: ({ getValue }) => (
        <Tag color="blue">{getValue() || '未指定'}</Tag>
      )
    },
    {
      accessorKey: 'gpu_name',
      header: 'GPU型号',
      size: 150,
      cell: ({ getValue }) => (
        <Tag color="green">{getValue() || '未指定'}</Tag>
      )
    },
    {
      accessorKey: 'status',
      header: '状态',
      size: 120,
      cell: ({ getValue }) => {
        const status = getValue();
        const statusConfig = {
          'completed': { color: 'success', text: '已完成' },
          'running': { color: 'processing', text: '运行中' },
          'failed': { color: 'error', text: '失败' },
          'pending': { color: 'default', text: '等待中' }
        };
        const config = statusConfig[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      accessorKey: 'total_metrics',
      header: '总指标数',
      size: 120,
      cell: ({ getValue }) => (
        <span className={styles.metricsCount}>{getValue() || 0}</span>
      )
    },
    {
      accessorKey: 'confirmed_metrics',
      header: '已确认指标',
      size: 150,
      cell: ({ getValue, row }) => {
        const confirmed = getValue() || 0;
        const total = row.original.total_metrics || 0;
        const percentage = total > 0 ? Math.round((confirmed / total) * 100) : 0;
        
        return (
          <div className={styles.metricsProgress}>
            <div className={styles.metricsText}>{confirmed}/{total}</div>
            <Progress 
              percent={percentage} 
              size="small" 
              strokeColor={percentage >= 80 ? '#52c41a' : percentage >= 60 ? '#faad14' : '#ff4d4f'}
              showInfo={false}
            />
            <span className={styles.percentageText}>{percentage}%</span>
          </div>
        );
      }
    },
    {
      accessorKey: 'created_time',
      header: '创建时间',
      size: 180,
      cell: ({ getValue }) => (
        <span className={styles.timeCell}>
          {getValue() ? new Date(getValue()).toLocaleString() : '-'}
        </span>
      )
    },
    {
      accessorKey: 'duration',
      header: '执行时长',
      size: 120,
      cell: ({ getValue }) => {
        const duration = getValue();
        if (!duration) return '-';
        
        const seconds = Math.floor(duration / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
          return `${hours}h ${minutes % 60}m`;
        } else if (minutes > 0) {
          return `${minutes}m ${seconds % 60}s`;
        } else {
          return `${seconds}s`;
        }
      }
    }
  ], []);

  // 创建表格实例
  const table = useReactTable({
    data: data || [],
    columns,
    state: {
      globalFilter,
      sorting,
    },
    onGlobalFilterChange: setGlobalFilter,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    globalFilterFn: 'includesString',
  });

  // 导出Excel
  const handleExport = () => {
    if (!data || data.length === 0) {
      message.warning('暂无数据可导出');
      return;
    }

    const wb = XLSX.utils.book_new();

    // 为每个记录创建一个工作表
    data.forEach((record, index) => {
      const parsedData = parseLogData(record);

      if (parsedData && parsedData.length > 0) {
        // 移除key字段，只保留实际数据
        const exportData = parsedData.map(item => {
          const { key, ...rest } = item;
          return rest;
        });

        const ws = XLSX.utils.json_to_sheet(exportData);
        const sheetName = `${record.plan_name || `记录${index + 1}`}`.substring(0, 31); // Excel工作表名称限制
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
      }
    });

    // 如果没有任何数据，创建一个空的工作表
    if (wb.SheetNames.length === 0) {
      const ws = XLSX.utils.json_to_sheet([{ '提示': '暂无可导出的数据' }]);
      XLSX.utils.book_append_sheet(wb, ws, '无数据');
    }

    const fileName = `日志分析结果_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);
    message.success('导出成功');
  };

  // 应用筛选
  const handleFilter = () => {
    fetchData();
  };

  // 重置筛选
  const handleReset = () => {
    setFilters({
      status: '',
      planName: '',
      modelName: '',
      gpuName: '',
      dateRange: null
    });
    setGlobalFilter('');
  };

  return (
    <div className={styles.pageContainer}>
      <div className={styles.pageHeader}>
        <h1>高级表格分析</h1>
        <p>测试结果的详细分析和统计</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} className={styles.statsRow}>
        <Col span={4}>
          <Card size="small" className={styles.statCard}>
            <Statistic title="总数" value={stats.total} />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small" className={styles.statCard}>
            <Statistic 
              title="已完成" 
              value={stats.completed} 
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small" className={styles.statCard}>
            <Statistic 
              title="运行中" 
              value={stats.running} 
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small" className={styles.statCard}>
            <Statistic 
              title="失败" 
              value={stats.failed} 
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small" className={styles.statCard}>
            <Statistic 
              title="完成率" 
              value={stats.completionRate} 
              suffix="%" 
              valueStyle={{ color: stats.completionRate >= 80 ? '#3f8600' : '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small" className={styles.statCard}>
            <Statistic 
              title="平均指标数" 
              value={stats.avgMetrics} 
              precision={1}
            />
          </Card>
        </Col>
      </Row>

      {/* 筛选器 */}
      <Card className={styles.filterCard}>
        <Row gutter={16} align="middle">
          <Col span={4}>
            <Select
              placeholder="状态筛选"
              value={filters.status}
              onChange={(value) => setFilters({...filters, status: value})}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="completed">已完成</Option>
              <Option value="running">运行中</Option>
              <Option value="failed">失败</Option>
              <Option value="pending">等待中</Option>
            </Select>
          </Col>
          <Col span={5}>
            <Input
              placeholder="测试计划名称"
              value={filters.planName}
              onChange={(e) => setFilters({...filters, planName: e.target.value})}
            />
          </Col>
          <Col span={4}>
            <Input
              placeholder="模型名称"
              value={filters.modelName}
              onChange={(e) => setFilters({...filters, modelName: e.target.value})}
            />
          </Col>
          <Col span={4}>
            <Input
              placeholder="GPU型号"
              value={filters.gpuName}
              onChange={(e) => setFilters({...filters, gpuName: e.target.value})}
            />
          </Col>
          <Col span={5}>
            <RangePicker
              value={filters.dateRange}
              onChange={(dates) => setFilters({...filters, dateRange: dates})}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={2}>
            <Space>
              <Button type="primary" onClick={handleFilter}>
                筛选
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 工具栏 */}
      <Card className={styles.toolbarCard}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Search
                placeholder="全局搜索..."
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
                style={{ width: 300 }}
                allowClear
              />
              <Button icon={<BarChartOutlined />}>
                图表分析
              </Button>
              <Button icon={<LineChartOutlined />}>
                趋势分析
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button 
                icon={<DownloadOutlined />} 
                onClick={handleExport}
                disabled={!data || data.length === 0}
              >
                导出Excel
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={fetchData}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 日志数据表格 */}
      <Card className={styles.tableCard}>
        <Spin spinning={loading}>
          {data && data.length > 0 ? (
            data.map((record, index) => {
              const parsedData = parseLogData(record);
              const dynamicColumns = generateColumns(parsedData);

              return (
                <div key={record.id || index} className={styles.recordSection}>
                  <div className={styles.recordHeader}>
                    <h3>{record.plan_name || `记录 ${index + 1}`}</h3>
                    <div className={styles.recordMeta}>
                      <Tag color="blue">GPU: {record.gpu_name || '未指定'}</Tag>
                      <Tag color="green">模型: {record.model_name || '未指定'}</Tag>
                      <Tag color="orange">指标数: {record.total_metrics || 0}</Tag>
                      <Tag color="purple">创建时间: {record.created_at || '-'}</Tag>
                    </div>
                  </div>

                  {parsedData && parsedData.length > 0 ? (
                    <Table
                      dataSource={parsedData}
                      columns={dynamicColumns}
                      pagination={{
                        pageSize: 50,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                      }}
                      scroll={{ x: 'max-content' }}
                      size="small"
                      bordered
                      className={styles.dataTable}
                    />
                  ) : (
                    <div className={styles.noData}>
                      <p>暂无解析数据</p>
                      {record.raw_log && (
                        <details>
                          <summary>查看原始日志</summary>
                          <pre className={styles.rawLog}>{record.raw_log}</pre>
                        </details>
                      )}
                    </div>
                  )}
                </div>
              );
            })
          ) : (
            <div className={styles.emptyState}>
              <p>暂无数据</p>
            </div>
          )}
        </Spin>
      </Card>

      {/* 原始表格（备用） */}
      <Card className={styles.tableCard} style={{ display: 'none' }}>
        <Spin spinning={loading}>
          <div className={styles.tableWrapper}>
            <table className={styles.advancedTable}>
              <thead>
                {table.getHeaderGroups().map(headerGroup => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <th
                        key={header.id}
                        style={{ width: header.getSize() }}
                        className={styles.tableHeader}
                      >
                        <div
                          className={`${styles.headerContent} ${
                            header.column.getCanSort() ? styles.sortable : ''
                          }`}
                          onClick={header.column.getToggleSortingHandler()}
                        >
                          {flexRender(header.column.columnDef.header, header.getContext())}
                          {header.column.getIsSorted() && (
                            <span className={styles.sortIcon}>
                              {header.column.getIsSorted() === 'desc' ? '↓' : '↑'}
                            </span>
                          )}
                        </div>
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody>
                {table.getRowModel().rows.map(row => (
                  <tr key={row.id} className={styles.tableRow}>
                    {row.getVisibleCells().map(cell => (
                      <td key={cell.id} className={styles.tableCell}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* 分页信息 */}
          <div className={styles.pagination}>
            <span>
              显示 {table.getFilteredRowModel().rows.length} 条结果
              {globalFilter && ` (从 ${data?.length || 0} 条中筛选)`}
            </span>
          </div>
        </Spin>
      </Card>
    </div>
  );
}

export default AdvancedTableAnalysisPage;
