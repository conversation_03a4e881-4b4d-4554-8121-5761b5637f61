# Spug服务器部署方案

## 1. 服务器信息

| 项目 | 信息 |
|------|------|
| 服务器IP | ************ |
| 操作系统 | Ubuntu 22.04.5 LTS |
| 部署目录 | /opt/spug |
| 访问地址 | http://************ |

## 2. 架构说明

采用生产环境标准架构：

- **前端**：React构建的静态文件，由Nginx提供服务
- **后端**：Django应用，使用Gunicorn运行（4个worker进程）
- **反向代理**：Nginx处理静态文件和API代理
- **数据库**：MySQL（**********，共享数据库）
- **缓存**：本地Redis服务

## 3. 服务端口

| 服务 | 端口 | 说明 |
|------|------|------|
| Nginx | 80 | 对外提供服务 |
| Gunicorn | 8000 | 内网API服务 |
| Redis | 6379 | 本地缓存服务 |

## 4. 管理脚本

### 4.1 简单重启脚本（推荐）⭐

**脚本路径**：`/opt/spug/simple_restart.sh`

**功能**：自动git pull更新代码 + 重启服务

**使用场景**：日常代码更新和重启

```bash
cd /opt/spug && bash simple_restart.sh
```

### 4.2 完整重启脚本

**脚本路径**：`/opt/spug/restart_spug.sh`

**功能**：代码更新 + 数据库迁移 + 前端重新构建 + 重启服务

**使用场景**：大版本更新或前端代码变更

```bash
cd /opt/spug && bash restart_spug.sh
```

### 4.3 生产环境管理脚本

**脚本路径**：`/opt/spug/start_spug_production.sh`

**功能**：手动管理服务启停和状态查看

```bash
bash start_spug_production.sh start    # 启动服务
bash start_spug_production.sh stop     # 停止服务
bash start_spug_production.sh restart  # 重启服务
bash start_spug_production.sh status   # 查看状态
```

## 5. 重要文件路径

| 类型 | 路径 |
|------|------|
| 项目根目录 | `/opt/spug/` |
| 前端源码 | `/opt/spug/spug_web/` |
| 后端源码 | `/opt/spug/spug_api/` |
| 日志目录 | `/opt/spug/logs/` |
| Nginx配置 | `/etc/nginx/sites-available/spug` |
| 管理脚本 | `/opt/spug/*.sh` |

## 6. 日志文件

| 日志类型 | 路径 |
|----------|------|
| API服务日志 | `/opt/spug/logs/spug_api.log` |
| 访问日志 | `/opt/spug/logs/spug_access.log` |
| Nginx日志 | `/var/log/nginx/` |

## 7. 常用操作

### 7.1 代码更新重启

```bash
# 日常更新（推荐）
cd /opt/spug && bash simple_restart.sh

# 大版本更新
cd /opt/spug && bash restart_spug.sh
```

### 7.2 查看服务状态

```bash
# 查看所有服务状态
cd /opt/spug && bash start_spug_production.sh status

# 查看进程
ps aux | grep -E "(gunicorn|nginx)"

# 查看端口
netstat -tlnp | grep -E "(80|8000)"
```

### 7.3 查看日志

```bash
# 查看API日志
tail -f /opt/spug/logs/spug_api.log

# 查看访问日志
tail -f /opt/spug/logs/spug_access.log

# 查看Nginx错误日志
tail -f /var/log/nginx/error.log
```

### 7.4 数据库操作

```bash
# 进入API目录
cd /opt/spug/spug_api

# 创建迁移文件
python3 manage.py makemigrations

# 执行数据库迁移
python3 manage.py migrate

# 创建超级用户
python3 manage.py createsuperuser
```

## 8. 故障排查

| 问题 | 解决方案 |
|------|----------|
| 无法访问网站 | 检查Nginx状态：`systemctl status nginx` |
| API请求失败 | 检查Gunicorn进程：`ps aux \| grep gunicorn` |
| 数据库连接失败 | 检查数据库配置和网络连通性 |
| 服务启动失败 | 查看日志文件定位错误原因 |

## 9. 备注

- 数据库使用共享MySQL服务器（**********），无需在本机安装MySQL
- 系统支持热更新，修改代码后运行重启脚本即可生效
- 生产环境使用Nginx+Gunicorn架构，性能和稳定性更好
- 所有管理脚本都有彩色输出和详细状态显示
- 建议定期备份重要数据和配置文件

