#!/usr/bin/env python3
"""
环境切换脚本
用法：
  python switch_env.py dev   # 切换到开发环境
  python switch_env.py prod  # 切换到生产环境
"""

import sys
import os
import shutil

def switch_environment(env):
    if env not in ['dev', 'prod']:
        print("❌ 错误：环境参数必须是 'dev' 或 'prod'")
        sys.exit(1)
    
    # 后端配置切换
    backend_source = f"spug_api/spug/settings_{env}.py"
    backend_target = "spug_api/spug/settings_current.py"
    
    # 前端配置切换
    frontend_source = f"spug_web/src/setupProxy.{env}.js"
    frontend_target = "spug_web/src/setupProxy.js"
    
    try:
        # 检查源文件是否存在
        if not os.path.exists(backend_source):
            print(f"❌ 错误：后端配置文件 {backend_source} 不存在")
            sys.exit(1)
            
        if not os.path.exists(frontend_source):
            print(f"❌ 错误：前端配置文件 {frontend_source} 不存在")
            sys.exit(1)
        
        # 复制配置文件
        shutil.copy2(backend_source, backend_target)
        shutil.copy2(frontend_source, frontend_target)
        
        # 更新Django设置环境变量提示
        env_name = "开发环境" if env == "dev" else "生产环境"
        print(f"✅ 成功切换到{env_name}")
        print(f"📁 后端配置: {backend_source} -> {backend_target}")
        print(f"📁 前端配置: {frontend_source} -> {frontend_target}")
        
        if env == "dev":
            print("🔧 开发环境配置:")
            print("   - DEBUG = True")
            print("   - 开发者后门已启用")
            print("   - API端口: 9999")
        else:
            print("🚀 生产环境配置:")
            print("   - DEBUG = False")
            print("   - 开发者后门已禁用")
            print("   - API端口: 8000")
            
        print("\n💡 提示：请重启服务以使配置生效")
        
    except Exception as e:
        print(f"❌ 切换失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python switch_env.py [dev|prod]")
        sys.exit(1)
    
    switch_environment(sys.argv[1])
