.index {
  display: flex;
  height: calc(100vh - 218px);
  min-height: 420px;
  background-color: #fff;
  overflow: hidden;

  .left {
    padding: 24px;
    width: 60%;
    border-right: 1px solid #dfdfdf;
    overflow-y: auto;
    max-height: 100%;

    .tips {
      position: absolute;
      top: 10px;
      left: 180px;
      color: #999;
    }

    .tips:hover {
      color: #777;
    }

    .editor {
      height: 300px !important;
      min-height: 200px;
    }
  }

  .right {
    width: 40%;
    max-width: 600px;
    display: flex;
    flex-direction: column;
    background-color: #fafafa;
    padding: 24px 24px 0 24px;

    .title {
      font-weight: 500;
      margin-bottom: 12px;
    }

    .inner {
      flex: 1;
      overflow: auto;
    }

    .item {
      display: flex;
      align-items: center;
      border-radius: 2px;
      padding: 8px 12px;
      cursor: pointer;
      margin-bottom: 12px;

      .sh {
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        color: #fff;
        font-weight: 500;
        border-radius: 2px;
        background-color: #1890ff;
      }

      .python {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        width: 20px;
        height: 20px;
        font-weight: 500;
        border-radius: 2px;
        background-color: #dca900;
      }

      .number {
        width: 24px;
        text-align: center;
        margin-left: 12px;
        border-radius: 2px;
        font-weight: 500;
        background-color: #dfdfdf;
      }

      .command {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin: 0 12px;
      }

      .tpl {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin: 0 12px;
        background-color: #d2e7fd;
        padding: 0 8px;
        border-radius: 2px;
      }

      .desc {
        color: #999;
      }
    }

    .item:hover {
      border-color: #1890ff;
      background-color: #e6f7ff;
    }
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.output {
  display: flex;
  background-color: #fff;
  height: calc(100vh - 218px);
  overflow: hidden;

  .side {
    display: flex;
    flex-direction: column;
    width: 350px;
    border-right: 1px solid #e8e8e8;
    background-color: #fafafa;

    .tags {
      padding: 0 24px 24px;
      display: flex;
      justify-content: space-between;

      .item {
        width: 70px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        border-radius: 35px;
        padding: 2px 8px;
        cursor: pointer;
        background-color: #f3f3f3;
        color: #666;
        user-select: none;
      }

      .pendingOn {
        background-color: #1890ff;
        color: #fff;
      }

      .pending {
        color: #1890ff;
      }

      .pending:hover {
        background-color: #1890ff;
        opacity: 0.7;
        color: #fff;
      }

      .successOn {
        background-color: #52c41a;
        color: #fff;
      }

      .success {
        color: #52c41a;
      }

      .success:hover {
        background-color: #52c41a;
        opacity: 0.7;
        color: #fff;
      }

      .failOn {
        background-color: red;
        color: #fff;
      }

      .fail {
        color: red;
      }

      .fail:hover {
        background-color: red;
        opacity: 0.6;
        color: #fff;
      }
    }

    .list {
      flex: 1;
      overflow: auto;
      padding-bottom: 8px;

      .item {
        display: flex;
        align-items: center;
        padding: 8px 24px;
        cursor: pointer;

        &.active {
          background: #e6f7ff;
        }

        :global(.anticon) {
          margin-right: 4px;
        }

        .text {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          user-select: none;
        }
      }

      .item:hover {
        background: #e6f7ff;
      }
    }
  }

  .body {
    display: flex;
    flex-direction: column;
    width: calc(100% - 350px);
    padding: 16px 24px;
    overflow: hidden;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .icon {
        font-size: 18px;
        color: #1890ff;
        cursor: pointer;
      }

      .title {
        font-weight: 500;
      }
    }


    .termContainer {
      background-color: #1e1e1e;
      padding: 12px;
      border-radius: 6px;
      border: 1px solid #e8e8e8;
      flex: 1;
      display: flex;
      flex-direction: column;

      .term {
        width: 100%;
        height: 100%;
        min-height: 400px;
      }
    }
  }
}