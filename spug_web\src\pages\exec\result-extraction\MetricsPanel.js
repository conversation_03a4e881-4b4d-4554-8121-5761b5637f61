/**
 * 智能指标管理面板 - 支持拖拽添加和批量操作
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Tag, 
  Progress, 
  List, 
  Input, 
  Select, 
  Tooltip,
  Popconfirm,
  message,
  Badge,
  Modal
} from 'antd';
import { 
  CheckCircleOutlined,
  CloseCircleOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  AppstoreOutlined,
  ExclamationCircleOutlined,
  RobotOutlined,
  DragOutlined
} from '@ant-design/icons';
import styles from './MetricsPanel.module.less';

const { Option } = Select;
const { TextArea } = Input;

function MetricsPanel({ metrics, onMetricsUpdate, onBatchOperation }) {
  const [editingMetric, setEditingMetric] = useState(null);
  const [batchMode, setBatchMode] = useState(false);
  const [selectedMetrics, setSelectedMetrics] = useState([]);
  const [dragOver, setDragOver] = useState(false);

  // 计算指标统计信息
  const getMetricsStats = () => {
    const total = metrics.length;
    const confirmed = metrics.filter(m => m.status === 'confirmed').length;
    const pending = metrics.filter(m => m.status === 'pending').length;
    const rejected = metrics.filter(m => m.status === 'rejected').length;
    const avgConfidence = total > 0 ?
      (metrics.reduce((sum, m) => sum + (m.confidence || 0), 0) / total * 100).toFixed(1) : 0;

    // 按分类统计
    const categories = {};
    metrics.forEach(m => {
      const category = m.category || 'General';
      categories[category] = (categories[category] || 0) + 1;
    });

    return { total, confirmed, pending, rejected, avgConfidence, categories };
  };

  // 智能解析拖拽文本为指标
  const parseTextToMetric = (text) => {
    // 改进的解析算法：寻找 单位+数据+括号 的组合（三选二）
    const patterns = [
      // 模式1: 括号 + 数据 + 单位 (如: AveragePCIeSpeed(GB/s): 30.284781)
      {
        regex: /([^:\(]+)\(([^)]+)\):\s*([0-9.,]+)/i,
        extract: (match) => ({
          label: match[1].trim(),
          value: match[3].replace(',', ''),
          unit: match[2],
          confidence: 0.95
        })
      },
      // 模式2: 标签 + 数据 + 单位 (如: Memory Bandwidth: 30.5 GB/s)
      {
        regex: /([^:]+):\s*([0-9.,]+)\s*([A-Za-z\/°%]+)/i,
        extract: (match) => ({
          label: match[1].trim(),
          value: match[2].replace(',', ''),
          unit: match[3],
          confidence: 0.90
        })
      },
      // 模式3: 数据 + 单位 (如: 30.5 GB/s, 85°C, 150W)
      {
        regex: /([0-9.,]+)\s*([A-Za-z\/°%]+)/i,
        extract: (match) => ({
          label: '未知指标',
          value: match[1].replace(',', ''),
          unit: match[2],
          confidence: 0.70
        })
      },
      // 模式4: 带括号的数据 (如: (30.5))
      {
        regex: /\(([0-9.,]+)\)/i,
        extract: (match) => ({
          label: '括号数据',
          value: match[1].replace(',', ''),
          unit: '',
          confidence: 0.60
        })
      }
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern.regex);
      if (match) {
        const metric = pattern.extract(match);
        
        // 智能推断标签
        if (metric.label === '未知指标' || metric.label === '括号数据') {
          metric.label = inferLabelFromContext(text, metric.unit);
        }
        
        return {
          id: Date.now() + Math.random(),
          ...metric,
          status: 'pending',
          originalText: text
        };
      }
    }

    // 如果没有匹配到任何模式，返回原始文本
    return {
      id: Date.now() + Math.random(),
      label: '自定义指标',
      value: '',
      unit: '',
      confidence: 0.30,
      status: 'pending',
      originalText: text
    };
  };

  // 根据上下文和单位推断标签
  const inferLabelFromContext = (text, unit) => {
    const lowerText = text.toLowerCase();
    
    if (unit.includes('GB/s') || unit.includes('MB/s')) {
      if (lowerText.includes('pcie') || lowerText.includes('pci')) {
        return 'PCIe带宽';
      }
      if (lowerText.includes('memory') || lowerText.includes('mem')) {
        return '内存带宽';
      }
      return '数据传输速度';
    }
    
    if (unit.includes('GFLOPS') || unit.includes('TFLOPS')) {
      return 'GPU计算性能';
    }
    
    if (unit.includes('°C') || unit.includes('celsius')) {
      return '温度';
    }
    
    if (unit.includes('W') || unit.includes('watt')) {
      return '功耗';
    }
    
    if (unit.includes('ms') || unit.includes('us') || unit.includes('ns')) {
      return '延迟';
    }
    
    if (unit.includes('%')) {
      return '百分比指标';
    }
    
    if (unit.includes('Hz') || unit.includes('MHz') || unit.includes('GHz')) {
      return '频率';
    }
    
    return '性能指标';
  };

  // 处理拖拽进入
  const handleDragEnter = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  // 处理拖拽悬停
  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };

  // 处理拖拽离开
  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  // 处理拖拽放下
  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    const text = e.dataTransfer.getData('text/plain');
    if (text && text.trim()) {
      const newMetric = parseTextToMetric(text.trim());
      
      // 添加到指标列表
      const updatedMetrics = [...metrics, newMetric];
      onMetricsUpdate(updatedMetrics);
      
      message.success(`已添加指标: ${newMetric.label}`);
      
      // 自动开始编辑新添加的指标
      setEditingMetric(newMetric.id);
    }
  };

  const handleConfirmMetric = (metricId) => {
    const updatedMetrics = metrics.map(metric => 
      metric.id === metricId 
        ? { ...metric, status: 'confirmed' }
        : metric
    );
    onMetricsUpdate(updatedMetrics);
    message.success('指标已确认');
  };

  const handleEditMetric = (metricId) => {
    setEditingMetric(metricId);
  };

  const handleSaveEdit = (metricId, updatedData) => {
    const updatedMetrics = metrics.map(metric => 
      metric.id === metricId 
        ? { ...metric, ...updatedData, status: 'confirmed' }
        : metric
    );
    onMetricsUpdate(updatedMetrics);
    setEditingMetric(null);
    message.success('指标已更新');
  };

  const handleDeleteMetric = (metricId) => {
    const updatedMetrics = metrics.filter(metric => metric.id !== metricId);
    onMetricsUpdate(updatedMetrics);
    message.success('指标已删除');
  };

  const handleBatchConfirm = () => {
    const updatedMetrics = metrics.map(metric => 
      selectedMetrics.includes(metric.id)
        ? { ...metric, status: 'confirmed' }
        : metric
    );
    onMetricsUpdate(updatedMetrics);
    setSelectedMetrics([]);
    setBatchMode(false);
    message.success(`已确认 ${selectedMetrics.length} 个指标`);
  };

  const handleBatchDelete = () => {
    const updatedMetrics = metrics.filter(metric => 
      !selectedMetrics.includes(metric.id)
    );
    onMetricsUpdate(updatedMetrics);
    setSelectedMetrics([]);
    setBatchMode(false);
    message.success(`已删除 ${selectedMetrics.length} 个指标`);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed': return 'success';
      case 'pending': return 'warning';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'confirmed': return <CheckCircleOutlined />;
      case 'pending': return <ExclamationCircleOutlined />;
      case 'rejected': return <CloseCircleOutlined />;
      default: return null;
    }
  };

  const confirmedCount = metrics.filter(m => m.status === 'confirmed').length;
  const pendingCount = metrics.filter(m => m.status === 'pending').length;

  return (
    <div className={styles.metricsPanel}>
      <Card
        className={styles.metricsCard}
        title={
          <div className={styles.panelHeader}>
            <div className={styles.panelTitle}>
              <span>指标管理</span>
              <div className={styles.progressInfo}>
                <Progress
                  percent={metrics.length > 0 ? Math.round((confirmedCount / metrics.length) * 100) : 0}
                  size="small"
                  status={confirmedCount === metrics.length && metrics.length > 0 ? 'success' : 'active'}
                  style={{ width: '120px' }}
                />
                <span style={{ marginLeft: '8px', fontSize: '12px', color: '#666' }}>
                  {confirmedCount}/{metrics.length}
                </span>
              </div>
            </div>
            <Space>
              <Badge count={confirmedCount} style={{ backgroundColor: '#52c41a' }}>
                <Tag color="success">已确认</Tag>
              </Badge>
              <Badge count={pendingCount} style={{ backgroundColor: '#faad14' }}>
                <Tag color="warning">待处理</Tag>
              </Badge>
            </Space>
          </div>
        }
        extra={
          <Space>
            <Button
              size="small"
              icon={<AppstoreOutlined />}
              onClick={() => setBatchMode(!batchMode)}
              type={batchMode ? 'primary' : 'default'}
            >
              批量操作
            </Button>
            {batchMode && (
              <>
                <Button
                  size="small"
                  onClick={() => {
                    const allIds = metrics.map(metric => metric.id);
                    setSelectedMetrics(allIds);
                  }}
                >
                  全选 ({metrics.length})
                </Button>
                {selectedMetrics.length > 0 && (
                  <>
                    <Button
                      size="small"
                      type="primary"
                      onClick={handleBatchConfirm}
                    >
                      批量确认 ({selectedMetrics.length})
                    </Button>
                    <Popconfirm
                      title="确定要删除选中的指标吗？"
                      onConfirm={handleBatchDelete}
                    >
                      <Button size="small" danger>
                        批量删除 ({selectedMetrics.length})
                      </Button>
                    </Popconfirm>
                    <Button 
                      size="small" 
                      onClick={() => setSelectedMetrics([])}
                    >
                      取消选择
                    </Button>
                  </>
                )}
              </>
            )}
          </Space>
        }
      >
        <div 
          className={`${styles.dropZone} ${dragOver ? styles.dragOver : ''}`}
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {dragOver && (
            <div className={styles.dropHint}>
              <DragOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
              <div>松开鼠标以添加指标</div>
            </div>
          )}
          
          {metrics.length === 0 ? (
            <div className={styles.emptyState}>
              <RobotOutlined style={{ fontSize: '48px', color: '#ccc' }} />
              <div style={{ marginTop: '16px', color: '#999' }}>
                从左侧日志中选择文本拖拽到此处
              </div>
              <div style={{ color: '#ccc', fontSize: '12px', marginTop: '8px' }}>
                或点击"选择AI建议"自动添加指标
              </div>
            </div>
          ) : (
            <List
              dataSource={metrics}
              renderItem={(metric) => (
                <MetricItem
                  key={metric.id}
                  metric={metric}
                  editing={editingMetric === metric.id}
                  batchMode={batchMode}
                  selected={selectedMetrics.includes(metric.id)}
                  onConfirm={() => handleConfirmMetric(metric.id)}
                  onEdit={() => handleEditMetric(metric.id)}
                  onSave={(data) => handleSaveEdit(metric.id, data)}
                  onDelete={() => handleDeleteMetric(metric.id)}
                  onSelect={(selected) => {
                    if (selected) {
                      setSelectedMetrics([...selectedMetrics, metric.id]);
                    } else {
                      setSelectedMetrics(selectedMetrics.filter(id => id !== metric.id));
                    }
                  }}
                />
              )}
            />
          )}
        </div>
      </Card>
    </div>
  );
}

// 指标项组件
function MetricItem({ 
  metric, 
  editing, 
  batchMode, 
  selected, 
  onConfirm, 
  onEdit, 
  onSave, 
  onDelete, 
  onSelect 
}) {
  const [editData, setEditData] = useState({
    label: metric.label,
    value: metric.value,
    unit: metric.unit
  });

  const handleSave = () => {
    onSave(editData);
  };

  const handleCancel = () => {
    setEditData({
      label: metric.label,
      value: metric.value,
      unit: metric.unit
    });
  };

  if (editing) {
    return (
      <List.Item className={styles.editingItem}>
        <div className={styles.editForm}>
          <Input
            placeholder="指标名称"
            value={editData.label}
            onChange={(e) => setEditData({...editData, label: e.target.value})}
            style={{ marginBottom: '8px' }}
          />
          <Space>
            <Input
              placeholder="数值"
              value={editData.value}
              onChange={(e) => setEditData({...editData, value: e.target.value})}
              style={{ width: '120px' }}
            />
            <Input
              placeholder="单位"
              value={editData.unit}
              onChange={(e) => setEditData({...editData, unit: e.target.value})}
              style={{ width: '80px' }}
            />
          </Space>
          <div style={{ marginTop: '8px' }}>
            <Space>
              <Button size="small" type="primary" onClick={handleSave}>
                保存
              </Button>
              <Button size="small" onClick={handleCancel}>
                取消
              </Button>
            </Space>
          </div>
        </div>
      </List.Item>
    );
  }

  return (
    <List.Item 
      className={`${styles.metricItem} ${selected ? styles.selected : ''}`}
      actions={[
        batchMode ? (
          <input
            type="checkbox"
            checked={selected}
            onChange={(e) => onSelect(e.target.checked)}
          />
        ) : (
          <Space>
            {metric.status === 'pending' && (
              <Button 
                size="small" 
                type="primary" 
                icon={<CheckCircleOutlined />}
                onClick={onConfirm}
              >
                确认
              </Button>
            )}
            <Button 
              size="small" 
              icon={<EditOutlined />}
              onClick={onEdit}
            />
            <Popconfirm title="确定删除？" onConfirm={onDelete}>
              <Button 
                size="small" 
                danger 
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Space>
        )
      ]}
    >
      <div className={styles.metricContent}>
        <div className={styles.metricInfo}>
          <div className={styles.metricHeader}>
            <span className={styles.metricLabel}>{metric.label}</span>
            <Tag color={getStatusColor(metric.status)} size="small">
              {getStatusIcon(metric.status)}
              {metric.status === 'confirmed' ? '已确认' : 
               metric.status === 'pending' ? '待确认' : '已拒绝'}
            </Tag>
          </div>
          
          <div className={styles.metricValue}>
            <span className={styles.value}>
              {metric.value}
              {metric.unit && <span className={styles.unit}>{metric.unit}</span>}
            </span>
          </div>
          
          <div className={styles.metricMeta}>
            {metric.confidence && (
              <div className={styles.confidence}>
                <div className={styles.confidenceBar}>
                  <div
                    className={`${styles.confidenceFill} ${
                      metric.confidence > 0.8 ? styles.high :
                      metric.confidence > 0.6 ? styles.medium : styles.low
                    }`}
                    style={{ width: `${metric.confidence * 100}%` }}
                  />
                </div>
                <span className={styles.confidenceText}>
                  {Math.round(metric.confidence * 100)}%
                </span>
              </div>
            )}
            
            {metric.lineNumbers && metric.lineNumbers.length > 0 && (
              <span className={styles.lineNumber}>
                L{metric.lineNumbers.join(', L')}
              </span>
            )}
          </div>
          
          {metric.originalText && (
            <Tooltip title={metric.originalText}>
              <div className={styles.originalText}>
                原文: {metric.originalText.substring(0, 30)}
                {metric.originalText.length > 30 ? '...' : ''}
              </div>
            </Tooltip>
          )}
        </div>
      </div>
    </List.Item>
  );
}

// 辅助函数
function getStatusColor(status) {
  switch (status) {
    case 'confirmed': return 'success';
    case 'pending': return 'warning';
    case 'rejected': return 'error';
    default: return 'default';
  }
}

function getStatusIcon(status) {
  switch (status) {
    case 'confirmed': return <CheckCircleOutlined />;
    case 'pending': return <ExclamationCircleOutlined />;
    case 'rejected': return <CloseCircleOutlined />;
    default: return null;
  }
}

export default MetricsPanel; 