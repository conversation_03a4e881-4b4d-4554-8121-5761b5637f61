<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型存储系统 - 用户手册</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .version-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-top: 20px;
        }

        /* 导航栏 */
        .nav {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: #2c3e50;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #667eea;
        }

        /* 主要内容区域 */
        .main-content {
            padding: 60px 0;
        }

        .section {
            margin-bottom: 80px;
        }

        .section-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 50px;
        }

        /* 功能卡片 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            border: 1px solid #e9ecef;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #667eea;
        }

        .feature-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .feature-description {
            color: #7f8c8d;
            line-height: 1.6;
        }

        /* 步骤指南 */
        .steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .step {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            position: relative;
            border: 2px solid #e9ecef;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .step:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .step-number {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .step-title {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .step-description {
            color: #7f8c8d;
        }

        /* 截图样式 */
        .screenshot {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            text-align: center;
            border: 2px dashed #dee2e6;
        }

        .screenshot img {
            max-width: 100%;
            border-radius: 8px;
        }

        .screenshot-placeholder {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .screenshot-caption {
            color: #6c757d;
            font-style: italic;
        }

        /* 代码块样式 */
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
            overflow-x: auto;
        }

        /* 提示框 */
        .tip, .warning, .info {
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid;
        }

        .tip {
            background: #f0f9ff;
            border-color: #0ea5e9;
            color: #0c4a6e;
        }

        .warning {
            background: #fef3c7;
            border-color: #f59e0b;
            color: #92400e;
        }

        .info {
            background: #ecfdf5;
            border-color: #10b981;
            color: #065f46;
        }

        /* 底部 */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .steps {
                grid-template-columns: 1fr;
            }
        }
        
        /* FAQ样式 */
        .faq-item {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .faq-question {
            color: #667eea;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .faq-answer {
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1><i class="fas fa-database"></i> 模型存储系统</h1>
                <p>智能化的模型存储监控与发布计划管理平台</p>
                <div class="version-badge">v3.0 用户手册</div>
            </div>
        </div>
    </header>

    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container">
            <div class="nav-content">
                <div class="logo">
                    <strong>用户手册</strong>
                </div>
                <ul class="nav-links">
                    <li><a href="#overview">系统概述</a></li>
                    <li><a href="#features">功能介绍</a></li>
                    <li><a href="#getting-started">快速开始</a></li>
                    <li><a href="#tutorials">使用教程</a></li>
                    <li><a href="#faq">常见问题</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 系统概述 -->
            <section id="overview" class="section">
                <h2 class="section-title">系统概述</h2>
                <p class="section-subtitle">模型存储系统是一个综合性的AI模型管理平台，提供服务器监控、文件管理、发布计划和测试任务管理功能</p>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <h3 class="feature-title">服务器监控</h3>
                        <p class="feature-description">实时监控服务器CPU、内存、磁盘使用率和网络状态，确保系统稳定运行</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-folder-tree"></i>
                        </div>
                        <h3 class="feature-title">文件管理</h3>
                        <p class="feature-description">可视化文件树浏览，支持本地和远程文件对比，轻松管理模型文件</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <h3 class="feature-title">发布计划</h3>
                        <p class="feature-description">创建和管理模型发布计划，支持甘特图可视化，提高项目管理效率</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <h3 class="feature-title">测试任务</h3>
                        <p class="feature-description">完整的测试任务管理，支持Excel导入导出，进度跟踪和状态管理</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title">数据报告</h3>
                        <p class="feature-description">自动生成HTML格式周报，美观的数据可视化和统计分析</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="feature-title">安全可靠</h3>
                        <p class="feature-description">完善的权限控制和数据备份机制，确保数据安全和系统稳定</p>
                    </div>
                </div>
            </section>

            <!-- 功能介绍 -->
            <section id="features" class="section">
                <h2 class="section-title">功能介绍</h2>
                <p class="section-subtitle">详细了解系统的各项功能特性</p>

                <!-- 服务器监控 -->
                <div class="feature-detail">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-server"></i> 服务器监控
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        实时监控远程服务器的系统资源使用情况，包括CPU、内存、磁盘和网络状态。
                    </p>
                    
                    <div class="screenshot">
                        <img src="服务器监控.png" alt="服务器监控界面">
                        <p class="screenshot-caption">实时显示服务器CPU、内存、磁盘使用率和网络流量</p>
                    </div>

                    <div class="info">
                        <strong>主要功能：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>实时CPU使用率监控</li>
                            <li>内存使用情况统计</li>
                            <li>磁盘空间使用率</li>
                            <li>网络上传/下载速度</li>
                            <li>历史数据记录和清理</li>
                        </ul>
                    </div>
                </div>

                <!-- 文件管理 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-folder-tree"></i> 文件管理
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        提供直观的文件树浏览界面，支持本地和远程文件对比，帮助管理模型文件。
                    </p>
                    
                    <div class="screenshot">
                        <img src="文件管理.png" alt="文件树浏览界面">
                        <p class="screenshot-caption">左侧显示文件树结构，右侧显示文件详情和对比结果</p>
                    </div>

                    <div class="info">
                        <strong>主要功能：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>可视化文件树浏览</li>
                            <li>本地和远程文件对比</li>
                            <li>文件状态标识（同步/缺失/冲突）</li>
                            <li>懒加载提高性能</li>
                            <li>文件详情查看</li>
                        </ul>
                    </div>
                </div>

                <!-- 发布计划管理 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-project-diagram"></i> 发布计划管理
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        创建和管理模型发布计划，支持甘特图可视化，提高项目管理效率。
                    </p>
                    
                    <div class="screenshot">
                        <img src="发布计划.png" alt="发布计划管理界面">
                        <p class="screenshot-caption">发布计划列表视图，显示计划状态和基本信息</p>
                    </div>

                    <div class="info">
                        <strong>主要功能：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>创建和编辑发布计划</li>
                            <li>计划状态管理</li>
                            <li>时间范围设置</li>
                            <li>甘特图可视化</li>
                            <li>计划统计分析</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 测试任务管理 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-tasks"></i> 测试任务管理
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        管理测试任务的完整生命周期，支持手动创建和Excel批量导入。
                    </p>
                    
                    <div class="screenshot">
                        <img src="测试计划.png" alt="测试任务管理界面">
                        <p class="screenshot-caption">测试任务甘特图视图，可视化展示任务进度</p>
                    </div>

                    <div class="info">
                        <strong>主要功能：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>测试任务创建和编辑</li>
                            <li>Excel批量导入导出</li>
                            <li>任务状态跟踪（待开始、进行中、已完成等）</li>
                            <li>甘特图可视化进度</li>
                            <li>人员和资源分配</li>
                            <li>优先级管理（P1/P2/P3）</li>
                            <li>关联测试用例集执行</li>
                            <li>资料输出跟踪</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 统计报表 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-chart-bar"></i> 统计报表
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        提供全面的统计信息和报表功能，帮助管理者了解项目进展。
                    </p>
                    
                    <div class="screenshot">
                        <img src="统计报表.png" alt="统计报表界面">
                        <p class="screenshot-caption">测试计划统计概览，包含总体概况、人员效率、GPU资源和风险预警</p>
                    </div>

                    <div class="info">
                        <strong>主要功能：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>测试计划执行情况统计</li>
                            <li>人员效率分析</li>
                            <li>GPU资源使用情况</li>
                            <li>风险预警监控</li>
                            <li>HTML格式周报导出</li>
                            <li>数据可视化图表</li>
                            <li>自定义统计维度</li>
                        </ul>
                    </div>
                </div>

                <!-- 文件比较功能 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-code-compare"></i> 文件比较分析
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        提供强大的文件比较功能，支持模型文件的版本对比和差异分析。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            文件比较界面截图
                        </div>
                        <p class="screenshot-caption">并排显示文件差异，高亮显示变更内容</p>
                    </div>

                    <div class="info">
                        <strong>比较功能：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>文本文件逐行比较</li>
                            <li>二进制文件哈希对比</li>
                            <li>目录结构差异分析</li>
                            <li>版本变更历史追踪</li>
                            <li>差异报告生成</li>
                            <li>合并冲突解决</li>
                        </ul>
                    </div>
                </div>

                <!-- 增强统计分析 -->
                <div class="feature-detail" style="margin-top: 60px;">
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-chart-line"></i> 增强统计分析
                    </h3>
                    <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 30px;">
                        提供全面的数据统计和可视化分析，支持多维度的数据洞察和模型完整性检查。
                    </p>

                    <div class="screenshot">
                        <div class="screenshot-placeholder">
                            <i class="fas fa-image" style="font-size: 2rem; margin-right: 10px;"></i>
                            增强统计分析仪表板截图
                        </div>
                        <p class="screenshot-caption">丰富的图表和统计指标，支持交互式数据探索</p>
                    </div>

                    <div class="info">
                        <strong>增强功能：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>模型完整性统计（Word+PDF文件匹配率）</li>
                            <li>文件类型分布分析</li>
                            <li>存储空间使用趋势</li>
                            <li>测试覆盖率统计</li>
                            <li>团队工作量分析</li>
                            <li>性能指标监控</li>
                            <li>自定义报表生成</li>
                            <li>数据导出和分享</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 快速开始 -->
            <section id="getting-started" class="section">
                <h2 class="section-title">快速开始</h2>
                <p class="section-subtitle">按照以下步骤快速上手使用系统</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">访问系统</h3>
                        <p class="step-description">打开浏览器，访问系统主页，查看整体监控面板</p>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">创建发布计划</h3>
                        <p class="step-description">点击"新建发布计划"，填写计划信息和时间范围</p>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">添加测试任务</h3>
                        <p class="step-description">在甘特图页面添加测试任务，或使用Excel批量导入</p>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">4</div>
                        <h3 class="step-title">跟踪进度</h3>
                        <p class="step-description">实时更新任务状态和进度，使用甘特图查看整体进展</p>
                    </div>
                </div>
            </section>

            <!-- 使用教程 -->
            <section id="tutorials" class="section">
                <h2 class="section-title">使用教程</h2>
                <p class="section-subtitle">详细的操作指南和最佳实践</p>

                <!-- 创建发布计划教程 -->
                <div class="tutorial">
                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #2c3e50;">
                        📋 如何创建发布计划
                    </h3>
                    
                    <div class="steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <h4 class="step-title">进入发布计划页面</h4>
                            <p class="step-description">点击主页的"查看全部"或直接访问发布计划管理页面</p>
                        </div>
                        
                        <div class="step">
                            <div class="step-number">2</div>
                            <h4 class="step-title">点击新建按钮</h4>
                            <p class="step-description">点击页面右上角的"新建发布计划"按钮</p>
                        </div>
                        
                        <div class="step">
                            <div class="step-number">3</div>
                            <h4 class="step-title">填写计划信息</h4>
                            <p class="step-description">输入计划名称、描述和起止时间</p>
                        </div>
                        
                        <div class="step">
                            <div class="step-number">4</div>
                            <h4 class="step-title">保存计划</h4>
                            <p class="step-description">点击确定按钮保存，系统会自动生成计划ID</p>
                        </div>
                    </div>

                    <div class="tip">
                        <strong>💡 小贴士：</strong> 建议计划名称使用有意义的命名规则，如"模型名称-版本-发布时间"，方便后续管理和查找。
                    </div>
                </div>

                <!-- 测试任务管理教程 -->
                <div class="tutorial" style="margin-top: 50px;">
                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #2c3e50;">
                        ⚙️ 测试任务管理
                    </h3>
                    
                    <h4 style="color: #667eea; margin-bottom: 15px;">手动添加任务</h4>
                    <ol style="padding-left: 20px; color: #7f8c8d;">
                        <li>进入发布计划的甘特图页面</li>
                        <li>点击"新增任务"按钮</li>
                        <li>填写任务信息：模型名称、测试人员、模型类型、GPU型号、时间范围、优先级等</li>
                        <li>设置任务备注和其他相关信息</li>
                        <li>保存任务</li>
                    </ol>

                    <h4 style="color: #667eea; margin-bottom: 15px; margin-top: 30px;">Excel批量导入</h4>
                    <ol style="padding-left: 20px; color: #7f8c8d;">
                        <li>点击"下载模板"获取Excel导入模板</li>
                        <li>按照模板格式填写任务信息</li>
                        <li>点击"从Excel导入"选择文件</li>
                        <li>系统会自动验证数据并导入</li>
                    </ol>

                    <div class="warning">
                        <strong>⚠️ 注意：</strong> Excel导入时，模型名称和人员名称为必填项，其他字段可选。如果存在相同的模型名称+人员名称组合，系统会更新现有任务。
                    </div>
                </div>

                <!-- 周报导出教程 -->
                <div class="tutorial" style="margin-top: 50px;">
                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #2c3e50;">
                        📊 周报导出
                    </h3>
                    
                    <p style="color: #7f8c8d; margin-bottom: 20px;">
                        系统支持自动生成HTML格式的周报，包含本周的测试进度、完成情况和统计分析。
                    </p>

                    <div class="steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <h4 class="step-title">点击导出按钮</h4>
                            <p class="step-description">在发布计划甘特图页面点击绿色的"导出周报"按钮</p>
                        </div>
                        
                        <div class="step">
                            <div class="step-number">2</div>
                            <h4 class="step-title">等待生成</h4>
                            <p class="step-description">系统会自动生成HTML格式的周报文件</p>
                        </div>
                        
                        <div class="step">
                            <div class="step-number">3</div>
                            <h4 class="step-title">查看报告</h4>
                            <p class="step-description">用浏览器打开下载的HTML文件查看周报</p>
                        </div>
                    </div>

                    <div class="info">
                        <strong>📈 周报内容包括：</strong>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>总体统计：任务数量、完成率、资料输出情况</li>
                            <li>按测试人员统计：每个人的工作量和完成情况</li>
                            <li>按GPU型号统计：不同GPU的使用情况</li>
                            <li>详细任务列表：所有任务的状态和进度</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 文件对比教程 -->
                <div class="tutorial" style="margin-top: 50px;">
                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #2c3e50;">
                        📁 文件对比
                    </h3>
                    
                    <p style="color: #7f8c8d; margin-bottom: 20px;">
                        系统支持本地和远程仓库的文件对比功能，帮助识别文件差异。
                    </p>

                    <div class="steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <h4 class="step-title">进入文件管理</h4>
                            <p class="step-description">在模型存储页面点击"文件树"卡片</p>
                        </div>
                        
                        <div class="step">
                            <div class="step-number">2</div>
                            <h4 class="step-title">开始文件对比</h4>
                            <p class="step-description">点击"开始文件对比"按钮，系统会打开文件对比页面</p>
                        </div>
                        
                        <div class="step">
                            <div class="step-number">3</div>
                            <h4 class="step-title">查看对比结果</h4>
                            <p class="step-description">在文件对比页面查看本地和远程文件的差异</p>
                        </div>
                    </div>

                    <div class="tip">
                        <strong>💡 提示：</strong> 文件对比功能支持查看新增、修改、删除和重命名的文件状态，帮助确保文件同步。
                    </div>
                </div>
            </section>

            <!-- 常见问题 -->
            <section id="faq" class="section">
                <h2 class="section-title">常见问题</h2>
                <p class="section-subtitle">解答使用过程中的常见问题</p>

                <div style="max-width: 800px; margin: 0 auto;">
                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何修改已创建的测试任务？</h4>
                        <p class="faq-answer">A: 在甘特图页面的任务列表中，点击对应任务行的"编辑"按钮，即可修改任务信息。修改后点击保存即可。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: Excel导入失败怎么办？</h4>
                        <p class="faq-answer">A: 请检查Excel文件格式是否正确，确保模型名称和人员名称不为空。如果仍有问题，请下载最新的导入模板重新填写。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 周报显示的时间范围是什么？</h4>
                        <p class="faq-answer">A: 周报显示的是当前周（周一到周日）的数据，包括在此时间范围内有活动的所有测试任务。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 服务器监控数据多久更新一次？</h4>
                        <p class="faq-answer">A: 服务器监控数据每30秒更新一次，网络流量数据需要至少2次采样才能计算出准确的速度。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何删除不需要的发布计划？</h4>
                        <p class="faq-answer">A: 在发布计划列表页面，点击对应计划行的"删除"按钮。注意：删除计划会同时删除该计划下的所有测试任务，请谨慎操作。</p>
                    </div>
                    
                    <div class="faq-item">
                        <h4 class="faq-question">Q: 文件对比功能支持哪些类型的文件？</h4>
                        <p class="faq-answer">A: 文件对比功能支持所有文本格式的文件，包括代码文件、配置文件、日志文件等。对于二进制文件，系统会显示文件基本信息但不提供内容对比。</p>
                    </div>
                    
                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何查看历史监控数据？</h4>
                        <p class="faq-answer">A: 在服务器监控页面，可以通过时间选择器查看历史数据。系统默认保留最近30天的监控数据，如有特殊需求请联系系统管理员。</p>
                    </div>
                    
                    <div class="faq-item">
                        <h4 class="faq-question">Q: 测试任务支持哪些状态？</h4>
                        <p class="faq-answer">A: 测试任务支持以下状态：待开始、进行中、已完成、已取消、阻塞中、已延期。可以根据实际测试进度更新任务状态。</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 底部 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 模型存储系统. 版权所有.</p>
            <p style="margin-top: 10px; opacity: 0.8;">
                如有问题请联系系统管理员 | 
                <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a>
            </p>
        </div>
    </footer>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航栏高亮
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('.nav');
            if (window.scrollY > 100) {
                nav.style.background = 'rgba(255, 255, 255, 0.95)';
                nav.style.backdropFilter = 'blur(10px)';
            } else {
                nav.style.background = 'white';
                nav.style.backdropFilter = 'none';
            }
        });
    </script>
</body>
</html>