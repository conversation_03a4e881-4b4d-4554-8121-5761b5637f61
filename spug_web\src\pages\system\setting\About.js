/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React from 'react';
import styles from './index.module.css';
import { Descriptions, Spin } from 'antd';
import { observer } from 'mobx-react'
import { http, VERSION } from 'libs';


@observer
class About extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      fetching: true,
      info: {}
    }
  }

  componentDidMount() {
    http.get('/api/setting/about/')
      .then(res => this.setState({info: res}))
      .finally(() => this.setState({fetching: false}))
  }

  render() {
    const {info, fetching} = this.state;
    return (
      <Spin spinning={fetching}>
        <div style={{marginBottom: 24}}>
          <span style={{fontSize: 24, fontWeight: 'bold'}}>H3C异构运维平台</span>
        </div>
        <div className={styles.title}>系统信息</div>
        <Descriptions column={1}>
          <Descriptions.Item label="当前版本">v{VERSION}</Descriptions.Item>
          <Descriptions.Item label="产品名称">H3C异构运维平台</Descriptions.Item>
          <Descriptions.Item label="开发商">H3C Technologies Co., Ltd.</Descriptions.Item>
          <Descriptions.Item label="技术支持">H3C内部技术团队</Descriptions.Item>
          <Descriptions.Item label="操作系统">{info['system_version']}</Descriptions.Item>
          <Descriptions.Item label="Python版本">{info['python_version']}</Descriptions.Item>
          <Descriptions.Item label="Django版本">{info['django_version']}</Descriptions.Item>
          <Descriptions.Item label="API版本">{info['spug_version']}</Descriptions.Item>
          <Descriptions.Item label="Web版本">{VERSION}</Descriptions.Item>
        </Descriptions>
      </Spin>
    )
  }
}

export default About
