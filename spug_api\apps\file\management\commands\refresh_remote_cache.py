# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.

from django.core.management.base import BaseCommand
from apps.file.redis_cache_service import cache_service

class Command(BaseCommand):
    help = '刷新远程文件夹缓存'

    def add_arguments(self, parser):
        parser.add_argument(
            '--folder-id',
            type=int,
            help='只刷新指定的远程文件夹ID'
        )
        parser.add_argument(
            '--path',
            type=str,
            default='',
            help='指定路径（配合--folder-id使用）'
        )
        parser.add_argument(
            '--clear-expired',
            action='store_true',
            help='清理过期缓存'
        )
        parser.add_argument(
            '--status',
            action='store_true',
            help='显示缓存状态'
        )

    def handle(self, *args, **options):
        self.stdout.write('🚀 远程文件夹缓存管理工具')
        
        if options['status']:
            self._show_status()
            return
        
        if options['clear_expired']:
            self._clear_expired()
            return
        
        if options['folder_id']:
            self._refresh_single_folder(options['folder_id'], options['path'])
        else:
            self._refresh_all_folders()

    def _show_status(self):
        """显示缓存状态"""
        self.stdout.write('\n📊 缓存状态统计:')
        status = cache_service.get_cache_status()
        
        for key, value in status.items():
            if key == 'cache_expire_minutes':
                self.stdout.write(f'   缓存过期时间: {value} 分钟')
            else:
                key_map = {
                    'total_caches': '总缓存数',
                    'valid_caches': '有效缓存',
                    'invalid_caches': '无效缓存',
                    'expired_caches': '过期缓存',
                    'fresh_caches': '新鲜缓存'
                }
                self.stdout.write(f'   {key_map.get(key, key)}: {value}')

    def _clear_expired(self):
        """清理过期缓存"""
        self.stdout.write('\n🗑️ 清理过期缓存...')
        count = cache_service.clear_expired_caches()
        
        if count > 0:
            self.stdout.write(
                self.style.SUCCESS(f'✅ 成功清理 {count} 条过期缓存')
            )
        else:
            self.stdout.write('ℹ️ 没有过期缓存需要清理')

    def _refresh_single_folder(self, folder_id, path):
        """刷新单个文件夹缓存"""
        self.stdout.write(f'\n🔄 刷新文件夹 {folder_id} 的缓存 (路径: {path or "根目录"})')
        
        success = cache_service.refresh_folder_cache(folder_id, path)
        
        if success:
            self.stdout.write(
                self.style.SUCCESS('✅ 缓存刷新成功')
            )
        else:
            self.stdout.write(
                self.style.ERROR('❌ 缓存刷新失败')
            )

    def _refresh_all_folders(self):
        """刷新所有文件夹缓存"""
        self.stdout.write('\n🔄 刷新所有远程文件夹缓存...')
        
        stats = cache_service.refresh_all_caches()
        
        self.stdout.write('\n📈 刷新统计:')
        self.stdout.write(f'   总文件夹数: {stats["total_folders"]}')
        self.stdout.write(f'   成功文件夹: {stats["success_folders"]}')
        self.stdout.write(f'   失败文件夹: {stats["failed_folders"]}')
        self.stdout.write(f'   总路径数: {stats["total_paths"]}')
        self.stdout.write(f'   成功路径: {stats["success_paths"]}')
        self.stdout.write(f'   失败路径: {stats["failed_paths"]}')
        
        if stats["failed_folders"] == 0 and stats["failed_paths"] == 0:
            self.stdout.write(
                self.style.SUCCESS('\n✅ 所有缓存刷新成功！')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'\n⚠️ 部分缓存刷新失败，请检查日志')
            ) 