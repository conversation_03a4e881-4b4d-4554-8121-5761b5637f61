/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React, { useState, useEffect } from 'react';
import { 
  Button, 
  Space, 
  message, 
  Tag, 
  Spin,
  Card,
  Breadcrumb,
  Typography
} from 'antd';
import { 
  SaveOutlined,
  ArrowLeftOutlined,
  EditOutlined
} from '@ant-design/icons';
import { http } from 'libs';
import AceEditor from 'react-ace';

// 导入Ace编辑器的语言模式和主题
import 'ace-builds/src-noconflict/mode-javascript';
import 'ace-builds/src-noconflict/mode-typescript';
import 'ace-builds/src-noconflict/mode-python';
import 'ace-builds/src-noconflict/mode-java';
import 'ace-builds/src-noconflict/mode-c_cpp';
import 'ace-builds/src-noconflict/mode-csharp';
import 'ace-builds/src-noconflict/mode-php';
import 'ace-builds/src-noconflict/mode-ruby';
import 'ace-builds/src-noconflict/mode-golang';
import 'ace-builds/src-noconflict/mode-rust';
import 'ace-builds/src-noconflict/mode-swift';
import 'ace-builds/src-noconflict/mode-kotlin';
import 'ace-builds/src-noconflict/mode-scala';
import 'ace-builds/src-noconflict/mode-sh';
import 'ace-builds/src-noconflict/mode-powershell';
import 'ace-builds/src-noconflict/mode-sql';
import 'ace-builds/src-noconflict/mode-html';
import 'ace-builds/src-noconflict/mode-xml';
import 'ace-builds/src-noconflict/mode-css';
import 'ace-builds/src-noconflict/mode-scss';
import 'ace-builds/src-noconflict/mode-less';
import 'ace-builds/src-noconflict/mode-json';
import 'ace-builds/src-noconflict/mode-yaml';
import 'ace-builds/src-noconflict/mode-toml';
import 'ace-builds/src-noconflict/mode-ini';
import 'ace-builds/src-noconflict/mode-markdown';
import 'ace-builds/src-noconflict/mode-dockerfile';
import 'ace-builds/src-noconflict/mode-makefile';
import 'ace-builds/src-noconflict/mode-r';
import 'ace-builds/src-noconflict/mode-matlab';
import 'ace-builds/src-noconflict/mode-lua';
import 'ace-builds/src-noconflict/mode-perl';
import 'ace-builds/src-noconflict/mode-text';

// 导入主题
import 'ace-builds/src-noconflict/theme-monokai';
import 'ace-builds/src-noconflict/theme-github';
import 'ace-builds/src-noconflict/theme-tomorrow_night';
import 'ace-builds/src-noconflict/theme-one_dark';

// 导入扩展功能
import 'ace-builds/src-noconflict/ext-language_tools';
import 'ace-builds/src-noconflict/ext-searchbox';

const { Title } = Typography;

function FileEditor() {
  const [fileContent, setFileContent] = useState('');
  const [loading, setLoading] = useState(true);
  const [saveLoading, setSaveLoading] = useState(false);
  const [editorLanguage, setEditorLanguage] = useState('text');
  const [fileName, setFileName] = useState('');
  const [filePath, setFilePath] = useState('');
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    // 从URL参数获取文件路径
    const urlParams = new URLSearchParams(window.location.search);
    const path = urlParams.get('path');
    if (path) {
      setFilePath(path);
      setFileName(path.split('/').pop());
      loadFile(path);
    } else {
      message.error('缺少文件路径参数');
      setLoading(false);
    }
  }, [loadFile]);

  // 获取文件编程语言 - 适配Ace Editor
  const getFileLanguage = (filename) => {
    const ext = filename.split('.').pop()?.toLowerCase();
    const languageMap = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'c': 'c_cpp',
      'cpp': 'c_cpp',
      'cxx': 'c_cpp',
      'cc': 'c_cpp',
      'h': 'c_cpp',
      'hpp': 'c_cpp',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'golang',
      'rs': 'rust',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'sh': 'sh',
      'bash': 'sh',
      'zsh': 'sh',
      'fish': 'sh',
      'ps1': 'powershell',
      'sql': 'sql',
      'html': 'html',
      'htm': 'html',
      'xml': 'xml',
      'css': 'css',
      'scss': 'scss',
      'sass': 'scss',
      'less': 'less',
      'json': 'json',
      'yaml': 'yaml',
      'yml': 'yaml',
      'toml': 'toml',
      'ini': 'ini',
      'cfg': 'ini',
      'conf': 'ini',
      'md': 'markdown',
      'markdown': 'markdown',
      'dockerfile': 'dockerfile',
      'makefile': 'makefile',
      'r': 'r',
      'matlab': 'matlab',
      'm': 'matlab',
      'lua': 'lua',
      'perl': 'perl',
      'pl': 'perl'
    };
    return languageMap[ext] || 'text';
  };

  const loadFile = async (path) => {
    setLoading(true);
    try {
      const response = await http.get('/api/file/manager/edit/', { params: { filename: path } });
      setFileContent(response.content || '');
      const language = getFileLanguage(path);
      setEditorLanguage(language);
    } catch (error) {
      message.error(error.response?.data?.error || '读取文件失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaveLoading(true);
    try {
      await http.post('/api/file/manager/edit/', {
        filename: filePath,
        content: fileContent
      });
      message.success('文件保存成功');
      setHasChanges(false);
    } catch (error) {
      message.error(error.response?.data?.error || '保存文件失败');
    } finally {
      setSaveLoading(false);
    }
  };

  const handleContentChange = (value) => {
    setFileContent(value);
    setHasChanges(true);
  };

  const handleBack = () => {
    if (hasChanges) {
      if (window.confirm('文件已修改但未保存，确定要离开吗？')) {
        window.history.back();
      }
    } else {
      window.history.back();
    }
  };

  const getBreadcrumbItems = () => {
    const pathParts = filePath.split('/').filter(Boolean);
    const items = [{ title: '文件管理' }];
    
    pathParts.forEach((part, index) => {
      if (index === pathParts.length - 1) {
        // 最后一项是文件名
        items.push({ title: part });
      } else {
        items.push({ title: part });
      }
    });
    
    return items;
  };

  if (loading) {
    return (
      <div style={{ 
        height: '100vh', 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center' 
      }}>
        <Spin size="large" tip="正在加载文件..." />
      </div>
    );
  }

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 头部工具栏 */}
      <Card 
        size="small" 
        style={{ borderRadius: 0, borderBottom: '1px solid #f0f0f0' }}
        bodyStyle={{ padding: '12px 24px' }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={handleBack}
              type="text"
            >
              返回
            </Button>
            <div>
              <Space align="center">
                <EditOutlined style={{ color: '#1890ff' }} />
                <Title level={4} style={{ margin: 0 }}>
                  {fileName}
                </Title>
                <Tag color="blue">{editorLanguage}</Tag>
                {hasChanges && <Tag color="orange">未保存</Tag>}
              </Space>
              <Breadcrumb 
                items={getBreadcrumbItems()}
                style={{ fontSize: '12px', marginTop: '4px' }}
              />
            </div>
          </div>
          
          <Space>
            <Button 
              type="primary" 
              icon={<SaveOutlined />}
              loading={saveLoading}
              onClick={handleSave}
              disabled={!hasChanges}
            >
              保存 (Ctrl+S)
            </Button>
          </Space>
        </div>
      </Card>

      {/* 编辑器区域 */}
      <div style={{ flex: 1, position: 'relative' }}>
        <AceEditor
          mode={editorLanguage}
          theme="monokai"
          name="file-editor"
          width="100%"
          height="100%"
          value={fileContent}
          onChange={handleContentChange}
          fontSize={14}
          showPrintMargin={true}
          showGutter={true}
          highlightActiveLine={true}
          setOptions={{
            enableBasicAutocompletion: true,
            enableLiveAutocompletion: true,
            enableSnippets: true,
            showLineNumbers: true,
            tabSize: 2,
            useWorker: false,
            wrap: true,
            autoScrollEditorIntoView: true,
            copyWithEmptySelection: true,
            dragEnabled: true,
            highlightSelectedWord: true,
            mergeUndoDeltas: true,
            navigateWithinSoftTabs: true,
            scrollPastEnd: 0.1,
            fixedWidthGutter: true
          }}
          commands={[
            {
              name: 'save',
              bindKey: { win: 'Ctrl-S', mac: 'Command-S' },
              exec: () => {
                handleSave();
              }
            }
          ]}
          onLoad={(editor) => {
            editor.focus();
            editor.renderer.setScrollMargin(10, 10, 10, 10);
          }}
          style={{
            fontFamily: 'Menlo, Monaco, "Courier New", monospace'
          }}
        />
      </div>
    </div>
  );
}

export default FileEditor; 