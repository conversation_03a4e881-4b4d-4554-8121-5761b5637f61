/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React from 'react';
import { Card, Descriptions, Tag, Space, Row, Col, Timeline, Alert, Button } from 'antd';
import { 
  ToolOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  DownloadOutlined,
  UploadOutlined,
  SettingOutlined
} from '@ant-design/icons';

function GpuDriverInfo({ data = {}, loading }) {
  console.log('[DEBUG GpuDriverInfo] 接收到的数据:', data);
  
  // 测试用模拟数据
  const mockData = {
    driver_version: '*********',
    xpu_rt_version: '10.2',
    firmware_version: 'PBL:1.0, PCIE:2.14, SBL:1.48',
    cpld_version: '5.2',
    is_latest: true,
    is_compatible: true
  };
  
  // 如果没有数据，使用模拟数据进行测试
  const effectiveData = data?.driver_version ? data : mockData;
  console.log('[DEBUG GpuDriverInfo] 使用的数据:', effectiveData);
  
  const getDriverStatus = () => {
    if (!effectiveData?.driver_version) {
      return { color: 'error', text: '驱动未安装', icon: <ExclamationCircleOutlined /> };
    }
    if (effectiveData.is_latest === false) {
      return { color: 'warning', text: '有新版本', icon: <InfoCircleOutlined /> };
    }
    if (effectiveData.is_compatible === false) {
      return { color: 'warning', text: '兼容性问题', icon: <ExclamationCircleOutlined /> };
    }
    return { color: 'success', text: '驱动正常', icon: <CheckCircleOutlined /> };
  };

  const formatDate = (dateString) => {
    if (!dateString) return '未知';
    return new Date(dateString).toLocaleDateString();
  };

  const driverStatus = getDriverStatus();

  return (
    <Card
      title={
        <Space>
          <ToolOutlined style={{ color: '#f59e0b' }} />
          <span>GPU驱动固件信息</span>
          <Tag className={`status-${driverStatus.color}`}>
            {driverStatus.icon} {driverStatus.text}
          </Tag>
        </Space>
      }
      extra={
        <Space>
          <Button size="small" icon={<UploadOutlined />}>更新驱动</Button>
          <Button size="small" icon={<DownloadOutlined />}>导出信息</Button>
        </Space>
      }
      loading={loading}
      style={{ height: '100%', minHeight: '400px' }}
    >
      <Row gutter={[16, 16]}>
        {/* 驱动状态告警 */}
        {driverStatus.color === 'error' && (
          <Col span={24}>
            <Alert
              message="驱动状态异常"
              description="GPU驱动未正确安装或存在兼容性问题，请检查驱动安装状态"
              type="error"
              showIcon
            />
          </Col>
        )}
        
        {driverStatus.color === 'warning' && (
          <Col span={24}>
            <Alert
              message="发现新版本驱动"
              description="建议更新到最新版本驱动以获得更好的性能和稳定性"
              type="warning"
              showIcon
            />
          </Col>
        )}

        {/* 驱动信息 */}
        <Col span={12}>
          <Card size="small" title="GPU驱动信息" style={{ height: '100%' }}>
            <Descriptions size="small" column={3}>
              <Descriptions.Item label="驱动版本">
                <Space>
                  {effectiveData?.driver_version || '未知'}
                  {effectiveData?.is_latest === false && (
                    <Tag color="orange">可更新</Tag>
                  )}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="XPU-RT版本">
                {effectiveData?.xpu_rt_version || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="安装路径">
                {effectiveData?.install_path || '/usr/local/xpu'}
              </Descriptions.Item>
              <Descriptions.Item label="兼容性">
                <Tag color={effectiveData?.is_compatible ? 'green' : 'red'}>
                  {effectiveData?.is_compatible ? '兼容' : '不兼容'}
                </Tag>
              </Descriptions.Item>
              {effectiveData?.latest_version && (
                <Descriptions.Item label="最新版本">
                  {effectiveData.latest_version}
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>
        </Col>

        {/* 固件信息 */}
        <Col span={12}>
          <Card
            title={
              <Space>
                <UploadOutlined />
                <span>固件信息</span>
              </Space>
            }
            size="small"
          >
            <Descriptions column={1} size="small">
              <Descriptions.Item label="固件版本">
                {effectiveData?.firmware_version || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="CPLD版本">
                {effectiveData?.cpld_version || '未知'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 硬件信息 */}
        <Col span={12}>
          <Card size="small" title="硬件信息" style={{ height: '100%' }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="产品名称">
                {data?.hardware_info?.product_name || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="产品品牌">
                {data?.hardware_info?.product_brand || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="产品架构">
                {data?.hardware_info?.product_architecture || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="OAM ID">
                {data?.hardware_info?.oam_id !== undefined ? data?.hardware_info?.oam_id : '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="设备ID">
                {data?.hardware_info?.device_id || '未知'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* PCIe信息 */}
        <Col span={12}>
          <Card size="small" title="PCIe信息" style={{ height: '100%' }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="PCIe总线">
                {data?.pcie_info?.bus || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="PCIe设备">
                {data?.pcie_info?.device || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="PCIe功能">
                {data?.pcie_info?.function || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="PCIe世代">
                <Tag color="blue">
                  {`当前: ${data?.pcie_info?.current_gen || '未知'}`}
                </Tag>
                <Tag color="green">
                  {`最大: ${data?.pcie_info?.max_gen || '未知'}`}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="链路宽度">
                <Tag color="blue">
                  {`当前: ${data?.pcie_info?.current_width || '未知'}`}
                </Tag>
                <Tag color="green">
                  {`最大: ${data?.pcie_info?.max_width || '未知'}`}
                </Tag>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 安装历史时间线 */}
        {data?.install_history && (
          <Col span={24}>
            <Card size="small" title="安装历史">
              <Timeline mode="left" style={{ marginTop: '16px' }}>
                {data.install_history.map((item, index) => (
                  <Timeline.Item
                    key={index}
                    color={item.status === 'success' ? 'green' : item.status === 'warning' ? 'orange' : 'red'}
                    label={formatDate(item.date)}
                  >
                    <div>
                      <strong>{item.action}</strong>
                      <br />
                      <span style={{ fontSize: '12px', opacity: 0.8 }}>
                        {item.version} - {item.description}
                      </span>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </Card>
          </Col>
        )}

        {/* 自定义检测命令结果 */}
        {data?.custom_commands && (
          <Col span={24}>
            <Card size="small" title="自定义检测结果">
              <Space direction="vertical" style={{ width: '100%' }}>
                {data.custom_commands.map((cmd, index) => (
                  <div key={index}>
                    <div style={{ fontSize: '12px', opacity: 0.8, marginBottom: '4px' }}>
                      命令: {cmd.command}
                    </div>
                    <pre style={{ 
                      background: 'rgba(255, 255, 255, 0.05)', 
                      padding: '8px', 
                      borderRadius: '4px',
                      fontSize: '12px',
                      maxHeight: '100px',
                      overflow: 'auto'
                    }}>
                      {cmd.output || '无输出'}
                    </pre>
                  </div>
                ))}
              </Space>
            </Card>
          </Col>
        )}
      </Row>
    </Card>
  );
}

export default GpuDriverInfo; 