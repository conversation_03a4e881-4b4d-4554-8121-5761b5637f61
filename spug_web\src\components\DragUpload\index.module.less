.dragUpload {
  position: relative;
  width: 100%;

  &.dragging {
    .dragger {
      border-color: #1890ff;
      background-color: #f0f8ff;
    }
  }

  .dragger {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    cursor: pointer;
    transition: border-color 0.3s, background-color 0.3s;

    &:hover {
      border-color: #1890ff;
    }

    &.uploading {
      border-color: #52c41a;
      background-color: #f6ffed;
      cursor: not-allowed;
    }
  }

  .dragOverlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(24, 144, 255, 0.1);
    border: 2px dashed #1890ff;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    pointer-events: none;

    .dragText {
      text-align: center;
      color: #1890ff;
      font-size: 16px;

      .anticon {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
      }

      p {
        margin: 0;
        font-weight: 500;
      }
    }
  }
}

// 文件管理界面的拖拽区域
.fileManagerDragArea {
  min-height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: #999;
  font-size: 14px;
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }

  &.active {
    border-color: #1890ff;
    background-color: #f0f8ff;
    color: #1890ff;
  }

  .icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  .text {
    margin: 0;
    line-height: 1.5;
  }
}

// 文件分发界面的拖拽区域
.transferDragArea {
  padding: 20px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  text-align: center;
  color: #999;
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }

  &.active {
    border-color: #1890ff;
    background-color: #f0f8ff;
    color: #1890ff;
  }

  .icon {
    font-size: 32px;
    margin-bottom: 8px;
    opacity: 0.6;
  }

  .text {
    margin: 0;
    font-size: 14px;
  }
} 