import React, { useState } from 'react';
import {
  Card,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Row,
  Col,
  message
} from 'antd';

const { TextArea } = Input;
const { Option } = Select;

export default function TemplateEditTest() {
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [form] = Form.useForm();

  // 测试数据
  const testTemplate = {
    id: 1,
    name: '测试模板',
    description: '这是一个测试模板',
    template_type: 'test_guide',
    status: 'active'
  };

  const handleEdit = () => {
    setEditingTemplate(testTemplate);
    form.setFieldsValue({
      name: testTemplate.name,
      description: testTemplate.description,
      template_type: testTemplate.template_type,
      status: testTemplate.status
    });
    setModalVisible(true);
  };

  const handleSubmit = (values) => {
    message.success('模板更新成功');
    setModalVisible(false);
    setEditingTemplate(null);
    form.resetFields();
  };

  return (
    <div style={{ padding: '20px' }}>
      <Card title="Modal测试页面">
        <div style={{ marginBottom: 16 }}>
          <p><strong>当前状态：</strong></p>
          <p>modalVisible: {modalVisible ? 'true' : 'false'}</p>
          <p>editingTemplate: {editingTemplate ? editingTemplate.name : 'null'}</p>
        </div>
        
        <Button type="primary" onClick={handleEdit}>
          测试编辑Modal
        </Button>
        
        <Button 
          style={{ marginLeft: 8 }} 
          onClick={() => setModalVisible(!modalVisible)}
        >
          直接切换Modal状态
        </Button>
      </Card>

      {/* 编辑Modal */}
      <Modal
        title="编辑模板测试"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingTemplate(null);
          form.resetFields();
        }}
        onOk={() => {
          form.submit();
        }}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="模板名称"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="请输入模板名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="模板描述"
          >
            <TextArea 
              rows={3} 
              placeholder="请输入模板描述" 
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="template_type"
                label="模板类型"
                rules={[{ required: true, message: '请选择模板类型' }]}
              >
                <Select placeholder="请选择模板类型">
                  <Option value="test_guide">测试指导</Option>
                  <Option value="report">测试报告</Option>
                  <Option value="manual">使用手册</Option>
                  <Option value="specification">技术规范</Option>
                  <Option value="imported">导入模板</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="active">启用</Option>
                  <Option value="inactive">禁用</Option>
                  <Option value="draft">草稿</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
}
