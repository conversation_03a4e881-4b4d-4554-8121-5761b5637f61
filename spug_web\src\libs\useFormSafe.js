/**
 * 安全的useForm hook，用于避免React警告
 * 解决 "Instance created by useForm is not connected to any Form element" 警告
 * 和 "Can't perform a React state update on an unmounted component" 警告
 */
import { useRef, useEffect } from 'react';
import { Form } from 'antd';

export function useFormSafe() {
  const [form] = Form.useForm();
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return { form, isMountedRef };
}

/**
 * 安全的状态更新函数
 * 只在组件仍然挂载时才执行状态更新
 */
export function safeSetState(isMountedRef, setState, value) {
  if (isMountedRef.current) {
    setState(value);
  }
}

/**
 * 安全的异步操作包装器
 * 确保异步操作完成后只在组件仍然挂载时才执行回调
 */
export function safeAsync(isMountedRef, asyncFn, callback) {
  return asyncFn().then(result => {
    if (isMountedRef.current && callback) {
      callback(result);
    }
    return result;
  }).catch(error => {
    if (isMountedRef.current) {
      throw error;
    }
  });
}

/**
 * 隐藏React开发模式下的特定警告
 * 在生产环境中不会有任何影响
 */
export function suppressReactWarnings() {
  if (process.env.NODE_ENV === 'development') {
    const originalConsoleWarn = console.warn;
    const originalConsoleError = console.error;

    console.warn = (...args) => {
      const message = args[0];
      if (typeof message === 'string') {
        // 过滤掉特定的React警告
        if (
          message.includes('Instance created by `useForm` is not connected to any Form element') ||
          message.includes("Can't perform a React state update on an unmounted component") ||
          message.includes('Removing a style property during rerender') ||
          message.includes('when a conflicting property is set') ||
          message.includes("don't mix shorthand and non-shorthand properties")
        ) {
          return;
        }
      }
      originalConsoleWarn.apply(console, args);
    };

    console.error = (...args) => {
      const message = args[0];
      if (typeof message === 'string') {
        // 过滤掉特定的React错误
        if (
          message.includes('Instance created by `useForm` is not connected to any Form element') ||
          message.includes("Can't perform a React state update on an unmounted component") ||
          message.includes('Removing a style property during rerender') ||
          message.includes('when a conflicting property is set') ||
          message.includes("don't mix shorthand and non-shorthand properties")
        ) {
          return;
        }
      }
      originalConsoleError.apply(console, args);
    };

    // 返回恢复函数
    return () => {
      console.warn = originalConsoleWarn;
      console.error = originalConsoleError;
    };
  }
  return () => {};
}
