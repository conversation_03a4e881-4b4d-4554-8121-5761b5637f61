# 智能日志分析功能

## 功能概述

智能日志分析是从智能结果收集器中提取出来的独立功能模块，专门用于远程主机日志文件的智能提取和性能分析。

## 主要特性

### 1. 远程主机支持
- 支持通过SSH连接远程主机
- 支持Docker容器内的日志文件访问
- 自动获取主机列表和容器信息

### 2. 多文件批量分析
- 支持同时选择多个日志文件进行分析
- 支持文件夹浏览和文件预览
- 智能文件过滤和选择

### 3. 智能性能指标提取
- 自动识别响应时间指标
- 提取吞吐量和错误率信息
- 计算P95、P99等关键性能指标

### 4. 可视化结果展示
- 统计概览卡片展示
- 详细结果表格
- 支持数据导出功能

## 使用方法

### 1. 访问页面
在系统导航菜单中，选择：
```
脚本配置中心 -> 日志分析
```

### 2. 配置日志获取参数
1. 点击"获取并分析日志"按钮
2. 选择目标主机
3. 配置Docker设置（如需要）
4. 选择日志文件路径
5. 预览和选择要分析的文件

### 3. 查看分析结果
- 查看统计概览（文件数、平均响应时间等）
- 浏览详细结果表格
- 导出分析结果

## 技术实现

### 前端组件结构
```
/pages/exec/log-analysis/
├── index.js                 # 主页面组件
├── index.module.less        # 主页面样式
├── LogResultsTable.js       # 结果表格组件
├── LogResultsTable.module.less # 结果表格样式
└── README.md               # 功能说明文档
```

### 复用组件
- `LogExtractionConfig`: 日志提取配置弹窗（复用自智能结果收集器）
- 主机选择器、文件浏览器等基础组件

### API接口
- `/api/exec/remote-log-fetch-multiple/`: 多文件日志提取接口
- `/api/exec/docker-containers/`: Docker容器列表接口
- `/api/exec/docker-filesystem/`: Docker文件系统浏览接口

## 权限配置

该功能使用 `exec.task.do` 权限，与批量执行任务权限一致。

## 路由配置

```javascript
{
  title: '日志分析', 
  auth: 'exec.task.do', 
  path: '/exec/log-analysis', 
  component: LogAnalysisPage
}
```

## 样式特点

- 采用现代化的渐变色头部设计
- 响应式布局，支持移动端访问
- 统一的卡片式设计风格
- 清晰的数据可视化展示

## 扩展性

该模块设计为独立功能，可以轻松扩展：
- 添加更多日志格式支持
- 集成更多性能分析算法
- 支持实时日志监控
- 添加报告生成功能
