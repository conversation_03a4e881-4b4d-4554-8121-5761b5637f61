/**
 * 配套管理
 */
import React from 'react';
import { observer } from 'mobx-react';
import { Table, Button, Modal, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { Action, TableCard, SearchForm } from 'components';
import ComForm from './Form';
import store from './store';

@observer
class PackageIndex extends React.Component {
  componentDidMount() {
    store.fetchRecords();
  }

  columns = [{
    title: '配套名称',
    dataIndex: 'name',
  }, {
    title: '配套标识',
    dataIndex: 'key',
  }, {
    title: '描述',
    dataIndex: 'desc',
    ellipsis: true
  }, {
    title: '排序',
    dataIndex: 'sort_id',
    width: 80,
  }, {
    title: '操作',
    width: 160,
    render: (text, record) => (
      <Action>
        <Action.Button onClick={() => store.showForm(record)}>编辑</Action.Button>
        <Action.Button onClick={() => this.handleDelete(record)}>删除</Action.Button>
      </Action>
    )
  }];

  handleDelete = (record) => {
    Modal.confirm({
      title: '删除确认',
      content: `确定要删除配套【${record.name}】吗？`,
      onOk: () => {
        return store.deleteRecord(record.id).then(() => {
          message.success('删除成功');
          store.fetchRecords();
        })
      }
    })
  };

  render() {
    return (
      <div>
        <TableCard
          title="配套管理"
          loading={store.loading}
          dataSource={store.dataSource}
          onReload={store.fetchRecords}
          actions={[
            <Button type="primary" icon={<PlusOutlined/>} onClick={() => store.showForm()}>新增</Button>
          ]}
          pagination={{
            showSizeChanger: true,
            showLessItems: true,
            showTotal: total => `共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100']
          }}
          columns={this.columns}
          rowKey="id"/>
        {store.formVisible && <ComForm/>}
      </div>
    )
  }
}

export default PackageIndex 