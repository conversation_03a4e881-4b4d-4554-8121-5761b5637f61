import React, { useState, useEffect } from 'react';
import { Card, Button, Table, Space, Modal, Form, Input, DatePicker, message, Tag, Spin } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, BarChartOutlined } from '@ant-design/icons';
import { http } from 'libs';
import moment from 'moment';

const { RangePicker } = DatePicker;

export default function ReleasePlan() {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [editingPlan, setEditingPlan] = useState(null);
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = () => {
    setLoading(true);
    http.get('/api/model-storage/new-release-plans/')
      .then(res => {
        const plansData = res.map(plan => ({
          ...plan,
          timeRange: [moment(plan.start_date), moment(plan.end_date)]
        }));
        setPlans(plansData);
      })
      .catch(err => {
        message.error('获取发布计划失败：' + err.message);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleShowModal = (plan) => {
    setEditingPlan(plan);
    if (plan) {
      form.setFieldsValue({
        ...plan,
        timeRange: plan.timeRange
      });
    } else {
      form.resetFields();
    }
    setVisible(true);
  };

  const handleOk = () => {
    form.validateFields().then(values => {
      const data = {
        name: values.name,
        description: values.description || '',
        start_date: values.timeRange[0].format('YYYY-MM-DD'),
        end_date: values.timeRange[1].format('YYYY-MM-DD'),
      };

      if (editingPlan) {
        // Edit
        http.put(`/api/model-storage/new-release-plans/${editingPlan.id}/`, data)
          .then(() => {
        message.success('更新成功');
            fetchPlans();
            setVisible(false);
          })
          .catch(err => {
            message.error('更新失败：' + err.message);
          });
      } else {
        // Add
        http.post('/api/model-storage/new-release-plans/', data)
          .then(() => {
        message.success('创建成功');
            fetchPlans();
      setVisible(false);
          })
          .catch(err => {
            message.error('创建失败：' + err.message);
          });
      }
    });
  };

  const handleDelete = (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个发布计划吗？这将同时删除相关的测试任务。',
      onOk: () => {
        http.delete(`/api/model-storage/new-release-plans/${id}/`)
          .then(() => {
    message.success('删除成功');
            fetchPlans();
          })
          .catch(err => {
            message.error('删除失败：' + err.message);
          });
      }
    });
  };

  const columns = [
    {
      title: '计划名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status_display',
      key: 'status',
      render: status => {
        let color = 'geekblue';
        if (status === '已完成') {
          color = 'green';
        } else if (status === '进行中') {
          color = 'volcano';
        } else if (status === '计划中') {
          color = 'blue';
        } else if (status === '已取消') {
          color = 'default';
        }
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: '起止时间',
      dataIndex: 'timeRange',
      key: 'timeRange',
      render: (timeRange) => `${timeRange[0].format('YYYY-MM-DD')} ~ ${timeRange[1].format('YYYY-MM-DD')}`
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      key: 'created_by',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => moment(text).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<BarChartOutlined />}
            onClick={() => window.open(`/model-storage/release-plan-gantt/${record.id}`, '_blank')}
          >
            甘特图
          </Button>
          <Button type="link" icon={<EditOutlined />} onClick={() => handleShowModal(record)}>编辑</Button>
          <Button type="link" danger icon={<DeleteOutlined />} onClick={() => handleDelete(record.id)}>删除</Button>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={() => handleShowModal(null)}>
          新建发布计划
        </Button>
      </div>
      <Table 
        columns={columns} 
        dataSource={plans} 
        rowKey="id" 
        loading={loading}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
      />
      <Modal
        title={editingPlan ? '编辑发布计划' : '新建发布计划'}
        visible={visible}
        onOk={handleOk}
        onCancel={() => setVisible(false)}
        destroyOnClose
      >
        <Form form={form} layout="vertical" name="releasePlanForm">
          <Form.Item
            name="name"
            label="计划名称"
            rules={[{ required: true, message: '请输入计划名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="timeRange"
            label="起止时间"
            rules={[{ required: true, message: '请选择起止时间' }]}
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={4} />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
} 