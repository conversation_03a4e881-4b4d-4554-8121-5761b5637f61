.extractionLayout {
  height: 100vh;
  background: #f5f5f5;
  
  .header {
    background: #fff;
    padding: 0 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .headerContent {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 64px;
      
      .headerLeft {
        display: flex;
        align-items: center;
        
        .title {
          font-size: 18px;
          font-weight: 600;
          color: #262626;
        }
      }
      
      .headerRight {
        .progressInfo {
          font-size: 12px;
          color: #666;
          margin-right: 8px;
        }
      }
    }
  }
  
  .mainContent {
    padding: 16px;
    margin-right: 400px;
    height: calc(100vh - 64px);
    overflow: hidden;
    
    .logCard {
      height: 100%;
      display: flex;
      flex-direction: column;
      
      :global(.ant-card-body) {
        padding: 0;
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }
    }
  }
  
  .sidebar {
    background: #fff;
    border-left: 1px solid #e8e8e8;
    height: calc(100vh - 64px);
    overflow: visible;
    display: flex;
    flex-direction: column;
    padding: 16px;
  }
  
  .loadingContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #666;
  }
} 