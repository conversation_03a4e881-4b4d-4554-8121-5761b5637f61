/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React from 'react';
import { Layout } from 'antd';
import { CopyrightOutlined } from '@ant-design/icons';
import styles from './layout.module.less';


export default function () {
  return (
    <Layout.Footer style={{padding: 0}}>
      <div className={styles.footer}>
        <div className={styles.links}>
          <a className={styles.item} title="内部文档" href="#" target="_blank"
             rel="noopener noreferrer">内部文档</a>
          <a className={styles.item} title="技术支持" href="#" target="_blank"
             rel="noopener noreferrer">技术支持</a>
          <a title="帮助中心" href="#" target="_blank"
             rel="noopener noreferrer">帮助中心</a>
        </div>
        <div style={{color: 'rgba(0, 0, 0, .45)'}}>
          Copyright <CopyrightOutlined/> {new Date().getFullYear()} H3C Technologies Co., Ltd.
        </div>
      </div>
    </Layout.Footer>
  )
}
