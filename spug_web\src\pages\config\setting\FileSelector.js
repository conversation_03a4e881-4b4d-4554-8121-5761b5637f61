/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React, { useState, useEffect } from 'react';
import { Modal, Tree, message, Spin } from 'antd';
import { FolderOutlined, FileOutlined } from '@ant-design/icons';
import http from 'libs/http';

export default function FileSelector({ visible, onSelect: onFileSelect, onCancel }) {
  const [treeData, setTreeData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [checkedKeys, setCheckedKeys] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);

  useEffect(() => {
    if (visible) {
      loadRootFiles();
      setCheckedKeys([]); // 重置选择
    }
  }, [visible]);

  const loadRootFiles = () => {
    setLoading(true);
    http.get('/api/file/manager/', { params: { path: '' } })
      .then(res => {
        // 适配现有API格式：从res.files中分离文件夹和文件
        const files = res.files || [];
        const data = buildTreeData(files, '');
        setTreeData(data);
        setExpandedKeys(['']);
      })
      .catch(() => message.error('加载文件列表失败'))
      .finally(() => setLoading(false));
  };

  const buildTreeData = (items, parentPath) => {
    return items.map(item => {
      const fullPath = parentPath === '' ? item.name : `${parentPath}/${item.name}`;
      return {
        title: item.name,
        key: fullPath,
        isLeaf: item.is_file,
        icon: item.is_file ? <FileOutlined /> : <FolderOutlined />,
        children: item.is_file ? undefined : [],
        disableCheckbox: !item.is_file, // 只允许选择文件，不允许选择文件夹
        ...item
      };
    });
  };

  const onLoadData = ({ key, children }) => {
    return new Promise((resolve) => {
      if (children && children.length > 0) {
        resolve();
        return;
      }

      http.get('/api/file/manager/', { params: { path: key } })
        .then(res => {
          // 适配现有API格式：从res.files中分离文件夹和文件
          const files = res.files || [];
          const newData = buildTreeData(files, key);
          setTreeData(prevData => updateTreeData(prevData, key, newData));
          resolve();
        })
        .catch(() => {
          message.error('加载目录失败');
          resolve();
        });
    });
  };

  const updateTreeData = (data, key, children) => {
    return data.map(node => {
      if (node.key === key) {
        return { ...node, children };
      }
      if (node.children) {
        return { ...node, children: updateTreeData(node.children, key, children) };
      }
      return node;
    });
  };

  const onCheck = (checkedKeys, info) => {
    // 只保留文件的选择，过滤掉文件夹
    const fileKeys = checkedKeys.filter(key => {
      return findNodeByKey(treeData, key)?.isLeaf;
    });
    setCheckedKeys(fileKeys);
  };

  const findNodeByKey = (data, key) => {
    for (let node of data) {
      if (node.key === key) {
        return node;
      }
      if (node.children) {
        const found = findNodeByKey(node.children, key);
        if (found) return found;
      }
    }
    return null;
  };

  const onExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
  };

  const handleOk = () => {
    if (checkedKeys.length === 0) {
      message.warning('请选择至少一个文件');
      return;
    }
    onFileSelect(checkedKeys);
  };

  return (
    <Modal
      title="选择配套文件"
      visible={visible}
      onOk={handleOk}
      onCancel={onCancel}
      width={600}
      okText="确定"
      cancelText="取消"
      okButtonProps={{ disabled: checkedKeys.length === 0 }}
    >
      <div style={{ height: 400, overflow: 'auto' }}>
        <Spin spinning={loading}>
          <Tree
            checkable
            showIcon
            loadData={onLoadData}
            treeData={treeData}
            onCheck={onCheck}
            onExpand={onExpand}
            expandedKeys={expandedKeys}
            checkedKeys={checkedKeys}
          />
        </Spin>
      </div>
      {checkedKeys.length > 0 && (
        <div style={{ marginTop: 10, color: '#1890ff' }}>
          已选择 {checkedKeys.length} 个文件: {checkedKeys.join(', ')}
        </div>
      )}
    </Modal>
  );
} 