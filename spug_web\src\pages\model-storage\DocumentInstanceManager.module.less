/* 文档实例管理页面样式 */
.container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.headerCard {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  
  :global(.ant-card-body) {
    padding: 24px;
  }
  
  :global(.ant-statistic-title) {
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
  }
  
  :global(.ant-statistic-content) {
    font-size: 24px;
    font-weight: 600;
  }
}

:global(.ant-card) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: none;
  
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      padding: 16px 0;
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .ant-card-body {
    padding: 24px;
  }
}

:global(.ant-table) {
  .ant-table-thead > tr > th {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 600;
    color: #262626;
  }
  
  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #f5f5f5;
    padding: 12px 16px;
  }
  
  .ant-table-tbody > tr:hover > td {
    background: #f5f7fa;
  }
}

:global(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;
  
  &.ant-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.4);
    
    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      box-shadow: 0 4px 8px rgba(102, 126, 234, 0.6);
      transform: translateY(-1px);
    }
  }
  
  &.ant-btn-text {
    &:hover {
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
    }
    
    &.ant-btn-dangerous:hover {
      background: rgba(255, 77, 79, 0.1);
      color: #ff4d4f;
    }
  }
}

:global(.ant-tag) {
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  border: none;
  
  &.ant-tag-blue {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1976d2;
  }
  
  &.ant-tag-green {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #388e3c;
  }
  
  &.ant-tag-orange {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    color: #f57c00;
  }
  
  &.ant-tag-gray {
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
    color: #666;
  }
}

:global(.ant-input),
:global(.ant-select-selector),
:global(.ant-picker) {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  
  &:hover {
    border-color: #667eea;
  }
  
  &:focus,
  &.ant-input-focused,
  &.ant-select-focused .ant-select-selector,
  &.ant-picker-focused {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  }
}

:global(.ant-select-dropdown) {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border: none;
}

:global(.ant-picker-dropdown) {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

:global(.ant-tooltip) {
  .ant-tooltip-inner {
    background: rgba(0, 0, 0, 0.85);
    border-radius: 6px;
  }
}

:global(.ant-popconfirm) {
  .ant-popover-inner {
    border-radius: 8px;
  }
  
  .ant-popover-buttons {
    .ant-btn {
      margin-left: 8px;
      
      &.ant-btn-primary {
        background: #ff4d4f;
        border-color: #ff4d4f;
        
        &:hover {
          background: #ff7875;
          border-color: #ff7875;
        }
      }
    }
  }
}

/* 文件大小显示样式 */
.fileSize {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #8c8c8c;
}

/* 版本号样式 */
.version {
  background: linear-gradient(135deg, #f0f0f0 0%, #e8e8e8 100%);
  color: #666;
  padding: 1px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
}

/* 状态指示器 */
.statusIndicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  
  &::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
  }
  
  &.draft::before {
    background: #d48806;
  }
  
  &.generated::before {
    background: #1890ff;
  }
  
  &.exported::before {
    background: #3f8600;
  }
  
  &.archived::before {
    background: #8c8c8c;
  }
}

/* 筛选器样式 */
.filters {
  background: #fafafa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .headerCard {
    margin-bottom: 16px;
    
    :global(.ant-card-body) {
      padding: 16px;
    }
    
    :global(.ant-row) {
      .ant-col {
        margin-bottom: 16px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  
  :global(.ant-card-body) {
    padding: 16px;
  }
  
  :global(.ant-table) {
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      padding: 8px;
      font-size: 12px;
    }
  }
  
  .filters {
    :global(.ant-row) {
      .ant-col {
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

/* 动画效果 */
:global(.ant-table-tbody > tr) {
  transition: all 0.3s ease;
}

:global(.ant-btn) {
  transition: all 0.3s ease;
}

:global(.ant-card) {
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
}

/* 自定义滚动条 */
:global(.ant-table-body) {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

/* 加载状态样式 */
:global(.ant-spin-container) {
  min-height: 200px;
}

/* 空状态样式 */
:global(.ant-empty) {
  padding: 60px 0;
  
  .ant-empty-image {
    height: 80px;
    margin-bottom: 16px;
  }
  
  .ant-empty-description {
    color: #8c8c8c;
    font-size: 14px;
  }
}
