/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React from 'react';
import { observer } from 'mobx-react';
import { Button, Modal, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { Action, TableCard, AuthDiv, Breadcrumb } from 'components';
import http from 'libs/http';
import store from './store';
import ComForm from './Form';

@observer
class ComTable extends React.Component {
  componentDidMount() {
    store.fetchRecords()
  }

  handleDelete = (text) => {
    Modal.confirm({
      title: '删除确认',
      content: `确定要删除【${text['name']}】?`,
      onOk: () => {
        return http.delete('/api/config/package/', {params: {id: text.id}})
          .then(() => {
            message.success('删除成功');
            store.fetchRecords()
          })
      }
    })
  };

  render() {
    const columns = [{
      title: '序号',
      key: 'series',
      render: (_, __, index) => index + 1,
      width: 80,
    }, {
      title: '配套名称',
      dataIndex: 'name',
    }, {
      title: '标识符',
      dataIndex: 'key',
    }, {
      title: '描述信息',
      dataIndex: 'desc',
    }, {
      title: '创建时间',
      dataIndex: 'created_at',
    }, {
      title: '操作',
      render: info => (
        <Action>
          <Action.Button auth="config.package.edit" onClick={() => store.showForm(info)}>编辑</Action.Button>
          <Action.Button auth="config.package.del" onClick={() => this.handleDelete(info)}>删除</Action.Button>
        </Action>
      ),
      width: 180,
    }];
    return (
      <AuthDiv auth="config.package.view">
        <Breadcrumb>
          <Breadcrumb.Item>配置中心</Breadcrumb.Item>
          <Breadcrumb.Item>配套管理</Breadcrumb.Item>
        </Breadcrumb>
        <TableCard
          title="配套列表"
          rowKey="id"
          loading={store.isFetching}
          dataSource={store.records}
          onReload={store.fetchRecords}
          actions={[
            <Button
              auth="config.package.add"
              type="primary"
              icon={<PlusOutlined/>}
              onClick={() => store.showForm()}>
              新建
            </Button>
          ]}
          columns={columns}/>
        {store.formVisible && <ComForm/>}
      </AuthDiv>
    )
  }
}

export default ComTable
