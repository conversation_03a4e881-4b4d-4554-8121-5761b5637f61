# Generated by Django 2.2.28 on 2025-07-23 10:48

from django.db import migrations, models
import django.db.models.deletion
import libs.mixins


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0012_add_task_step_completion'),
    ]

    operations = [
        migrations.CreateModel(
            name='WordTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128, verbose_name='模板名称')),
                ('description', models.TextField(blank=True, default='', verbose_name='模板描述')),
                ('template_file', models.CharField(max_length=512, verbose_name='模板文件路径')),
                ('template_type', models.CharField(default='test_guide', max_length=64, verbose_name='模板类型')),
                ('status', models.CharField(choices=[('active', '启用'), ('inactive', '禁用'), ('draft', '草稿')], default='active', max_length=32, verbose_name='状态')),
                ('variables_config', models.TextField(default='{}', help_text='存储模板中的变量定义和默认值', verbose_name='变量配置JSON')),
                ('usage_count', models.IntegerField(default=0, verbose_name='使用次数')),
                ('last_used_at', models.DateTimeField(blank=True, null=True, verbose_name='最后使用时间')),
                ('created_by', models.CharField(default='', max_length=64, verbose_name='创建者')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': 'Word模板',
                'verbose_name_plural': 'Word模板',
                'db_table': 'model_storage_word_templates',
                'ordering': ('-created_at',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='DocumentInstance',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=256, verbose_name='文档标题')),
                ('variables_values', models.TextField(default='{}', help_text='存储用户输入的变量值', verbose_name='变量值JSON')),
                ('rich_content', models.TextField(blank=True, default='', help_text='用户编辑的富文本内容', verbose_name='富文本内容')),
                ('generated_file_path', models.CharField(blank=True, default='', max_length=512, verbose_name='生成文件路径')),
                ('file_size', models.BigIntegerField(default=0, verbose_name='文件大小(字节)')),
                ('status', models.CharField(choices=[('draft', '草稿'), ('generated', '已生成'), ('exported', '已导出'), ('archived', '已归档')], default='draft', max_length=32, verbose_name='状态')),
                ('version', models.CharField(default='1.0', max_length=32, verbose_name='版本号')),
                ('created_by', models.CharField(default='', max_length=64, verbose_name='创建者')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('export_count', models.IntegerField(default=0, verbose_name='导出次数')),
                ('last_exported_at', models.DateTimeField(blank=True, null=True, verbose_name='最后导出时间')),
                ('parent_document', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='model_storage.DocumentInstance', verbose_name='父文档')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='model_storage.WordTemplate', verbose_name='关联模板')),
            ],
            options={
                'verbose_name': '文档实例',
                'verbose_name_plural': '文档实例',
                'db_table': 'model_storage_document_instances',
                'ordering': ('-created_at',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='TemplateVariable',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('variable_name', models.CharField(max_length=128, verbose_name='变量名')),
                ('display_name', models.CharField(max_length=128, verbose_name='显示名称')),
                ('variable_type', models.CharField(choices=[('text', '文本'), ('select', '选择'), ('multiselect', '多选'), ('date', '日期'), ('number', '数字'), ('textarea', '多行文本'), ('gpu_model', 'GPU型号'), ('vendor', '厂商')], default='text', max_length=32, verbose_name='变量类型')),
                ('default_value', models.TextField(blank=True, default='', verbose_name='默认值')),
                ('options', models.TextField(blank=True, default='[]', help_text='用于select类型的选项', verbose_name='选项配置JSON')),
                ('is_required', models.BooleanField(default=True, verbose_name='是否必填')),
                ('validation_rule', models.CharField(blank=True, default='', max_length=256, verbose_name='验证规则')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('group_name', models.CharField(blank=True, default='', max_length=64, verbose_name='分组名称')),
                ('help_text', models.TextField(blank=True, default='', verbose_name='帮助文本')),
                ('placeholder', models.CharField(blank=True, default='', max_length=128, verbose_name='占位符')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='model_storage.WordTemplate', verbose_name='关联模板')),
            ],
            options={
                'verbose_name': '模板变量',
                'verbose_name_plural': '模板变量',
                'db_table': 'model_storage_template_variables',
                'ordering': ('sort_order', 'id'),
                'unique_together': {('template', 'variable_name')},
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
