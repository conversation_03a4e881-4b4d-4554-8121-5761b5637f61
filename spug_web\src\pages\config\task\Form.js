/**
 * 任务管理表单
 */
import React, { useState } from 'react';
import { observer } from 'mobx-react';
import { Modal, Form, Input, InputNumber, message } from 'antd';
import http from 'libs/http';
import store from './store';

export default observer(function ComForm() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = () => {
    setLoading(true);
    const formData = form.getFieldsValue();
    formData['id'] = store.record.id;
    http.post('/api/config/task/', formData)
      .then(res => {
        message.success('操作成功');
        store.formVisible = false;
        store.fetchRecords()
      })
      .catch(() => setLoading(false));
  };

  const info = store.record;
  return (
    <Modal
      visible
      width={800}
      maskClosable={false}
      title={store.record.id ? '编辑任务' : '新增任务'}
      onCancel={() => store.formVisible = false}
      confirmLoading={loading}
      onOk={handleSubmit}>
      <Form 
        form={form} 
        labelCol={{span: 6}} 
        wrapperCol={{span: 14}}
        initialValues={info}>
        <Form.Item 
          name="name"
          label="任务名称"
          rules={[{ required: true, message: '请输入任务名称' }]}>
          <Input placeholder="请输入任务名称"/>
        </Form.Item>
        <Form.Item 
          name="key"
          label="任务标识"
          rules={[{ required: true, message: '请输入任务标识' }]}>
          <Input placeholder="请输入任务标识，用于API调用"/>
        </Form.Item>
        <Form.Item 
          name="desc"
          label="描述信息">
          <Input.TextArea rows={3} placeholder="请输入描述信息"/>
        </Form.Item>
      </Form>
    </Modal>
  )
}) 