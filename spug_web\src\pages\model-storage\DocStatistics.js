import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Table,
  Progress,
  Row,
  Col,
  Typography,
  Button,
  Space,
  Tag,
  Alert,
  Breadcrumb,
  Spin,
  Statistic,
  message,
  Skeleton
} from 'antd';
import {
  BarChartOutlined,
  ReloadOutlined,
  HomeOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  FolderOutlined,
  RiseOutlined,
  SyncOutlined,
  ClearOutlined
} from '@ant-design/icons';
import { http } from '../../libs';

const { Title, Text } = Typography;

export default function DocStatistics() {
  const [loading, setLoading] = useState(false);
  const [statsData, setStatsData] = useState(null);
  const [error, setError] = useState(null);

  // 用于跟踪组件是否已卸载
  const isMountedRef = useRef(true);

  const fetchDocStatistics = async (forceRefresh = false, clearCache = false) => {
    if (!isMountedRef.current) return;
    setLoading(true);
    setError(null);
    try {
      let url = '/api/model-storage/doc-statistics/';
      const params = [];
      if (forceRefresh) params.push('force_refresh=true');
      if (clearCache) params.push('clear_cache=true');
      if (params.length > 0) url += '?' + params.join('&');

      const response = await http.get(url);

      if (isMountedRef.current) {
        if (response && (response.vendor_stats || response.data)) {
          const data = response.data || response;
          setStatsData(data);

          // 显示不同的成功消息
          if (response.from_cache) {
            message.success('文档统计数据获取成功！（来自缓存）');
          } else {
            message.success('文档统计数据获取成功！（实时数据）');
          }
        } else {
          setError('数据格式错误');
          message.error('获取文档统计失败：数据格式错误');
        }
      }
    } catch (error) {
      if (isMountedRef.current) {
        setError(error.message || '网络错误');
        message.error('获取文档统计失败：' + (error.message || '网络错误'));
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  };

  const refreshDataAsync = async () => {
    if (!isMountedRef.current) return;
    try {
      message.loading('正在启动后台数据刷新...', 2);
      await http.post('/api/model-storage/doc-statistics/');
      if (isMountedRef.current) {
        message.success('已启动后台数据刷新，请稍后点击刷新按钮获取最新数据');
      }
    } catch (error) {
      if (isMountedRef.current) {
        message.error('启动后台刷新失败：' + (error.message || '网络错误'));
      }
    }
  };

  const clearCacheAndRefresh = async () => {
    if (!isMountedRef.current) return;
    try {
      message.loading('正在清除缓存并重新获取数据...', 1);
      await fetchDocStatistics(true, true); // 清除缓存并强制刷新
    } catch (error) {
      if (isMountedRef.current) {
        message.error('清除缓存失败：' + (error.message || '网络错误'));
      }
    }
  };

  useEffect(() => {
    fetchDocStatistics();

    // 清理函数
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // 渲染骨架屏
  const renderSkeleton = () => (
    <>
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        {[1, 2, 3, 4].map(i => (
          <Col span={6} key={i}>
            <Card>
              <Skeleton active paragraph={{ rows: 2 }} />
            </Card>
          </Col>
        ))}
      </Row>
      <Card style={{ marginBottom: 24 }}>
        <Skeleton active paragraph={{ rows: 8 }} />
      </Card>
      <Card>
        <Skeleton active paragraph={{ rows: 6 }} />
      </Card>
    </>
  );

  // 渲染总览卡片
  const renderOverviewCards = () => {
    if (!statsData) return null;

    const { total_stats, rates } = statsData;

    return (
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="大模型总数"
              value={total_stats.models}
              prefix={<FolderOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="版本目录总数"
              value={total_stats.versions}
              prefix={<FileTextOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="文档覆盖率"
              value={rates.doc_rate}
              suffix="%"
              prefix={<RiseOutlined style={{ color: rates.doc_rate >= 80 ? '#52c41a' : '#faad14' }} />}
              valueStyle={{ color: rates.doc_rate >= 80 ? '#52c41a' : '#faad14' }}
            />
            <Progress 
              percent={rates.doc_rate} 
              size="small" 
              strokeColor={rates.doc_rate >= 80 ? '#52c41a' : '#faad14'}
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="缺失文档模型"
              value={total_stats.incomplete}
              prefix={<ExclamationCircleOutlined style={{ color: '#f5222d' }} />}
              valueStyle={{ color: total_stats.incomplete > 0 ? '#f5222d' : '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染详细统计表格
  const renderDetailTable = () => {
    if (!statsData) return null;

    const { vendor_stats, total_stats, rates } = statsData;

    const dataSource = Object.entries(vendor_stats).map(([vendor, stats]) => ({
      key: vendor,
      vendor: vendor,
      models: stats.models,
      versions: stats.versions,
      docs: stats.docs,
      words: stats.words,
      pdfs: stats.pdfs,
      docFiles: stats.words + stats.pdfs,
      docRate: stats.versions > 0 ? ((stats.docs / stats.versions) * 100).toFixed(1) : '0',
      complete: stats.complete,
      incomplete: stats.incomplete,
      missingModels: stats.missing_models || []
    }));

    // 添加总计行
    dataSource.push({
      key: 'total',
      vendor: '总计',
      models: total_stats.models,
      versions: total_stats.versions,
      docs: total_stats.docs,
      words: total_stats.words,
      pdfs: total_stats.pdfs,
      docFiles: total_stats.words + total_stats.pdfs,
      docRate: rates.doc_rate,
      complete: total_stats.complete,
      incomplete: total_stats.incomplete,
      missingModels: []
    });

    const columns = [
      {
        title: '厂商',
        dataIndex: 'vendor',
        key: 'vendor',
        width: 120,
        fixed: 'left',
        render: (text, record) => (
          <span style={{ 
            fontWeight: record.key === 'total' ? 'bold' : 'normal',
            fontSize: record.key === 'total' ? '16px' : '14px'
          }}>
            {text}
          </span>
        )
      },
      {
        title: '大模型数量',
        dataIndex: 'models',
        key: 'models',
        width: 120,
        align: 'center',
        render: (text, record) => (
          <span style={{ fontWeight: record.key === 'total' ? 'bold' : 'normal' }}>
            {text}
          </span>
        )
      },
      {
        title: '版本目录数',
        dataIndex: 'versions',
        key: 'versions',
        width: 120,
        align: 'center',
        render: (text, record) => (
          <span style={{ fontWeight: record.key === 'total' ? 'bold' : 'normal' }}>
            {text}
          </span>
        )
      },
      {
        title: 'doc文件夹数',
        dataIndex: 'docs',
        key: 'docs',
        width: 130,
        align: 'center',
        render: (text, record) => (
          <span style={{ 
            color: text === record.versions ? '#52c41a' : (text > 0 ? '#faad14' : '#f5222d'),
            fontWeight: record.key === 'total' ? 'bold' : 'normal'
          }}>
            {text}
          </span>
        )
      },
      {
        title: 'Word文档数',
        dataIndex: 'words',
        key: 'words',
        width: 120,
        align: 'center',
        render: (text, record) => (
          <span style={{ 
            color: text > 0 ? '#52c41a' : '#f5222d',
            fontWeight: record.key === 'total' ? 'bold' : 'normal'
          }}>
            {text}
          </span>
        )
      },
      {
        title: 'PDF文档数',
        dataIndex: 'pdfs',
        key: 'pdfs',
        width: 120,
        align: 'center',
        render: (text, record) => (
          <span style={{ 
            color: text > 0 ? '#52c41a' : '#f5222d',
            fontWeight: record.key === 'total' ? 'bold' : 'normal'
          }}>
            {text}
          </span>
        )
      },
      {
        title: 'doc覆盖率',
        dataIndex: 'docRate',
        key: 'docRate',
        width: 130,
        align: 'center',
        render: (text, record) => {
          const rate = parseFloat(text);
          const color = rate === 100 ? '#52c41a' : rate >= 80 ? '#faad14' : '#f5222d';
          return (
            <div>
              <span style={{ 
                color, 
                fontWeight: record.key === 'total' ? 'bold' : '500',
                fontSize: record.key === 'total' ? '16px' : '14px'
              }}>
                {text}%
              </span>
              {record.key !== 'total' && (
                <Progress 
                  percent={rate} 
                  size="small" 
                  strokeColor={color}
                  style={{ marginTop: 4 }}
                  showInfo={false}
                />
              )}
            </div>
          );
        }
      },
      {
        title: '完整模型',
        dataIndex: 'complete',
        key: 'complete',
        width: 120,
        align: 'center',
        render: (text, record) => (
          <span style={{ 
            color: '#52c41a',
            fontWeight: record.key === 'total' ? 'bold' : 'normal'
          }}>
            <CheckCircleOutlined style={{ marginRight: 4 }} />
            {text}
          </span>
        )
      },
      {
        title: '缺失模型',
        dataIndex: 'incomplete',
        key: 'incomplete',
        width: 120,
        align: 'center',
        render: (text, record) => (
          <span style={{ 
            color: text > 0 ? '#f5222d' : '#52c41a',
            fontWeight: record.key === 'total' ? 'bold' : 'normal'
          }}>
            {text > 0 && <ExclamationCircleOutlined style={{ marginRight: 4 }} />}
            {text}
          </span>
        )
      }
    ];

    return (
      <Card title="详细统计表格" style={{ marginBottom: 24 }}>
        <Table
          dataSource={dataSource}
          columns={columns}
          pagination={false}
          scroll={{ x: 1200 }}
          size="middle"
          rowClassName={(record) => record.key === 'total' ? 'total-row' : ''}
        />
        <style jsx>{`
          .total-row {
            background-color: #f0f2f5 !important;
            border-top: 2px solid #d9d9d9;
          }
        `}</style>
      </Card>
    );
  };

  // 渲染缺失模型列表
  const renderMissingModels = () => {
    if (!statsData || !statsData.vendor_stats) return null;

    const missingData = Object.entries(statsData.vendor_stats)
      .filter(([vendor, stats]) => stats.missing_models && stats.missing_models.length > 0)
      .map(([vendor, stats]) => ({ vendor, models: stats.missing_models }));

    if (missingData.length === 0) {
      return (
        <Card title={
          <span style={{ color: '#52c41a' }}>
            <CheckCircleOutlined style={{ marginRight: 8 }} />
            文档完整性检查
          </span>
        }>
          <Alert 
            message="恭喜！所有大模型都有对应的文档" 
            type="success" 
            showIcon 
          />
        </Card>
      );
    }

    return (
      <Card title={
        <span style={{ color: '#f5222d' }}>
          <ExclamationCircleOutlined style={{ marginRight: 8 }} />
          缺少文档的大模型列表
        </span>
      }>
        <Row gutter={[16, 16]}>
          {missingData.map(({ vendor, models }) => (
            <Col span={12} key={vendor}>
              <Card size="small" title={`${vendor} (${models.length}个)`}>
                <Space wrap>
                  {models.map((model, index) => (
                    <Tag key={index} color="red">
                      {model}
                    </Tag>
                  ))}
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    );
  };

  return (
    <div style={{ padding: '24px', minHeight: '100vh', background: '#f0f2f5' }}>
      {/* 页面头部 */}
      <Card style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Breadcrumb style={{ marginBottom: 16 }}>
              <Breadcrumb.Item>
                <HomeOutlined />
                <span style={{ marginLeft: '8px' }}>首页</span>
              </Breadcrumb.Item>
              <Breadcrumb.Item>
                <BarChartOutlined />
                <span style={{ marginLeft: '8px' }}>文档统计</span>
              </Breadcrumb.Item>
            </Breadcrumb>
            
            <Title level={2} style={{ margin: 0 }}>
              <BarChartOutlined style={{ marginRight: '12px', color: '#1890ff' }} />
              大模型文档统计报告
            </Title>
            <Text type="secondary" style={{ fontSize: '16px' }}>
              实时扫描远程服务器 (/HDD_Raid/SVN_MODEL_REPO/Vendor) 的大模型文档完整性
            </Text>
            {statsData?.timestamp && (
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">
                  更新时间: {new Date(statsData.timestamp).toLocaleString('zh-CN')}
                </Text>
              </div>
            )}
          </div>
          
          <Space>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={() => fetchDocStatistics(false)}
              loading={loading}
              size="large"
            >
              刷新数据
            </Button>
            <Button
              icon={<SyncOutlined />}
              onClick={() => fetchDocStatistics(true)}
              loading={loading}
              size="large"
            >
              强制刷新
            </Button>
            <Button
              icon={<ClearOutlined />}
              onClick={clearCacheAndRefresh}
              loading={loading}
              size="large"
              danger
            >
              清除缓存
            </Button>
            <Button
              icon={<SyncOutlined />}
              onClick={refreshDataAsync}
              size="large"
            >
              后台刷新
            </Button>
          </Space>
        </div>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert 
          type="error" 
          message="获取数据失败" 
          description={error}
          style={{ marginBottom: 24 }}
          showIcon
        />
      )}

      {/* 主要内容 */}
      {loading && !statsData ? (
        renderSkeleton()
      ) : (
        <Spin spinning={loading && statsData}>
          {statsData && (
            <>
              {/* 总览卡片 */}
              {renderOverviewCards()}

              {/* 详细统计表格 */}
              {renderDetailTable()}

              {/* 缺失模型列表 */}
              {renderMissingModels()}
            </>
          )}
        </Spin>
      )}
    </div>
  );
}