"""
AI智能分析视图
"""
import json
import hashlib
import time
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from libs import json_response, Json<PERSON>arser, Argument
from libs.decorators import auth
from libs.ai_helper import ai_helper, AI
from .models import AIConfig, AIAnalysisLog


class AIConfigView(View):
    """AI配置管理"""
    
    def get(self, request):
        """获取AI配置"""
        try:
            config = AIConfig.objects.first()
            if not config:
                # 创建默认配置
                config = AIConfig.objects.create(
                    ai_enabled=False,
                    api_base='https://api-inference.modelscope.cn/v1',
                    model_name='qwen/Qwen2.5-72B-Instruct',
                    max_tokens=4000,
                    temperature=0.7,
                    cache_enabled=True,
                    cache_ttl=3600
                )
            
            return json_response({
                'ai_enabled': config.ai_enabled,
                'api_base': config.api_base,
                'api_key': config.api_key,
                'model_name': config.model_name,
                'max_tokens': config.max_tokens,
                'temperature': config.temperature,
                'cache_enabled': config.cache_enabled,
                'cache_ttl': config.cache_ttl,
            })
        except Exception as e:
            return json_response(error=f'获取AI配置失败: {str(e)}')
    
    def post(self, request):
        """更新AI配置"""
        try:
            form, error = JsonParser(
                Argument('ai_enabled', default=False, required=False, type=bool),
                Argument('api_base', required=False, type=str),
                Argument('api_key', required=False, type=str),
                Argument('model_name', required=False, type=str),
                Argument('max_tokens', required=False, type=int),
                Argument('temperature', required=False, type=float),
                Argument('cache_enabled', required=False, type=bool),
                Argument('cache_ttl', required=False, type=int),
            ).parse(request.body)

            if error:
                return json_response(error=error)
            
            config = AIConfig.objects.first()
            if not config:
                config = AIConfig()
            
            # 更新配置
            if form.ai_enabled is not None:
                config.ai_enabled = form.ai_enabled
            if form.api_base:
                config.api_base = form.api_base
            if form.api_key is not None:
                config.api_key = form.api_key
            if form.model_name:
                config.model_name = form.model_name
            if form.max_tokens:
                config.max_tokens = form.max_tokens
            if form.temperature is not None:
                config.temperature = form.temperature
            if form.cache_enabled is not None:
                config.cache_enabled = form.cache_enabled
            if form.cache_ttl:
                config.cache_ttl = form.cache_ttl
            
            config.save()
            
            # 更新AI助手配置
            ai_helper.update_config({
                'api_base': config.api_base,
                'api_key': config.api_key,
                'model': config.model_name,
                'max_tokens': config.max_tokens,
                'temperature': config.temperature,
            })
            
            return json_response('AI配置更新成功')
        except Exception as e:
            return json_response(error=f'更新AI配置失败: {str(e)}')


class AIStatusView(View):
    """AI服务状态"""
    
    def get(self, request):
        """获取AI服务状态"""
        try:
            config = AIConfig.objects.first()
            if not config or not config.ai_enabled:
                return json_response({
                    'available': False,
                    'error': 'AI功能未启用'
                })
            
            # 检查AI服务状态
            status = ai_helper.get_status()
            
            return json_response({
                'available': ai_helper.is_available(),
                'status': status,
                'config': {
                    'model': config.model_name,
                    'api_base': config.api_base,
                }
            })
        except Exception as e:
            return json_response({
                'available': False,
                'error': f'检查AI服务状态失败: {str(e)}'
            })


class AITestView(View):
    """AI功能测试"""
    
    def post(self, request):
        """测试AI分析功能"""
        try:
            form, error = JsonParser(
                Argument('content', help='测试内容'),
                Argument('filename', default='test.log', required=False),
            ).parse(request.body)

            if error:
                return json_response(error=error)
            
            config = AIConfig.objects.first()
            if not config or not config.ai_enabled:
                return json_response(error='AI功能未启用')
            
            start_time = time.time()
            
            # 执行AI分析
            analysis_result = AI.analyze_log(form.content, form.filename)
            
            processing_time = time.time() - start_time
            
            # 记录分析日志
            content_hash = hashlib.md5(form.content.encode()).hexdigest()
            AIAnalysisLog.objects.create(
                filename=form.filename,
                content_hash=content_hash,
                analysis_type='test',
                analysis_result=json.dumps(analysis_result, ensure_ascii=False),
                success=True,
                processing_time=processing_time,
                token_usage=0  # 这里可以从AI响应中获取实际token使用量
            )
            
            return json_response({
                'success': True,
                'ai_analysis': analysis_result,
                'processing_time': processing_time,
                'message': 'AI测试完成'
            })
            
        except Exception as e:
            # 记录失败日志
            try:
                content_hash = hashlib.md5(form.content.encode()).hexdigest()
                AIAnalysisLog.objects.create(
                    filename=form.filename,
                    content_hash=content_hash,
                    analysis_type='test',
                    analysis_result='{}',
                    success=False,
                    error_message=str(e),
                    processing_time=0
                )
            except:
                pass
            
            return json_response(error=f'AI测试失败: {str(e)}')


class AIAnalysisView(View):
    """AI分析接口"""
    
    def post(self, request):
        """执行AI分析"""
        try:
            form, error = JsonParser(
                Argument('content', help='分析内容'),
                Argument('filename', help='文件名'),
                Argument('data_type', default='log_analysis', required=False),
            ).parse(request.body)

            if error:
                return json_response(error=error)
            
            config = AIConfig.objects.first()
            if not config or not config.ai_enabled:
                return json_response(error='AI功能未启用')
            
            # 检查缓存
            content_hash = hashlib.md5(form.content.encode()).hexdigest()
            if config.cache_enabled:
                cached_result = AIAnalysisLog.objects.filter(
                    filename=form.filename,
                    content_hash=content_hash,
                    success=True
                ).first()
                
                if cached_result:
                    return json_response({
                        'success': True,
                        'ai_analysis': json.loads(cached_result.analysis_result),
                        'cached': True
                    })
            
            start_time = time.time()
            
            # 执行AI分析
            if form.data_type == 'benchmark_result':
                analysis_result = AI.analyze_benchmark(form.content, form.filename)
            else:
                analysis_result = AI.analyze_log(form.content, form.filename)
            
            processing_time = time.time() - start_time
            
            # 保存分析结果
            AIAnalysisLog.objects.create(
                filename=form.filename,
                content_hash=content_hash,
                analysis_type=form.data_type,
                analysis_result=json.dumps(analysis_result, ensure_ascii=False),
                success=True,
                processing_time=processing_time,
                token_usage=0
            )
            
            return json_response({
                'success': True,
                'ai_analysis': analysis_result,
                'processing_time': processing_time
            })
            
        except Exception as e:
            # 记录失败日志
            try:
                content_hash = hashlib.md5(form.content.encode()).hexdigest()
                AIAnalysisLog.objects.create(
                    filename=form.filename,
                    content_hash=content_hash,
                    analysis_type=form.data_type,
                    analysis_result='{}',
                    success=False,
                    error_message=str(e),
                    processing_time=0
                )
            except:
                pass
            
            return json_response(error=f'AI分析失败: {str(e)}')
