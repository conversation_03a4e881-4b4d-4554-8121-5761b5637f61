.notice {
  :global(.ant-card-body) {
    height: 234px;
    padding: 0 24px;
    overflow: auto;
  }

  button {
    padding-right: 0;
  }

  .title {
    flex: 1;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .title:hover {
    color: #1890ff
  }

  .badge {
    overflow: hidden;
  }

  .date {
    display: inline-block;
    font-size: 12px;
    color: #999;
  }

  .add {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 8px;
    height: 35px;
    border-radius: 2px;
    border: 1px dashed #d9d9d9;
    font-size: 12px;
    cursor: pointer;
  }

  .add:hover {
    border: 1px dashed #1890ff;
    color: #1890ff;
  }

  .item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    width: 100%;
    align-items: center;

    :global(.anticon) {
      cursor: pointer;
      color: #1890ff;
    }
  }
}

.nav {
  margin-top: 12px;

  button {
    padding-right: 0;
  }

  .add {
    cursor: pointer;
    height: 167px;
    border: 1px dashed #d9d9d9;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .add:hover {
    border: 1px dashed #1890ff;
    color: #1890ff;
  }

  :global(.ant-card) {
    height: 167px;
    background-color: #fdfdfd;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      border-color: #1890ff;
    }

    :global(.ant-card-actions) {
      background-color: #fafafa;
    }

    :global(.ant-card-meta-description) {
      height: 44px;
      display: -webkit-box;
      text-overflow: ellipsis;
      overflow: hidden;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    // 确保卡片主体区域也有点击效果
    :global(.ant-card-body) {
      cursor: pointer;
    }
  }

  .icon {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 32px;
    height: 32px;
    padding: 8px;
    font-size: 16px;
    color: rgba(0, 0, 0, .45);
    cursor: pointer;
  }

  .icon:hover {
    color: #ff4d4f;
  }
}

.minusIcon {
  font-size: 26px;
  color: #a6a6a6;
}

.minusIcon:hover {
  color: #ff4d4f;
}

.imgExample {
  position: absolute;
  top: 62px;
  left: 130px;

  :global(.ant-avatar) {
    cursor: pointer;
    margin-right: 12px;
  }
}