# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# React build
spug_web/build/
spug_web/dist/
spug_api/storage/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Temporary files
temp/
tmp/
*.tmp
*.temp

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Environment configuration files (current active configs)
spug_api/spug/settings_current.py
spug_web/src/setupProxy.js

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Custom - Spug specific
spug_api/storage/
spug_api/repos/
spug_api/files/
spug_api/venv/

# Debug and development files
spug_api/api_debug.txt
spug_api/debug_output.txt
spug_api/test_api.py
*.debug
debug_*.txt
api_debug_*.txt

# Sample and test data files
sample_*.txt
sample_*.json
test_*.json
test_batch_*.json
*_sample.*
*_test_data.*

# Benchmark and performance test files
Max_Combination_Search.py
max.sh
benchmark_*.txt
performance_*.log

# Documentation that might contain sensitive info
technical_design.md
docs/development/DEVELOPER_BACKDOOR.md

# Cleanup scripts
cleanup_*.js

# Kiro directory (appears to be project management tool)
.kiro/

# Additional log patterns
spug_api/files/执行日志/
*.funboost.log
*.global_except_hook.log
*.root.log
*.print
*.std

# Package lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Editor and IDE specific files
*.sublime-*
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json

# Runtime and process files
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out