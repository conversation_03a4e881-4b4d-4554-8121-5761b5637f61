/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React from 'react';
import { observer } from 'mobx-react';
import { Table, Modal, Dropdown, Button, Menu, Avatar, Tooltip, Space, Tag, Radio, Input, message, Card, Typography } from 'antd';
import { PlusOutlined, DownOutlined, SyncOutlined, FormOutlined, DashboardOutlined, SearchOutlined } from '@ant-design/icons';
import { Action, AuthButton, AuthFragment } from 'components';
import IPAddress from './IPAddress';
import { http, hasPermission } from 'libs';
import store from './store';
import icons from './icons';
import moment from 'moment';
import styles from './index.module.less';

const { Text } = Typography;

function ComTable() {
  function handleDelete(text) {
    Modal.confirm({
      title: '删除确认',
      content: `确定要删除【${text['name']}】?`,
      onOk: () => {
        return http.delete('/api/host/', {params: {id: text.id}})
          .then(() => {
            message.success('删除成功');
            store.fetchRecords()
          })
      }
    })
  }

  function handleOpenGpuDashboard(host) {
    // 在新标签页打开GPU大屏
    const url = `/host/gpu-dashboard/${host.id}`;
    window.open(url, '_blank');
  }

  function handleImport(menu) {
    if (menu.key === 'excel') {
      store.importVisible = true
    } else if (menu.key === 'form') {
      store.showForm({group_ids: [store.group.value]})
    } else {
      store.cloudImport = menu.key
    }
  }

  function ExpTime(props) {
    if (!props.value) return null
    let value = moment(props.value)
    const days = value.diff(moment(), 'days')
    if (days > 30) {
      return <span>剩余 <b style={{color: '#389e0d'}}>{days}</b> 天</span>
    } else if (days > 7) {
      return <span>剩余 <b style={{color: '#faad14'}}>{days}</b> 天</span>
    } else if (days >= 0) {
      return <span>剩余 <b style={{color: '#d9363e'}}>{days}</b> 天</span>
    } else {
      return <span>过期 <b style={{color: '#d9363e'}}>{Math.abs(days)}</b> 天</span>
    }
  }

  return (
    <Card className={styles.modernTableCard} bordered={false}>
      {/* Table Header */}
      <div className={styles.tableHeader}>
        <div className={styles.tableHeaderLeft}>
          <Input 
            allowClear 
            value={store.f_word} 
            placeholder="搜索主机名称或IP地址..." 
            prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
            className={styles.searchInput}
            onChange={e => store.f_word = e.target.value}
          />
          <Radio.Group 
            value={store.f_status} 
            onChange={e => store.f_status = e.target.value}
            className={styles.filterGroup}
          >
            <Radio.Button value="">全部</Radio.Button>
            <Radio.Button value={false}>未验证</Radio.Button>
          </Radio.Group>
        </div>
        <div className={styles.tableHeaderRight}>
          <Space size={12}>
            <AuthButton
              auth="host.host.add"
              icon={<SyncOutlined/>}
              className={styles.actionButton}
              onClick={() => store.showSync()}
            >
              验证连接
            </AuthButton>
            <AuthFragment auth="host.host.add">
              <Dropdown overlay={(
                <Menu onClick={handleImport} className={styles.dropdownMenu}>
                  <Menu.Item key="form">
                    <Space>
                      <FormOutlined style={{fontSize: 16, color: '#667eea'}}/>
                      <span>新建主机</span>
                    </Space>
                  </Menu.Item>
                  <Menu.Divider />
                  <Menu.Item key="excel">
                    <Space>
                      <Avatar shape="square" size={20} src={icons.excel}/>
                      <span>Excel 导入</span>
                    </Space>
                  </Menu.Item>
                  <Menu.Item key="ali">
                    <Space>
                      <Avatar shape="square" size={20} src={icons.alibaba}/>
                      <span>阿里云导入</span>
                    </Space>
                  </Menu.Item>
                  <Menu.Item key="tencent">
                    <Space>
                      <Avatar shape="square" size={20} src={icons.tencent}/>
                      <span>腾讯云导入</span>
                    </Space>
                  </Menu.Item>
                </Menu>
              )}>
                <Button type="primary" className={styles.primaryButton}>
                  <PlusOutlined /> 添加主机 <DownOutlined/>
                </Button>
              </Dropdown>
            </AuthFragment>
          </Space>
        </div>
      </div>

      {/* Modern Table */}
      <Table
        rowKey="id"
        loading={store.isFetching}
        dataSource={store.dataSource}
        className={styles.modernTable}
        pagination={{
          showSizeChanger: true,
          showLessItems: true,
          hideOnSinglePage: true,
          showTotal: (total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          pageSizeOptions: ['10', '20', '50', '100'],
          className: styles.tablePagination
        }}
      >
        <Table.Column
          showSorterTooltip={false}
          title="主机信息"
          render={info => (
            <div className={styles.hostInfo}>
              <div className={styles.hostName}>
                <Action.Button onClick={() => store.showDetail(info)} className={styles.hostNameLink}>
                  {info.name}
                </Action.Button>
              </div>
              <div className={styles.hostMeta}>
                <Space size={8}>
                  <Tooltip title={info.os_name}>
                    <Avatar shape="square" size={16} src={icons[info.os_type]}/>
                  </Tooltip>
                  <Text type="secondary">{info.cpu}核 {info.memory}GB</Text>
                </Space>
              </div>
            </div>
          )}
          sorter={(a, b) => a.name.localeCompare(b.name)}
        />
        <Table.Column 
          title="网络地址" 
          render={info => (
            <div className={styles.ipAddresses}>
              <IPAddress ip={info.public_ip_address} isPublic/>
              <IPAddress ip={info.private_ip_address}/>
            </div>
          )}
        />
        <Table.Column
          title="运行状态"
          dataIndex="is_verified"
          render={v => (
            <Tag 
              color={v ? 'success' : 'warning'} 
              className={styles.statusTag}
            >
              {v ? '已验证' : '未验证'}
            </Tag>
          )}
        />
        <Table.Column hide title="到期信息" dataIndex="expired_time" render={v => <ExpTime value={v}/>}/>
        <Table.Column hide title="备注信息" dataIndex="desc"/>
        {hasPermission('host.host.edit|host.host.del|host.host.console') && (
          <Table.Column 
            width={320} 
            title="操作" 
            render={info => (
              <div className={styles.actionButtons}>
                <Action.Button 
                  auth="host.host.edit" 
                  onClick={() => store.showForm(info)}
                  className={styles.editButton}
                  size="small"
                >
                  编辑
                </Action.Button>
                <Action.Button 
                  auth="host.host.view" 
                  onClick={() => handleOpenGpuDashboard(info)}
                  icon={<DashboardOutlined />}
                  className={styles.gpuButton}
                  size="small"
                >
                  GPU监控
                </Action.Button>
                <Action.Button 
                  danger 
                  auth="host.host.del" 
                  onClick={() => handleDelete(info)}
                  className={styles.deleteButton}
                  size="small"
                >
                  删除
                </Action.Button>
              </div>
            )}
          />
        )}
      </Table>
    </Card>
  )
}

export default observer(ComTable)
