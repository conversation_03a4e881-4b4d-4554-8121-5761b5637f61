// 现代化Modal样式
.modernModal {
  .ant-modal-content {
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  }

  .ant-modal-header {
    border-radius: 12px 12px 0 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;

    .ant-modal-title {
      color: white;
      font-weight: 600;
    }
  }

  .ant-modal-close {
    color: white;

    &:hover {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.chartContainer {
  .modernTabs {
    .ant-tabs-nav {
      margin-bottom: 16px;

      .ant-tabs-tab {
        font-size: 14px;
        font-weight: 500;
        border-radius: 8px 8px 0 0;
        margin-right: 4px;

        &.ant-tabs-tab-active {
          background: linear-gradient(135deg, #1890ff, #36cfc9);

          .ant-tabs-tab-btn {
            color: white;
          }
        }

        &:hover:not(.ant-tabs-tab-active) {
          background: rgba(24, 144, 255, 0.1);
        }
      }
    }

    .ant-tabs-content-holder {
      background: white;
      border-radius: 0 8px 8px 8px;
      padding: 16px;
    }
  }
}

// 仪表板样式
.dashboardContainer {
  .metricsRow {
    margin-bottom: 16px;
  }

  .metricCard {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    }

    .metricContent {
      display: flex;
      align-items: center;

      .metricIcon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
      }

      .metricInfo {
        flex: 1;

        .metricValue {
          font-size: 24px;
          font-weight: 700;
          color: #262626;
          line-height: 1.2;
        }

        .metricLabel {
          font-size: 14px;
          color: #8c8c8c;
          margin-top: 4px;

          .trendIndicator {
            margin-left: 8px;
            font-size: 12px;
            font-weight: 500;
          }
        }
      }
    }
  }

  .chartCard {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        font-weight: 600;
        color: #262626;
      }
    }
  }

  .progressContainer {
    .progressItem {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .progressLabel {
        margin-bottom: 8px;
        font-size: 14px;

        .ant-badge {
          .ant-badge-status-dot {
            width: 8px;
            height: 8px;
          }
        }
      }

      .ant-progress {
        .ant-progress-bg {
          border-radius: 4px;
        }
      }
    }
  }
}

// 趋势分析样式
.trendContainer {
  .chartCard {
    .ant-card-extra {
      .ant-select {
        .ant-select-selector {
          border-radius: 6px;
          border: 1px solid #d9d9d9;

          &:hover {
            border-color: #40a9ff;
          }
        }
      }
    }
  }
}

// 计划对比样式
.planContainer {
  .rankingList {
    .rankingItem {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .rankingNumber {
        margin-right: 12px;

        .ant-badge {
          .ant-badge-count {
            min-width: 24px;
            height: 24px;
            line-height: 24px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
          }
        }
      }

      .rankingInfo {
        flex: 1;

        .rankingName {
          font-size: 14px;
          font-weight: 500;
          color: #262626;
          margin-bottom: 4px;

          // 文本溢出处理
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .rankingValue {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }
}

.customTooltip {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  
  .tooltipLabel {
    font-weight: 500;
    margin-bottom: 4px;
    color: #333;
  }
  
  p {
    margin: 0;
    font-size: 12px;
    line-height: 1.4;
  }
}

.statsContainer {
  .statItem {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .colorIndicator {
      width: 12px;
      height: 12px;
      border-radius: 2px;
      margin-right: 8px;
    }
    
    .statLabel {
      flex: 1;
      font-size: 14px;
      color: #333;
    }
    
    .statValue {
      font-weight: 500;
      color: #1890ff;
      margin-right: 8px;
    }
    
    .statPercentage {
      font-size: 12px;
      color: #666;
    }
  }
}

.reportContent {
  .reportSection {
    h4 {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 12px;
      color: #333;
      border-bottom: 2px solid #1890ff;
      padding-bottom: 4px;
    }
    
    ul {
      margin: 0;
      padding-left: 16px;
      
      li {
        margin-bottom: 8px;
        font-size: 14px;
        line-height: 1.6;
        
        strong {
          color: #1890ff;
        }
      }
    }
  }
} 