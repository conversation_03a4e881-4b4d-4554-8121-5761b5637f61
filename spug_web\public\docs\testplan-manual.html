<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试计划用户手册 - Spug运维平台</title>
    <style>
        /* 基于Vue.js官方文档样式 */
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background-color: #ffffff;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            border-bottom: 1px solid #eaecef;
            margin-bottom: 40px;
            padding-bottom: 20px;
        }
        
        h1 {
            font-size: 2.5em;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 20px 0;
        }
        
        .subtitle {
            font-size: 1.2em;
            color: #7f8c8d;
            margin: 0;
        }
        
        h2 {
            font-size: 1.8em;
            font-weight: 600;
            color: #2c3e50;
            margin: 40px 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #eaecef;
        }
        
        h3 {
            font-size: 1.4em;
            font-weight: 600;
            color: #2c3e50;
            margin: 30px 0 15px 0;
        }
        
        h4 {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin: 25px 0 10px 0;
        }
        
        p {
            margin: 15px 0;
            line-height: 1.7;
        }
        
        .tip {
            background-color: #f3f5f7;
            border-left: 4px solid #42b883;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .tip-title {
            font-weight: 600;
            color: #42b883;
            margin-bottom: 8px;
        }
        
        .warning {
            background-color: #fff6f0;
            border-left: 4px solid #e67e22;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .warning-title {
            font-weight: 600;
            color: #e67e22;
            margin-bottom: 8px;
        }
        
        .code-block {
            background-color: #f8f8f8;
            border: 1px solid #e1e1e1;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .inline-code {
            background-color: #f8f8f8;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #e83e8c;
        }
        
        .image-placeholder {
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            color: #6c757d;
            font-style: italic;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #42b883;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .step-number {
            background-color: #42b883;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .nav-toc {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .nav-toc h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .nav-toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .nav-toc li {
            margin: 8px 0;
        }
        
        .nav-toc a {
            color: #007bff;
            text-decoration: none;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .nav-toc a:hover {
            background-color: #e3f2fd;
            text-decoration: none;
        }
        
        .nav-toc ul ul {
            padding-left: 20px;
            margin-top: 5px;
        }
        
        .button-demo {
            display: inline-block;
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .button-demo.success {
            background-color: #28a745;
        }
        
        .button-demo.warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .button-demo.danger {
            background-color: #dc3545;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-badge.running {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        
        .status-badge.success {
            background-color: #e8f5e8;
            color: #2e7d32;
        }
        
        .status-badge.failed {
            background-color: #ffebee;
            color: #c62828;
        }
        
        .table-demo {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .table-demo th,
        .table-demo td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table-demo th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>测试计划用户手册</h1>
            <p class="subtitle">Spug运维平台 - 自动化测试计划管理与执行</p>
        </div>

        <div class="nav-toc">
            <h3>目录</h3>
            <ul>
                <li><a href="#introduction">介绍</a></li>
                <li><a href="#getting-started">快速开始</a></li>
                <li><a href="#plan-management">测试计划管理</a>
                    <ul>
                        <li><a href="#create-plan">新建测试计划</a></li>
                        <li><a href="#edit-plan">编辑测试计划</a></li>
                        <li><a href="#copy-plan">复制测试计划</a></li>
                        <li><a href="#delete-plan">删除测试计划</a></li>
                    </ul>
                </li>
                <li><a href="#step-editor">步骤编辑器</a>
                    <ul>
                        <li><a href="#command-steps">命令步骤</a></li>
                        <li><a href="#file-steps">文件传输步骤</a></li>
                        <li><a href="#callback-steps">回调步骤</a></li>
                        <li><a href="#assertion-config">断言配置</a></li>
                    </ul>
                </li>
                <li><a href="#execution">测试计划执行</a></li>
                <li><a href="#reports">执行报告</a></li>
                <li><a href="#import-export">导入导出</a></li>
                <li><a href="#best-practices">最佳实践</a></li>
            </ul>
        </div>

        <section id="introduction">
            <h2>介绍</h2>
            <p>测试计划是Spug运维平台的核心功能之一，它允许您创建、管理和执行复杂的自动化测试流程。通过测试计划，您可以：</p>
            
            <ul class="feature-list">
                <li>组织多个测试步骤为有序的执行流程</li>
                <li>支持文件传输、命令执行、断言验证等多种步骤类型</li>
                <li>跨多台主机并行或串行执行测试</li>
                <li>实时监控执行进度和结果</li>
                <li>生成详细的执行报告和日志</li>
                <li>支持长耗时任务的回调机制</li>
                <li>提供丰富的断言和验证功能</li>
            </ul>

            <div class="image-placeholder">
                [测试计划主界面截图]
            </div>

            <div class="tip">
                <div class="tip-title">💡 提示</div>
                <p>测试计划特别适用于自动化部署、系统验证、性能测试等场景。通过预定义的测试步骤，可以确保操作的一致性和可重复性。</p>
            </div>
        </section>

        <section id="getting-started">
            <h2>快速开始</h2>
            <p>让我们通过一个简单的例子来了解如何创建和使用测试计划。</p>

            <h3>第一个测试计划</h3>
            <p>我们将创建一个简单的系统检查测试计划，包含环境检查和服务状态验证。</p>

            <div class="step-number">1</div>
            <strong>进入测试计划页面</strong>
            <p>在Spug主界面中，点击左侧菜单的"任务计划" → "测试计划"进入测试计划管理页面。</p>

            <div class="image-placeholder">
                [导航菜单截图]
            </div>

            <div class="step-number">2</div>
            <strong>创建新的测试计划</strong>
            <p>点击页面右上角的<span class="inline-code">新建测试计划</span>按钮，打开测试计划编辑器。</p>

            <div class="step-number">3</div>
            <strong>填写基本信息</strong>
            <p>在基本信息选项卡中，填写以下内容：</p>

            <div class="code-block">
名称: 系统健康检查
分类: 系统监控
描述: 检查系统基本状态和关键服务运行情况
步骤间隔: 2秒
            </div>

            <div class="image-placeholder">
                [基本信息填写界面截图]
            </div>
        </section>

        <section id="plan-management">
            <h2>测试计划管理</h2>

            <section id="create-plan">
                <h3>新建测试计划</h3>
                <p>创建新的测试计划是自动化测试的第一步。系统提供了直观的编辑器来帮助您构建测试流程。</p>

                <h4>基本信息配置</h4>
                <p>每个测试计划都需要配置以下基本信息：</p>

                <table class="table-demo">
                    <thead>
                        <tr>
                            <th>字段</th>
                            <th>说明</th>
                            <th>是否必填</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>计划名称</td>
                            <td>测试计划的唯一标识名称</td>
                            <td>是</td>
                        </tr>
                        <tr>
                            <td>分类</td>
                            <td>用于组织和筛选测试计划</td>
                            <td>否</td>
                        </tr>
                        <tr>
                            <td>描述</td>
                            <td>详细说明测试计划的用途和目标</td>
                            <td>否</td>
                        </tr>
                        <tr>
                            <td>步骤间隔</td>
                            <td>每个测试步骤之间的等待时间（秒）</td>
                            <td>否</td>
                        </tr>
                    </tbody>
                </table>

                <div class="image-placeholder">
                    [测试计划基本信息编辑界面截图]
                </div>

                <div class="tip">
                    <div class="tip-title">💡 命名建议</div>
                    <p>建议使用有意义的测试计划名称，如"GPU驱动升级测试"、"Web服务部署验证"等，便于团队成员理解和维护。</p>
                </div>
            </section>

            <section id="edit-plan">
                <h3>编辑测试计划</h3>
                <p>已创建的测试计划可以随时进行修改和完善。</p>

                <h4>修改基本信息</h4>
                <p>在测试计划列表中，点击计划名称或操作列的"编辑"按钮，即可进入编辑模式。</p>

                <div class="image-placeholder">
                    [测试计划列表和编辑按钮截图]
                </div>

                <h4>版本控制</h4>
                <p>每次保存测试计划时，系统会自动记录修改时间和修改人，确保变更的可追溯性。</p>

                <div class="warning">
                    <div class="warning-title">⚠️ 注意</div>
                    <p>如果测试计划正在执行中，建议等待执行完成后再进行修改，避免影响正在运行的任务。</p>
                </div>
            </section>

            <section id="copy-plan">
                <h3>复制测试计划</h3>
                <p>复制功能可以帮助您基于现有测试计划快速创建类似的测试流程。</p>

                <div class="step-number">1</div>
                <strong>选择要复制的测试计划</strong>
                <p>在测试计划列表中，找到要复制的测试计划。</p>

                <div class="step-number">2</div>
                <strong>执行复制操作</strong>
                <p>点击操作列的"复制"按钮，系统会创建一个新的测试计划副本。</p>

                <div class="step-number">3</div>
                <strong>修改副本信息</strong>
                <p>复制的测试计划名称会自动添加"(副本)"后缀，您可以根据需要修改名称和其他配置。</p>

                <div class="image-placeholder">
                    [复制测试计划操作截图]
                </div>
            </section>

            <section id="delete-plan">
                <h3>删除测试计划</h3>
                <p>不再需要的测试计划可以被安全删除。</p>

                <div class="warning">
                    <div class="warning-title">⚠️ 删除前确认</div>
                    <p>删除测试计划是不可逆操作，请确保：</p>
                    <ul>
                        <li>测试计划当前没有正在执行的任务</li>
                        <li>已备份重要的测试步骤和配置</li>
                        <li>团队成员都已知晓删除操作</li>
                    </ul>
                </div>

                <div class="step-number">1</div>
                <strong>选择要删除的测试计划</strong>
                <p>在测试计划列表中定位目标计划。</p>

                <div class="step-number">2</div>
                <strong>点击删除按钮</strong>
                <p>点击操作列的"删除"按钮。</p>

                <div class="step-number">3</div>
                <strong>确认删除</strong>
                <p>在弹出的确认对话框中点击"确定"完成删除。</p>

                <div class="image-placeholder">
                    [删除确认对话框截图]
                </div>
            </section>
        </section>

        <section id="step-editor">
            <h2>步骤编辑器</h2>
            <p>步骤编辑器是测试计划的核心组件，它允许您定义复杂的测试流程。系统支持多种类型的测试步骤。</p>

            <div class="image-placeholder">
                [步骤编辑器主界面截图]
            </div>

            <section id="command-steps">
                <h3>命令步骤</h3>
                <p>命令步骤用于在目标主机上执行Shell命令或脚本。</p>

                <h4>配置命令步骤</h4>
                <table class="table-demo">
                    <thead>
                        <tr>
                            <th>配置项</th>
                            <th>说明</th>
                            <th>示例</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>步骤名称</td>
                            <td>步骤的显示名称</td>
                            <td>检查系统状态</td>
                        </tr>
                        <tr>
                            <td>执行命令</td>
                            <td>要执行的Shell命令</td>
                            <td><code>df -h && free -m</code></td>
                        </tr>
                        <tr>
                            <td>工作路径</td>
                            <td>命令执行的目录</td>
                            <td>/tmp</td>
                        </tr>
                        <tr>
                            <td>超时时间</td>
                            <td>命令执行的最大等待时间</td>
                            <td>300秒</td>
                        </tr>
                    </tbody>
                </table>

                <div class="code-block">
# 示例：系统资源检查命令
echo "=== 磁盘使用情况 ==="
df -h
echo "=== 内存使用情况 ==="
free -m
echo "=== CPU负载 ==="
uptime
                </div>

                <div class="image-placeholder">
                    [命令步骤配置界面截图]
                </div>

                <div class="tip">
                    <div class="tip-title">💡 命令编写技巧</div>
                    <p>建议在命令中添加输出标识，如echo语句，这样可以让执行日志更加清晰易读。</p>
                </div>
            </section>

            <section id="file-steps">
                <h3>文件传输步骤</h3>
                <p>文件传输步骤用于将本地文件上传到目标主机的指定位置。</p>

                <h4>配置文件传输</h4>
                <p>每个步骤可以配置多个文件的传输：</p>

                <table class="table-demo">
                    <thead>
                        <tr>
                            <th>配置项</th>
                            <th>说明</th>
                            <th>注意事项</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>本地文件</td>
                            <td>要上传的文件路径</td>
                            <td>支持相对路径和绝对路径</td>
                        </tr>
                        <tr>
                            <td>目标路径</td>
                            <td>文件在目标主机的保存位置</td>
                            <td>确保目录具有写权限</td>
                        </tr>
                        <tr>
                            <td>文件权限</td>
                            <td>上传后的文件权限设置</td>
                            <td>如755、644等</td>
                        </tr>
                    </tbody>
                </table>

                <div class="image-placeholder">
                    [文件传输配置界面截图]
                </div>

                <div class="warning">
                    <div class="warning-title">⚠️ 文件路径注意事项</div>
                    <p>确保目标路径的目录已存在且具有写权限，否则文件传输可能失败。建议在文件传输前先执行目录创建命令。</p>
                </div>
            </section>

            <section id="callback-steps">
                <h3>回调步骤</h3>
                <p>回调步骤专为长耗时任务设计，如模型推理、大数据处理等可能需要数小时的操作。</p>

                <h4>回调步骤特点</h4>
                <ul class="feature-list">
                    <li>支持异步执行，不阻塞测试计划流程</li>
                    <li>可配置回调URL接收任务完成通知</li>
                    <li>支持超时机制和重试策略</li>
                    <li>提供安全的签名验证机制</li>
                </ul>

                <h4>配置回调步骤</h4>
                <table class="table-demo">
                    <thead>
                        <tr>
                            <th>配置项</th>
                            <th>说明</th>
                            <th>默认值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>回调URL</td>
                            <td>任务完成后的通知地址</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>超时时间</td>
                            <td>任务的最大执行时间</td>
                            <td>24小时</td>
                        </tr>
                        <tr>
                            <td>回调密钥</td>
                            <td>用于验证回调请求的密钥</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>重试次数</td>
                            <td>回调失败时的重试次数</td>
                            <td>3次</td>
                        </tr>
                    </tbody>
                </table>

                <div class="code-block">
# 示例：模型推理回调步骤
python3 /opt/ml/inference.py \
  --input /data/input.json \
  --output /data/result.json \
  --callback-url https://your-api.com/callback \
  --task-id ${CALLBACK_TASK_ID}
                </div>

                <div class="image-placeholder">
                    [回调步骤配置界面截图]
                </div>

                <div class="tip">
                    <div class="tip-title">💡 回调URL设计建议</div>
                    <p>建议实现幂等的回调接口，能够处理重复的回调请求。同时要验证请求签名确保安全性。</p>
                </div>
            </section>

            <section id="assertion-config">
                <h3>断言配置</h3>
                <p>断言功能可以验证步骤执行结果是否符合预期，确保测试的准确性。</p>

                <h4>支持的断言类型</h4>
                <ul class="feature-list">
                    <li><strong>退出码断言</strong>：验证命令的退出状态码</li>
                    <li><strong>输出内容断言</strong>：检查输出中是否包含特定内容</li>
                    <li><strong>正则表达式断言</strong>：使用正则表达式匹配输出</li>
                    <li><strong>JSON路径断言</strong>：验证JSON格式输出的特定字段</li>
                    <li><strong>命令执行断言</strong>：执行额外命令进行验证</li>
                </ul>

                <h4>配置断言规则</h4>
                <div class="code-block">
# 输出内容断言示例
断言类型: 输出内容包含
期望内容: "服务运行正常"
匹配模式: 包含匹配

# 正则表达式断言示例
断言类型: 正则表达式
正则表达式: "CPU使用率: (\d+)%"
期望条件: 匹配的数值 < 80
                </div>

                <div class="image-placeholder">
                    [断言配置界面截图]
                </div>

                <div class="warning">
                    <div class="warning-title">⚠️ 断言失败处理</div>
                    <p>当断言失败时，可以选择继续执行后续步骤或立即停止整个测试计划。请根据测试需求合理配置。</p>
                </div>
            </section>
        </section>

        <section id="execution">
            <h2>测试计划执行</h2>
            <p>配置完成的测试计划可以在指定的主机上执行。系统支持单机执行和多机并行执行。</p>

            <h3>执行测试计划</h3>
            <div class="step-number">1</div>
            <strong>选择测试计划</strong>
            <p>在测试计划列表中，点击要执行的计划名称或"执行"按钮。</p>

            <div class="step-number">2</div>
            <strong>选择目标主机</strong>
            <p>在弹出的执行对话框中，选择一个或多个目标主机。</p>

            <div class="image-placeholder">
                [主机选择界面截图]
            </div>

            <div class="step-number">3</div>
            <strong>开始执行</strong>
            <p>确认选择后，点击"开始执行"按钮启动测试计划。</p>

            <h3>实时监控</h3>
            <p>测试计划开始执行后，系统会打开实时监控页面，您可以：</p>

            <ul class="feature-list">
                <li>查看当前执行进度和状态</li>
                <li>实时观察每个步骤的输出日志</li>
                <li>监控各主机的执行情况</li>
                <li>在必要时中止执行</li>
            </ul>

            <div class="image-placeholder">
                [实时执行监控界面截图]
            </div>

            <h3>执行状态说明</h3>
            <table class="table-demo">
                <thead>
                    <tr>
                        <th>状态</th>
                        <th>说明</th>
                        <th>显示样式</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>运行中</td>
                        <td>测试计划正在执行</td>
                        <td><span class="status-badge running">Running</span></td>
                    </tr>
                    <tr>
                        <td>已完成</td>
                        <td>所有步骤执行完成</td>
                        <td><span class="status-badge success">Success</span></td>
                    </tr>
                    <tr>
                        <td>执行失败</td>
                        <td>某个步骤执行失败</td>
                        <td><span class="status-badge failed">Failed</span></td>
                    </tr>
                </tbody>
            </table>

            <div class="tip">
                <div class="tip-title">💡 并行执行</div>
                <p>当选择多台主机时，测试计划会在所有主机上并行执行，可以大大提高测试效率。每台主机的执行情况都会独立显示。</p>
            </div>
        </section>

        <section id="reports">
            <h2>执行报告</h2>
            <p>每次测试计划执行完成后，系统会生成详细的执行报告，包含执行结果、日志信息和统计数据。</p>

            <h3>查看执行历史</h3>
            <p>在测试计划页面点击"执行历史"标签，可以查看所有的执行记录。</p>

            <div class="image-placeholder">
                [执行历史列表截图]
            </div>

            <h3>执行报告内容</h3>
            <p>点击任意执行记录，可以查看详细的执行报告：</p>

            <h4>概览信息</h4>
            <ul class="feature-list">
                <li>执行时间和用时</li>
                <li>目标主机信息</li>
                <li>整体执行状态</li>
                <li>步骤成功/失败统计</li>
            </ul>

            <h4>步骤详情</h4>
            <ul class="feature-list">
                <li>每个步骤的执行时间</li>
                <li>命令输出和错误信息</li>
                <li>断言验证结果</li>
                <li>文件传输状态</li>
            </ul>

            <div class="image-placeholder">
                [执行报告详情截图]
            </div>

            <h4>日志下载</h4>
            <p>执行报告提供日志下载功能，您可以：</p>
            <ul class="feature-list">
                <li>下载完整的执行日志</li>
                <li>下载单个步骤的日志文件</li>
                <li>导出报告为PDF格式</li>
            </ul>

            <div class="tip">
                <div class="tip-title">💡 日志保留</div>
                <p>系统会自动保留最近30天的执行日志。超过保留期的日志会被自动清理以节省存储空间。</p>
            </div>
        </section>

        <section id="import-export">
            <h2>导入导出</h2>
            <p>测试计划支持导入导出功能，便于在不同环境间迁移配置或进行备份。</p>

            <h3>导出测试计划</h3>
            <div class="step-number">1</div>
            <strong>选择要导出的计划</strong>
            <p>在测试计划列表中选择一个或多个测试计划。</p>

            <div class="step-number">2</div>
            <strong>点击导出按钮</strong>
            <p>点击页面上方的"导出"按钮。</p>

            <div class="step-number">3</div>
            <strong>下载配置文件</strong>
            <p>系统会生成JSON格式的配置文件供下载。</p>

            <div class="image-placeholder">
                [导出操作界面截图]
            </div>

            <h3>导入测试计划</h3>
            <div class="step-number">1</div>
            <strong>准备配置文件</strong>
            <p>确保配置文件格式正确且包含必要的字段。</p>

            <div class="step-number">2</div>
            <strong>点击导入按钮</strong>
            <p>点击页面上方的"导入"按钮。</p>

            <div class="step-number">3</div>
            <strong>上传并处理</strong>
            <p>选择配置文件上传，系统会自动解析并创建测试计划。</p>

            <div class="warning">
                <div class="warning-title">⚠️ 导入注意事项</div>
                <p>导入时如果发现同名的测试计划，系统会提示您选择覆盖或跳过。请谨慎操作以避免数据丢失。</p>
            </div>
        </section>

        <section id="best-practices">
            <h2>最佳实践</h2>
            <p>以下是使用测试计划时的一些最佳实践建议。</p>

            <h3>测试计划设计</h3>
            <h4>步骤组织</h4>
            <ul class="feature-list">
                <li>将相关的操作组织为逻辑步骤</li>
                <li>每个步骤应该有明确的目标和验证点</li>
                <li>避免单个步骤过于复杂，便于调试和维护</li>
            </ul>

            <h4>错误处理</h4>
            <ul class="feature-list">
                <li>为关键步骤配置合适的断言验证</li>
                <li>设置合理的超时时间</li>
                <li>考虑步骤失败后的处理策略</li>
            </ul>

            <h3>安全考虑</h3>
            <h4>权限管理</h4>
            <ul class="feature-list">
                <li>确保测试计划只能由授权用户创建和修改</li>
                <li>避免在命令中硬编码敏感信息</li>
                <li>使用参数化方式处理环境相关的配置</li>
            </ul>

            <h4>主机选择</h4>
            <ul class="feature-list">
                <li>确认目标主机的连接性和权限</li>
                <li>避免在生产环境直接执行未充分测试的计划</li>
                <li>建议先在测试环境验证后再应用到生产环境</li>
            </ul>

            <h3>性能优化</h3>
            <h4>并行执行</h4>
            <ul class="feature-list">
                <li>合理利用多主机并行执行能力</li>
                <li>注意避免并发操作导致的资源竞争</li>
                <li>为长耗时任务配置适当的超时时间</li>
            </ul>

            <h4>资源管理</h4>
            <ul class="feature-list">
                <li>定期清理过期的执行日志</li>
                <li>避免在测试过程中产生大量临时文件</li>
                <li>监控测试执行对系统资源的影响</li>
            </ul>

            <div class="tip">
                <div class="tip-title">💡 团队协作</div>
                <p>建立团队内的测试计划命名和组织规范，使用有意义的分类和描述，便于团队成员理解和维护。定期review和优化测试计划，确保其有效性和时效性。</p>
            </div>
        </section>

        <div style="margin-top: 60px; padding-top: 20px; border-top: 1px solid #eaecef; text-align: center; color: #7f8c8d;">
            <p>© 2024 Spug运维平台 - 测试计划用户手册</p>
            <p>如有问题或建议，请联系技术支持团队</p>
        </div>
    </div>
</body>
</html> 