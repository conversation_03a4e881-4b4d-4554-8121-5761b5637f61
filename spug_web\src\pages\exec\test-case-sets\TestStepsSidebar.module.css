/* 侧边栏样式 */
.testStepsDrawer {
  z-index: 1001;
}

.drawerTitle {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.drawerFooter {
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}

/* 进度卡片样式 */
.progressCard {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.progressSection {
  margin-bottom: 12px;
}

.progressBar {
  margin-top: 8px;
}

.manualProgressSection {
  margin-top: 16px;
}

.manualHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.sliderSection {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.progressText {
  display: block;
  text-align: center;
  margin-top: 12px;
  font-weight: 500;
  color: #1890ff;
}

/* 测试用例列表样式 */
.testCasesList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.testCaseCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.testCaseCard:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.caseTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 新的测试用例布局样式 */
.testCaseLayout {
  display: flex;
  gap: 16px;
  min-height: 120px;
}

/* 前置条件区域 - 左侧 */
.preconditionSection {
  flex: 1;
  min-width: 0;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  border-left: 4px solid #17a2b8;
}

/* 测试步骤区域 - 中间 */
.testStepsSection {
  flex: 1.5;
  min-width: 0;
  background: #fff7e6;
  border-radius: 6px;
  padding: 12px;
  border-left: 4px solid #fa8c16;
}

/* 预期结果区域 - 右侧 */
.expectedResultSection {
  flex: 1;
  min-width: 0;
  background: #f6ffed;
  border-radius: 6px;
  padding: 12px;
  border-left: 4px solid #52c41a;
}

/* 区域标题样式 */
.sectionHeader {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 6px;
}

.sectionIcon {
  font-size: 14px;
}

.sectionTitle {
  font-size: 13px;
  font-weight: 600;
  color: #262626;
}

/* 区域内容样式 */
.sectionContent {
  margin-top: 8px;
}

.readOnlyContent {
  margin: 0;
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

.stepContent {
  margin: 0;
  color: #333;
  font-size: 12px;
  line-height: 1.4;
  font-weight: 500;
}

/* 步骤勾选框样式 */
.stepCheckbox {
  margin-bottom: 0;
}

.stepCheckbox .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #52c41a;
  border-color: #52c41a;
}

.completedStep {
  text-decoration: line-through;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .testCaseLayout {
    flex-direction: column;
    gap: 12px;
  }

  .preconditionSection,
  .testStepsSection,
  .expectedResultSection {
    flex: none;
  }
}

@media (max-width: 768px) {
  .testCaseLayout {
    gap: 8px;
  }

  .preconditionSection,
  .testStepsSection,
  .expectedResultSection {
    padding: 8px;
  }

  .sectionTitle {
    font-size: 12px;
  }

  .readOnlyContent,
  .stepContent {
    font-size: 11px;
  }
}

/* 动画效果 */
.testCaseLayout {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 进度条自定义样式 */
.progressBar .ant-progress-bg {
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
}

/* 滑块样式 */
.sliderSection .ant-slider-track {
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
}

.sliderSection .ant-slider-handle {
  border-color: #1890ff;
}

.sliderSection .ant-slider-handle:focus {
  box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.2);
}

/* 标签样式 */
.caseTitle .ant-tag {
  margin-left: 8px;
  border-radius: 12px;
  font-size: 11px;
  padding: 2px 8px;
}

/* 图标样式 */
.stepCheckbox .anticon {
  color: #1890ff;
  margin-right: 6px;
}

.completedStep .anticon {
  color: #52c41a;
}

/* 空状态样式 */
.testCasesList .ant-empty {
  margin: 40px 0;
}

.testCasesList .ant-empty-description {
  color: #999;
}
