/**
 * 任务管理页面
 */
import React from 'react';
import { observer } from 'mobx-react';
import { Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { Action, TableCard, AuthDiv, Breadcrumb } from 'components';
import { Modal, message } from 'antd';
import http from 'libs/http';
import ComForm from './Form';
import ComInfo from './Info';
import store from './store';

@observer
class TaskIndex extends React.Component {
  componentDidMount() {
    store.fetchRecords()
  }

  columns = [{
    title: '任务名称',
    dataIndex: 'name',
    ellipsis: true,
  }, {
    title: '任务标识',
    dataIndex: 'key',
  }, {
    title: '描述信息',
    dataIndex: 'desc',
    ellipsis: true,
  }, {
    title: '创建时间',
    dataIndex: 'created_at',
    sorter: (a, b) => a.created_at.localeCompare(b.created_at)
  }, {
    title: '操作',
    width: 180,
    render: (text, record) => (
      <Action>
        <Action.Button onClick={() => store.showInfo(record)}>配置管理</Action.Button>
        <Action.Button onClick={() => store.showForm(record)}>编辑</Action.Button>
        <Action.Button onClick={() => this.handleDelete(record)}>删除</Action.Button>
      </Action>
    )
  }];

  handleDelete = (record) => {
    Modal.confirm({
      title: '删除确认',
      content: `确定要删除任务【${record.name}】吗？`,
      onOk: () => {
        return http.delete('/api/config/task/', {params: {id: record.id}}).then(() => {
          message.success('删除成功');
          store.fetchRecords();
        })
      }
    })
  };

  render() {
    return (
      <AuthDiv auth="config.task.view">
        <Breadcrumb>
          <Breadcrumb.Item>配置中心</Breadcrumb.Item>
          <Breadcrumb.Item>任务管理</Breadcrumb.Item>
        </Breadcrumb>
        <TableCard
          title="任务管理"
          loading={store.loading}
          dataSource={store.dataSource}
          onReload={store.fetchRecords}
          actions={[
            <Button type="primary" icon={<PlusOutlined/>} onClick={() => store.showForm()}>新增</Button>
          ]}
          pagination={{
            showSizeChanger: true,
            showLessItems: true,
            showTotal: total => `共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100']
          }}
          columns={this.columns}
          rowKey="id"/>
        {store.formVisible && <ComForm/>}
        {store.infoVisible && <ComInfo/>}
      </AuthDiv>
    )
  }
}

export default TaskIndex 