.pageContainer {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.recordSection {
  margin-bottom: 24px;

  .recordHeader {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    border-left: 4px solid #1890ff;

    h3 {
      margin: 0 0 8px 0;
      color: #262626;
      font-size: 16px;
      font-weight: 600;
    }

    .recordMeta {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }

  .dataTable {
    .ant-table-thead > tr > th {
      background: #f0f2f5;
      font-weight: 600;
    }

    .ant-table-tbody > tr:hover > td {
      background: #e6f7ff;
    }
  }

  .noData {
    text-align: center;
    padding: 40px;
    color: #999;

    details {
      margin-top: 16px;
      text-align: left;

      summary {
        cursor: pointer;
        color: #1890ff;
        margin-bottom: 8px;
      }

      .rawLog {
        background: #f6f8fa;
        border: 1px solid #e1e4e8;
        border-radius: 4px;
        padding: 12px;
        font-size: 12px;
        max-height: 300px;
        overflow: auto;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
}

.emptyState {
  text-align: center;
  padding: 60px;
  color: #999;
  font-size: 16px;
}

.pageHeader {
  margin-bottom: 24px;
  
  h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: #262626;
  }
  
  p {
    margin: 8px 0 0 0;
    color: #8c8c8c;
    font-size: 14px;
  }
}

.statsRow {
  margin-bottom: 24px;
}

.statCard {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.filterCard {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.toolbarCard {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tableCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tableWrapper {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.advancedTable {
  width: 100%;
  border-collapse: collapse;
  background: white;
  
  .tableHeader {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    padding: 16px 12px;
    text-align: left;
    border-bottom: 2px solid #e8e8e8;
    position: sticky;
    top: 0;
    z-index: 10;
    
    .headerContent {
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: default;
      
      &.sortable {
        cursor: pointer;
        user-select: none;
        
        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          padding: 4px 8px;
          margin: -4px -8px;
        }
      }
    }
    
    .sortIcon {
      margin-left: 8px;
      font-size: 12px;
      opacity: 0.8;
    }
  }
  
  .tableRow {
    transition: all 0.2s ease;
    
    &:hover {
      background: #f5f7fa;
      transform: scale(1.01);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    &:nth-child(even) {
      background: #fafafa;
      
      &:hover {
        background: #f0f4f8;
      }
    }
  }
  
  .tableCell {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
    font-size: 14px;
  }
}

.idCell {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
  color: #1890ff;
  background: #e6f7ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.planName {
  font-weight: 500;
  color: #262626;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.metricsCount {
  font-weight: 600;
  color: #1890ff;
  background: #e6f7ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
}

.metricsProgress {
  display: flex;
  flex-direction: column;
  gap: 4px;
  
  .metricsText {
    font-size: 12px;
    font-weight: 500;
    color: #595959;
  }
  
  .percentageText {
    font-size: 11px;
    color: #8c8c8c;
    text-align: right;
  }
}

.timeCell {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  color: #595959;
  background: #f5f5f5;
  padding: 4px 6px;
  border-radius: 3px;
}

.pagination {
  margin-top: 16px;
  padding: 16px 0;
  text-align: center;
  color: #8c8c8c;
  font-size: 14px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .pageContainer {
    padding: 16px;
  }
  
  .statsRow {
    .statCard {
      margin-bottom: 16px;
    }
  }
  
  .advancedTable {
    font-size: 12px;
    
    .tableHeader {
      padding: 12px 8px;
    }
    
    .tableCell {
      padding: 8px;
    }
  }
}

@media (max-width: 768px) {
  .pageContainer {
    padding: 12px;
  }
  
  .pageHeader {
    h1 {
      font-size: 24px;
    }
  }
  
  .tableWrapper {
    font-size: 11px;
  }
  
  .advancedTable {
    .tableHeader {
      padding: 8px 6px;
    }
    
    .tableCell {
      padding: 6px;
    }
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pageContainer > * {
  animation: fadeIn 0.6s ease-out;
}

.statsRow .statCard:nth-child(1) { animation-delay: 0.1s; }
.statsRow .statCard:nth-child(2) { animation-delay: 0.2s; }
.statsRow .statCard:nth-child(3) { animation-delay: 0.3s; }
.statsRow .statCard:nth-child(4) { animation-delay: 0.4s; }
.statsRow .statCard:nth-child(5) { animation-delay: 0.5s; }
.statsRow .statCard:nth-child(6) { animation-delay: 0.6s; }

/* 滚动条样式 */
.tableWrapper::-webkit-scrollbar {
  height: 8px;
}

.tableWrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.tableWrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  
  &:hover {
    background: #a8a8a8;
  }
}
