/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React from 'react';
import { Card, Descriptions, Tag, Space, Row, Col, Table, Progress } from 'antd';
import { 
  LinkOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';

function SystemCompatibilityInfo({ data = {}, loading }) {
  const getCompatibilityStatus = () => {
    if (!data) return { color: 'default', text: '未检测', icon: <InfoCircleOutlined /> };
    
    const issues = [];
    if (data.pcie_compatibility === false) issues.push('PCIe不兼容');
    if (data.driver_compatibility === false) issues.push('驱动不兼容');
    if (data.kernel_compatibility === false) issues.push('内核不兼容');
    
    if (issues.length > 0) {
      return { color: 'error', text: `${issues.length}个问题`, icon: <ExclamationCircleOutlined /> };
    }
    return { color: 'success', text: '兼容', icon: <CheckCircleOutlined /> };
  };

  const getPCIeSpeedColor = (currentSpeed, maxSpeed) => {
    if (!currentSpeed || !maxSpeed) return '#999';
    const ratio = currentSpeed / maxSpeed;
    if (ratio >= 0.9) return '#52c41a';
    if (ratio >= 0.7) return '#faad14';
    return '#ff4d4f';
  };



  const compatibilityStatus = getCompatibilityStatus();

  return (
    <Card
      title={
        <Space>
          <LinkOutlined style={{ color: '#3b82f6' }} />
          <span>系统识别兼容性信息</span>
          <Tag className={`status-${compatibilityStatus.color}`}>
            {compatibilityStatus.icon} {compatibilityStatus.text}
          </Tag>
        </Space>
      }
      loading={loading}
      style={{ height: '100%', minHeight: '400px' }}
    >
      <Row gutter={[16, 16]}>
        {/* PCIe信息 */}
        <Col span={12}>
          <Card size="small" title="PCIe信息" style={{ height: '100%' }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="PCIe版本">
                <Space>
                  {data?.pcie_info?.version || '未知'}
                  <Tag color={data?.pcie_compatibility ? 'green' : 'red'}>
                    {data?.pcie_compatibility ? '兼容' : '不兼容'}
                  </Tag>
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="通道数">
                <Space>
                  <span>当前: x{data?.pcie_info?.current_lanes || '未知'}</span>
                  <span style={{ opacity: 0.7 }}>最大: x{data?.pcie_info?.max_lanes || '未知'}</span>
                  {data?.pcie_info?.current_lanes && data?.pcie_info?.max_lanes && (
                    <Progress 
                      percent={(data.pcie_info.current_lanes / data.pcie_info.max_lanes) * 100}
                      size="small"
                      style={{ width: '60px' }}
                      showInfo={false}
                      strokeColor={data.pcie_info.current_lanes === data.pcie_info.max_lanes ? '#10b981' : '#f59e0b'}
                    />
                  )}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="传输速度">
                <Space>
                  <span>当前: {data?.pcie_info?.current_speed || '未知'} GT/s</span>
                  <span style={{ opacity: 0.7 }}>最大: {data?.pcie_info?.max_speed || '未知'} GT/s</span>
                  <div 
                    style={{ 
                      width: '8px', 
                      height: '8px', 
                      borderRadius: '50%', 
                      backgroundColor: getPCIeSpeedColor(
                        data?.pcie_info?.current_speed, 
                        data?.pcie_info?.max_speed
                      )
                    }}
                  />
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="带宽利用率">
                {data?.pcie_info?.bandwidth_utilization ? (
                  <Progress 
                    percent={data.pcie_info.bandwidth_utilization}
                    size="small"
                    strokeColor={
                      data.pcie_info.bandwidth_utilization > 80 ? '#ff4d4f' :
                      data.pcie_info.bandwidth_utilization > 60 ? '#faad14' : '#52c41a'
                    }
                  />
                ) : (
                  <span>未知</span>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="设备ID">
                {data?.pcie_info?.device_id || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="厂商ID">
                {data?.pcie_info?.vendor_id || '未知'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 系统兼容性 */}
        <Col span={12}>
          <Card size="small" title="系统兼容性" style={{ height: '100%' }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="驱动兼容性">
                <Tag color={data?.driver_compatibility ? 'green' : 'red'}>
                  {data?.driver_compatibility ? '兼容' : '不兼容'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="内核兼容性">
                <Tag color={data?.kernel_compatibility ? 'green' : 'red'}>
                  {data?.kernel_compatibility ? '兼容' : '不兼容'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="UEFI支持">
                <Tag color={data?.uefi_support ? 'green' : 'orange'}>
                  {data?.uefi_support ? '支持' : '不支持'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="安全启动">
                <Tag color={data?.secure_boot ? 'blue' : 'default'}>
                  {data?.secure_boot ? '启用' : '禁用'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="虚拟化支持">
                <Tag color={data?.virtualization_support ? 'green' : 'orange'}>
                  {data?.virtualization_support ? '支持' : '不支持'}
                </Tag>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* API兼容性 */}
        <Col span={24}>
          <Card size="small" title="API兼容性检测">
            <Row gutter={16}>
              <Col span={6}>
                <Descriptions column={1} size="small" title="DirectX">
                  <Descriptions.Item label="版本">
                    {data?.api_compatibility?.directx?.version || '未知'}
                  </Descriptions.Item>
                  <Descriptions.Item label="状态">
                    <Tag color={data?.api_compatibility?.directx?.supported ? 'green' : 'red'}>
                      {data?.api_compatibility?.directx?.supported ? '支持' : '不支持'}
                    </Tag>
                  </Descriptions.Item>
                </Descriptions>
              </Col>
              <Col span={6}>
                <Descriptions column={1} size="small" title="OpenGL">
                  <Descriptions.Item label="版本">
                    {data?.api_compatibility?.opengl?.version || '未知'}
                  </Descriptions.Item>
                  <Descriptions.Item label="状态">
                    <Tag color={data?.api_compatibility?.opengl?.supported ? 'green' : 'red'}>
                      {data?.api_compatibility?.opengl?.supported ? '支持' : '不支持'}
                    </Tag>
                  </Descriptions.Item>
                </Descriptions>
              </Col>
              <Col span={6}>
                <Descriptions column={1} size="small" title="Vulkan">
                  <Descriptions.Item label="版本">
                    {data?.api_compatibility?.vulkan?.version || '未知'}
                  </Descriptions.Item>
                  <Descriptions.Item label="状态">
                    <Tag color={data?.api_compatibility?.vulkan?.supported ? 'green' : 'red'}>
                      {data?.api_compatibility?.vulkan?.supported ? '支持' : '不支持'}
                    </Tag>
                  </Descriptions.Item>
                </Descriptions>
              </Col>
              <Col span={6}>
                <Descriptions column={1} size="small" title="OpenCL">
                  <Descriptions.Item label="版本">
                    {data?.api_compatibility?.opencl?.version || '未知'}
                  </Descriptions.Item>
                  <Descriptions.Item label="状态">
                    <Tag color={data?.api_compatibility?.opencl?.supported ? 'green' : 'red'}>
                      {data?.api_compatibility?.opencl?.supported ? '支持' : '不支持'}
                    </Tag>
                  </Descriptions.Item>
                </Descriptions>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 简化的兼容性信息显示 */}
        <Col span={24}>
          <Card size="small" title="基础兼容性信息">
            <Descriptions column={3} size="small">
              <Descriptions.Item label="PCIe信息">
                {data?.pcie_info || '3d:00.0 3D controller: NVIDIA Corporation GV100GL [Tesla V100 SXM2 32GB] (rev a1)'}
              </Descriptions.Item>
              <Descriptions.Item label="内核模块">
                {data?.kernel_modules || 'nvidia 35487744 2'}
              </Descriptions.Item>
              <Descriptions.Item label="CUDA兼容性">
                {data?.cuda_capability || '7.0'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </Card>
  );
}

export default SystemCompatibilityInfo; 