/**
 * 测试用例池管理页面
 */
import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Input, 
  Select, 
  message, 
  Modal, 
  Form, 
  Switch,
  Popconfirm,
  Tag,
  Breadcrumb,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  CopyOutlined,
  ExportOutlined,
  ImportOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { AuthDiv, TableCard, SearchForm } from 'components';
import { http, hasPermission } from 'libs';
import { useHistory } from 'react-router-dom';
import styles from './index.module.less';

const { Option } = Select;
const { TextArea } = Input;

export default observer(function TestCaseTemplates() {
  const history = useHistory();
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [filteredTemplates, setFilteredTemplates] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories, setCategories] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadTemplates();

    // 清理函数
    return () => {
      form.resetFields();
    };
  }, []);

  useEffect(() => {
    // 过滤模板
    let filtered = templates;
    
    if (searchText) {
      filtered = filtered.filter(template => 
        template.name.toLowerCase().includes(searchText.toLowerCase()) ||
        (template.description && template.description.toLowerCase().includes(searchText.toLowerCase()))
      );
    }
    
    if (selectedCategory) {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }
    
    setFilteredTemplates(filtered);
  }, [templates, searchText, selectedCategory]);

  const loadTemplates = async () => {
    setLoading(true);
    try {
      const response = await http.get('/api/exec/test-case-templates/');
      
      let templatesData = [];
      if (Array.isArray(response)) {
        templatesData = response;
      } else if (response && Array.isArray(response.data)) {
        templatesData = response.data;
      } else {
        templatesData = [];
      }
      
      setTemplates(templatesData);
      
      // 提取分类
      const uniqueCategories = [...new Set(templatesData.map(t => t.category).filter(Boolean))];
      setCategories(uniqueCategories);
      

    } catch (error) {
      message.error('加载用例模板失败');
      setTemplates([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingTemplate(null);
    form.resetFields();
    form.setFieldsValue({
      enabled: true,
      sort_order: 0
    });
    setModalVisible(true);
  };

  const handleEdit = async (template) => {
    // 先查询模板使用情况
    try {
      const usageResponse = await http.get(`/api/exec/test-case-templates/${template.id}/usage/`);
      const { referencing_sets, count } = usageResponse;

      if (count > 0) {
        Modal.confirm({
          title: '模板使用提醒',
          content: (
            <div>
              <p>此模板正在被 <strong>{count}</strong> 个用例集引用：</p>
              <ul style={{ maxHeight: '200px', overflowY: 'auto' }}>
                {referencing_sets.map(set => (
                  <li key={set.id}>{set.name}</li>
                ))}
              </ul>
              <p style={{ color: '#ff4d4f', marginTop: '12px' }}>
                <strong>⚠️ 注意：</strong>修改此模板将会影响所有引用它的用例集！
              </p>
            </div>
          ),
          okText: '继续编辑',
          cancelText: '取消',
          onOk: () => {
            openEditModal(template);
          }
        });
      } else {
        openEditModal(template);
      }
    } catch (error) {
      openEditModal(template);
    }
  };

  const openEditModal = (template) => {
    setEditingTemplate(template);
    form.setFieldsValue({
      name: template.name,
      description: template.description,
      category: template.category,
      precondition: template.precondition,
      test_steps: template.test_steps,
      expected_result: template.expected_result,
      enabled: template.enabled,
      sort_order: template.sort_order
    });
    setModalVisible(true);
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingTemplate) {
        // 更新用例模板
        await http.put(`/api/exec/test-case-templates/${editingTemplate.id}/`, values);
        message.success('用例模板更新成功');
      } else {
        // 创建新用例模板
        await http.post('/api/exec/test-case-templates/', values);
        message.success('用例模板创建成功');
      }
      
      setModalVisible(false);
      form.resetFields();
      loadTemplates();
    } catch (error) {
      message.error('保存失败');
    }
  };

  const handleDelete = async (template) => {
    // 先查询模板使用情况
    try {
      const usageResponse = await http.get(`/api/exec/test-case-templates/${template.id}/usage/`);
      const { referencing_sets, count } = usageResponse;

      if (count > 0) {
        Modal.confirm({
          title: '删除确认',
          content: (
            <div>
              <p>此模板正在被 <strong>{count}</strong> 个用例集引用：</p>
              <ul style={{ maxHeight: '200px', overflowY: 'auto' }}>
                {referencing_sets.map(set => (
                  <li key={set.id}>{set.name}</li>
                ))}
              </ul>
              <p style={{ color: '#ff4d4f', marginTop: '12px' }}>
                <strong>⚠️ 警告：</strong>删除此模板将导致所有引用它的用例集中的相关用例失效！
              </p>
              <p>确定要删除吗？</p>
            </div>
          ),
          okText: '确定删除',
          okType: 'danger',
          cancelText: '取消',
          onOk: async () => {
            await performDelete(template);
          }
        });
      } else {
        Modal.confirm({
          title: '确定要删除这个用例模板吗？',
          content: '删除后无法恢复',
          okText: '确定删除',
          okType: 'danger',
          cancelText: '取消',
          onOk: async () => {
            await performDelete(template);
          }
        });
      }
    } catch (error) {
      await performDelete(template);
    }
  };

  const performDelete = async (template) => {
    try {
      await http.delete(`/api/exec/test-case-templates/${template.id}/`);
      message.success('用例模板删除成功');
      loadTemplates();
    } catch (error) {
      message.error('删除失败');

    }
  };

  const handleCopy = (template) => {
    setEditingTemplate(null);
    form.setFieldsValue({
      name: `${template.name} - 副本`,
      description: template.description,
      category: template.category,
      precondition: template.precondition,
      test_steps: template.test_steps,
      expected_result: template.expected_result,
      enabled: template.enabled,
      sort_order: template.sort_order
    });
    setModalVisible(true);
  };

  const handleViewUsage = async (template) => {
    try {
      const usageResponse = await http.get(`/api/exec/test-case-templates/${template.id}/usage/`);
      const { referencing_sets, count } = usageResponse;

      Modal.info({
        title: `模板"${template.name}"使用情况`,
        width: 600,
        content: (
          <div>
            {count > 0 ? (
              <div>
                <p>此模板正在被 <strong>{count}</strong> 个用例集引用：</p>
                <ul style={{ maxHeight: '300px', overflowY: 'auto', marginTop: '12px' }}>
                  {referencing_sets.map(set => (
                    <li key={set.id} style={{ marginBottom: '8px' }}>
                      <strong>{set.name}</strong>
                      {set.description && (
                        <div style={{ fontSize: '12px', color: '#666', marginLeft: '16px' }}>
                          {set.description}
                        </div>
                      )}
                      <div style={{ fontSize: '12px', color: '#999', marginLeft: '16px' }}>
                        创建时间: {set.created_at}
                      </div>
                    </li>
                  ))}
                </ul>
                <p style={{ color: '#1890ff', marginTop: '16px' }}>
                  💡 修改此模板将会自动同步到所有引用它的用例集
                </p>
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <div style={{ fontSize: '48px', marginBottom: '16px' }}>📝</div>
                <div style={{ fontSize: '16px', color: '#666' }}>暂无用例集引用此模板</div>
                <div style={{ fontSize: '14px', color: '#999', marginTop: '8px' }}>
                  您可以在用例集编辑器中引入此模板
                </div>
              </div>
            )}
          </div>
        ),
        okText: '关闭'
      });
    } catch (error) {
      message.error('查询使用情况失败');
    }
  };

  const columns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          {record.description && (
            <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
              {record.description}
            </div>
          )}
        </div>
      )
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      render: (text) => text ? <Tag color="blue">{text}</Tag> : '-'
    },
    {
      title: '预置条件',
      dataIndex: 'precondition',
      key: 'precondition',
      width: 200,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text} placement="topLeft">
          <div style={{ 
            maxWidth: '180px', 
            overflow: 'hidden', 
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {text || '-'}
          </div>
        </Tooltip>
      )
    },
    {
      title: '测试步骤',
      dataIndex: 'test_steps',
      key: 'test_steps',
      width: 250,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={<pre style={{ whiteSpace: 'pre-wrap' }}>{text}</pre>} placement="topLeft">
          <div style={{ 
            maxWidth: '230px', 
            overflow: 'hidden', 
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {text || '-'}
          </div>
        </Tooltip>
      )
    },
    {
      title: '预期结果',
      dataIndex: 'expected_result',
      key: 'expected_result',
      width: 200,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text} placement="topLeft">
          <div style={{ 
            maxWidth: '180px', 
            overflow: 'hidden', 
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {text || '-'}
          </div>
        </Tooltip>
      )
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 80,
      render: (enabled) => (
        <Tag color={enabled ? 'green' : 'red'}>
          {enabled ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 80,
      sorter: (a, b) => a.sort_order - b.sort_order
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      sorter: (a, b) => new Date(a.created_at) - new Date(b.created_at)
    },
    {
      title: '操作',
      key: 'action',
      width: 240,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="复制">
            <Button
              type="link"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopy(record)}
            />
          </Tooltip>
          <Tooltip title="查看使用情况">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewUsage(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个用例模板吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <AuthDiv auth="exec.task.do">
      <Breadcrumb style={{ marginBottom: 16 }}>
        <Breadcrumb.Item>首页</Breadcrumb.Item>
        <Breadcrumb.Item>脚本配置中心</Breadcrumb.Item>
        <Breadcrumb.Item>用例池</Breadcrumb.Item>
      </Breadcrumb>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Input
              placeholder="搜索模板名称或描述"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
              allowClear
            />
            <Select
              placeholder="选择分类"
              value={selectedCategory}
              onChange={setSelectedCategory}
              style={{ width: 150 }}
              allowClear
            >
              {categories.map(category => (
                <Option key={category} value={category}>{category}</Option>
              ))}
            </Select>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
            >
              新建模板
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={filteredTemplates}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
          }}
        />
      </Card>

      <Modal
        title={editingTemplate ? '编辑用例模板' : '新建用例模板'}
        visible={modalVisible}
        onOk={handleSave}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        width={800}
        destroyOnClose
      >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              enabled: true,
              sort_order: 0
            }}
          >
          <Form.Item
            name="name"
            label="模板名称"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="请输入模板名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="模板描述"
          >
            <TextArea rows={2} placeholder="请输入模板描述" />
          </Form.Item>
          
          <Form.Item
            name="category"
            label="分类"
          >
            <Input placeholder="请输入分类" />
          </Form.Item>
          
          <Form.Item
            name="precondition"
            label="预置条件"
          >
            <TextArea rows={3} placeholder="请输入预置条件" />
          </Form.Item>
          
          <Form.Item
            name="test_steps"
            label="测试步骤"
            rules={[{ required: true, message: '请输入测试步骤' }]}
          >
            <TextArea rows={4} placeholder="请输入测试步骤，每行一个步骤" />
          </Form.Item>
          
          <Form.Item
            name="expected_result"
            label="预期结果"
            rules={[{ required: true, message: '请输入预期结果' }]}
          >
            <TextArea rows={3} placeholder="请输入预期结果" />
          </Form.Item>
          
          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="enabled"
              label="启用状态"
              valuePropName="checked"
              style={{ flex: 1 }}
            >
              <Switch checkedChildren="启用" unCheckedChildren="禁用" />
            </Form.Item>
            
            <Form.Item
              name="sort_order"
              label="排序"
              style={{ flex: 1 }}
            >
              <Input type="number" placeholder="排序值，数字越小越靠前" />
            </Form.Item>
          </div>
        </Form>
      </Modal>
    </AuthDiv>
  );
});
