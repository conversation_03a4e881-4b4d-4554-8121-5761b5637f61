/* 文档生成器页面样式 */
.container {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundMove 20s ease-in-out infinite;
    z-index: 0;
  }
  
  > * {
    position: relative;
    z-index: 1;
  }
}

@keyframes backgroundMove {
  0%, 100% {
    transform: translateX(0) translateY(0);
  }
  25% {
    transform: translateX(-20px) translateY(-10px);
  }
  50% {
    transform: translateX(20px) translateY(10px);
  }
  75% {
    transform: translateX(-10px) translateY(20px);
  }
}

.headerCard {
  margin-bottom: 24px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  
  :global(.ant-card-body) {
    padding: 40px;
    text-align: center;
  }
  
  h2 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
  }
}

:global(.ant-card) {
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  
  .ant-card-head {
    border-bottom: 1px solid rgba(240, 240, 240, 0.8);
    border-radius: 20px 20px 0 0;
    
    .ant-card-head-title {
      padding: 20px 0;
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }
  
  .ant-card-body {
    padding: 32px;
  }
}

:global(.ant-steps) {
  .ant-steps-item {
    .ant-steps-item-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-color: transparent;
      
      .ant-steps-icon {
        color: white;
      }
    }
    
    &.ant-steps-item-active {
      .ant-steps-item-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: transparent;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }
      
      .ant-steps-item-title {
        color: #667eea;
        font-weight: 600;
      }
    }
    
    &.ant-steps-item-finish {
      .ant-steps-item-icon {
        background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
        border-color: transparent;
      }
      
      .ant-steps-item-title {
        color: #52c41a;
      }
    }
    
    .ant-steps-item-title {
      font-weight: 500;
      font-size: 14px;
    }
    
    .ant-steps-item-description {
      color: #8c8c8c;
      font-size: 12px;
    }
  }
  
  .ant-steps-item-tail::after {
    background: linear-gradient(90deg, #e8e8e8 0%, #f0f0f0 100%);
  }
}

:global(.ant-form) {
  .ant-form-item-label > label {
    font-weight: 600;
    color: #262626;
    font-size: 14px;
  }
  
  .ant-input,
  .ant-select-selector,
  .ant-input-affix-wrapper {
    border-radius: 12px;
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #667eea;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
    }
    
    &:focus,
    &.ant-input-focused,
    &.ant-select-focused .ant-select-selector {
      border-color: #667eea;
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    }
  }
  
  .ant-select-dropdown {
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border: none;
  }
}

:global(.ant-btn) {
  border-radius: 12px;
  font-weight: 500;
  height: auto;
  padding: 12px 24px;
  font-size: 14px;
  transition: all 0.3s ease;
  
  &.ant-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
    
    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      box-shadow: 0 8px 24px rgba(102, 126, 234, 0.6);
      transform: translateY(-2px);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  &.ant-btn-default {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid #f0f0f0;
    backdrop-filter: blur(10px);
    
    &:hover {
      background: rgba(255, 255, 255, 0.95);
      border-color: #667eea;
      color: #667eea;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
  
  &.ant-btn-lg {
    padding: 16px 32px;
    font-size: 16px;
    border-radius: 16px;
  }
}

:global(.ant-alert) {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  
  &.ant-alert-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-left: 4px solid #2196f3;
    
    .ant-alert-icon {
      color: #2196f3;
    }
  }
}

:global(.ant-progress) {
  .ant-progress-bg {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  }
  
  .ant-progress-text {
    color: #667eea;
    font-weight: 600;
  }
}

:global(.ant-spin) {
  .ant-spin-dot {
    .ant-spin-dot-item {
      background-color: #667eea;
    }
  }
}

:global(.ant-divider) {
  border-color: rgba(240, 240, 240, 0.8);
  margin: 32px 0;
}

/* 成功状态样式 */
.successIcon {
  font-size: 64px;
  color: #52c41a;
  margin-bottom: 16px;
  animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 加载状态样式 */
.loadingContainer {
  text-align: center;
  padding: 60px 0;
  
  .ant-spin {
    .ant-spin-dot {
      font-size: 32px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .headerCard {
    margin-bottom: 16px;
    border-radius: 16px;
    
    :global(.ant-card-body) {
      padding: 24px 16px;
    }
    
    h2 {
      font-size: 20px;
    }
  }
  
  :global(.ant-card) {
    border-radius: 16px;
    
    .ant-card-body {
      padding: 20px 16px;
    }
  }
  
  :global(.ant-steps) {
    .ant-steps-item {
      .ant-steps-item-title {
        font-size: 12px;
      }
      
      .ant-steps-item-description {
        font-size: 11px;
      }
    }
  }
  
  :global(.ant-btn) {
    padding: 10px 20px;
    font-size: 13px;
    
    &.ant-btn-lg {
      padding: 14px 28px;
      font-size: 14px;
    }
  }
  
  :global(.ant-form) {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }
}

/* 动画效果 */
:global(.ant-card) {
  animation: cardFadeIn 0.6s ease-out;
}

@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

:global(.ant-steps-item) {
  transition: all 0.3s ease;
}

/* 自定义滚动条 */
.container {
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}
