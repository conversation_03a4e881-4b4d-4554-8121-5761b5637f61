.detailContent {
  .infoCard {
    margin-bottom: 16px;
  }
  
  .statsRow {
    margin-bottom: 16px;
  }
  
  .metricsCard {
    margin-bottom: 16px;
  }
  
  .logCard {
    .logContent {
      max-height: 300px;
      overflow-y: auto;
      background: #f6f8fa;
      border: 1px solid #e1e4e8;
      border-radius: 4px;
      padding: 12px;
      
      pre {
        margin: 0;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
}

.metricValue {
  font-weight: 500;
  
  small {
    color: #666;
    margin-left: 4px;
  }
}

.sourceText {
  font-size: 12px;
  color: #666;
  font-family: monospace;
} 