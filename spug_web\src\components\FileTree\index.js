/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 * 
 * FileTree - 文件树组件
 */
import React, { useState, useEffect, useRef } from 'react';
import { 
  Tree, 
  Button, 
  Space, 
  Modal, 
  Input, 
  message, 
  Dropdown, 
  Menu 
} from 'antd';
import { 
  FolderOutlined, 
  FolderOpenOutlined,
  FileOutlined,
  FileTextOutlined,
  CodeOutlined,
  FileZipOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  PlusOutlined,
  DeleteOutlined,
  ReloadOutlined,
  EditOutlined,
  CopyOutlined,
  CloudServerOutlined,
  CloudSyncOutlined,
  ExportOutlined,
  SwapOutlined
} from '@ant-design/icons';
import { http } from 'libs';
import styles from './index.module.less';

function FileTree({ 
  onSelect,
  onFolderChange,
  onFileMove,
  onFileTransfer,
  onFileDelete,
  selectedPath,
  height = 400,
  showActions = true
}) {
  const [treeData, setTreeData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [contextMenuVisible, setContextMenuVisible] = useState(false);
  const [contextMenuNode, setContextMenuNode] = useState(null);
  const [dragOverNode, setDragOverNode] = useState(null);
  const isMountedRef = useRef(false);

  useEffect(() => {
    isMountedRef.current = true;
    loadFileTree();
    
    // 清理函数
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    if (selectedPath && selectedPath !== selectedKeys[0]) {
      setSelectedKeys([selectedPath]);
    }
  }, [selectedPath, selectedKeys]);

  const loadFileTree = async () => {
    if (!isMountedRef.current) return;
    
    setLoading(true);
    try {
      const response = await http.get('/api/file/tree/');
      if (isMountedRef.current) {
        setTreeData(response.tree || []);
      }
    } catch (error) {
      if (isMountedRef.current) {
        message.error('加载文件树失败');
        console.error('Failed to load file tree:', error);
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  };

  const getFileIcon = (type) => {
    const iconMap = {
      folder: <FolderOutlined style={{ color: '#faad14' }} />,
      remote_root: <CloudServerOutlined style={{ color: '#1890ff' }} />,
      remote_folder: <CloudServerOutlined style={{ color: '#52c41a' }} />,
      text: <FileTextOutlined style={{ color: '#52c41a' }} />,
      code: <CodeOutlined style={{ color: '#1890ff' }} />,
      script: <CodeOutlined style={{ color: '#722ed1' }} />,
      archive: <FileZipOutlined style={{ color: '#fa8c16' }} />,
      image: <FileImageOutlined style={{ color: '#eb2f96' }} />,
      document: <FilePdfOutlined style={{ color: '#f5222d' }} />,
      unknown: <FileOutlined style={{ color: '#8c8c8c' }} />
    };
    return iconMap[type] || iconMap.unknown;
  };

  const renderTreeNode = (node) => {
    if (!node) return null;
    
    const nodeKey = node.key || node.path || node.name || '';
    const icon = node.type === 'folder' ? 
      (expandedKeys.includes(nodeKey) ? <FolderOpenOutlined style={{ color: '#faad14' }} /> : <FolderOutlined style={{ color: '#faad14' }} />) :
      (node.type === 'remote_root' || node.type === 'remote_folder') ?
        <CloudServerOutlined style={{ color: node.type === 'remote_root' ? '#1890ff' : '#52c41a' }} /> :
        getFileIcon(node.type);

    // 获取显示名称，优先使用name，其次title
    const displayName = node.name || node.title || '未知文件';

    return (
      <span className={styles.treeNode}>
        <span className={styles.nodeIcon}>{icon}</span>
        <span className={styles.nodeTitle}>{displayName}</span>
        {node.type === 'folder' && (
          <span className={styles.nodeInfo}>
            {node.folder_count > 0 && <span>{node.folder_count}个文件夹</span>}
            {node.item_count > 0 && <span>{node.item_count}个文件</span>}
          </span>
        )}
        {node.type !== 'folder' && node.size_human && (
          <span className={styles.nodeSize}>{node.size_human}</span>
        )}
      </span>
    );
  };

  const processTreeData = (data) => {
    if (!Array.isArray(data)) {
      return [];
    }
    
    return data.map(item => {
      if (!item) return null;
      
      // 确保每个节点都有key
      const key = item.key || item.path || item.name || `unknown_${Math.random()}`;
      
      return {
        ...item,
        key: key,
        title: renderTreeNode(item),
        children: item.children ? processTreeData(item.children) : undefined
      };
    }).filter(Boolean); // 过滤掉null值
  };

  const onExpand = (expandedKeysValue) => {
    if (isMountedRef.current) {
      setExpandedKeys(expandedKeysValue);
      setAutoExpandParent(false);
    }
  };

  const onTreeSelect = (selectedKeysValue, info) => {
    
    if (isMountedRef.current) {
      setSelectedKeys(selectedKeysValue);
      
      if (selectedKeysValue.length > 0) {
        const selectedKey = selectedKeysValue[0];
        const node = info.node;
        

        
        if (node.type === 'folder') {
          // 选中文件夹时，通知父组件切换目录
          if (onFolderChange) {
            onFolderChange(selectedKey);
          } else {
          }
        } else {
          // 选中文件时，通知父组件
          if (onSelect) {
            onSelect(selectedKey, node);
          } else {
          }
        }
      } else {

      }
    } else {
    }
  };

  const handleRightClick = ({ event, node }) => {
    event.preventDefault();
    if (isMountedRef.current) {
      setContextMenuNode(node);
      setContextMenuVisible(true);
    }
  };

  const handleCreateFolder = () => {
    if (isMountedRef.current) {
      setContextMenuVisible(false);
      
      // 使用Modal.confirm方式
      let tempFolderName = '';
      const parentPath = contextMenuNode ? contextMenuNode.key : '';
      
      Modal.confirm({
        title: '新建文件夹',
        icon: null,
        content: (
          <Input
            placeholder="请输入文件夹名称"
            onChange={(e) => {
              tempFolderName = e.target.value;
            }}
            onPressEnter={() => {
              document.querySelector('.ant-modal-confirm-btns .ant-btn-primary').click();
            }}
          />
        ),
        okText: '创建',
        cancelText: '取消',
        onOk: async () => {
          if (!tempFolderName.trim()) {
            message.error('请输入文件夹名称');
            return Promise.reject('请输入文件夹名称');
          }
          
          try {
            await http.post('/api/file/folder/', {
              path: parentPath,
              name: tempFolderName.trim()
            });
            
            message.success('文件夹创建成功');
            loadFileTree();
            
            // 展开新创建的文件夹的父目录
            if (parentPath && !expandedKeys.includes(parentPath)) {
              setExpandedKeys([...expandedKeys, parentPath]);
            }
            
            return Promise.resolve();
          } catch (error) {
            console.error('Create folder error:', error);
            const errorMsg = error.response?.data?.error || error.message || '创建文件夹失败';
            message.error(errorMsg);
            return Promise.reject(error);
          }
        }
      });
    }
  };

  const handleDeleteFolder = async () => {
    if (!contextMenuNode || contextMenuNode.type !== 'folder') return;
    
    Modal.confirm({
      title: '删除文件夹',
      content: `确定要删除文件夹 "${contextMenuNode.name || contextMenuNode.title || '未知文件夹'}" 吗？只能删除空文件夹。`,
      onOk: async () => {
        try {
          await http.delete('/api/file/folder/', { 
            params: { path: contextMenuNode.key } 
          });
          if (isMountedRef.current) {
            message.success('文件夹删除成功');
            loadFileTree();
          }
        } catch (error) {
          if (isMountedRef.current) {
            message.error(error.response?.data?.error || '删除文件夹失败');
          }
        }
      }
    });
    if (isMountedRef.current) {
      setContextMenuVisible(false);
    }
  };

  const handleRename = () => {
    if (!contextMenuNode) return;
    
    Modal.confirm({
      title: `重命名${contextMenuNode.type === 'folder' ? '文件夹' : '文件'}`,
      content: (
        <Input
          defaultValue={contextMenuNode.name || contextMenuNode.title || ''}
          placeholder="请输入新名称"
          id="rename-input"
        />
      ),
      onOk: async () => {
        const newName = document.getElementById('rename-input').value.trim();
        if (!newName) {
          message.error('名称不能为空');
          return;
        }
        
        const currentName = contextMenuNode.name || contextMenuNode.title || '';
        if (newName === currentName) {
          return; // 名称未改变
        }
        
        try {
          const parentPath = contextMenuNode.key.includes('/') ? 
            contextMenuNode.key.substring(0, contextMenuNode.key.lastIndexOf('/')) : '';
          
          await http.put('/api/file/folder/', {
            source_path: contextMenuNode.key,
            target_path: parentPath,
            name: newName
          });
          
          if (isMountedRef.current) {
            message.success('重命名成功');
            loadFileTree();
            if (onFileMove) {
              onFileMove();
            }
          }
        } catch (error) {
          if (isMountedRef.current) {
            message.error(error.response?.data?.error || '重命名失败');
          }
        }
      }
    });
    if (isMountedRef.current) {
      setContextMenuVisible(false);
    }
  };

  const handleCopy = () => {
    if (!contextMenuNode) return;
    
    // 将路径复制到剪贴板
    navigator.clipboard.writeText(contextMenuNode.key).then(() => {
      if (isMountedRef.current) {
        message.success('路径已复制到剪贴板');
      }
    }).catch(() => {
      if (isMountedRef.current) {
        message.error('复制失败');
      }
    });
    if (isMountedRef.current) {
      setContextMenuVisible(false);
    }
  };

  const handleFileTransfer = () => {
    if (!contextMenuNode || contextMenuNode.type === 'folder') return;
    
    // 确保传递的是字符串文件名，而不是React元素
    const fileName = contextMenuNode.name || contextMenuNode.key?.split('/').pop() || '';
    
    if (onFileTransfer && fileName) {
      onFileTransfer(fileName, contextMenuNode);
    }
    if (isMountedRef.current) {
      setContextMenuVisible(false);
    }
  };

  const handleFileDelete = () => {
    if (!contextMenuNode || contextMenuNode.type === 'folder') return;
    
    // 确保传递的是字符串文件名，而不是React元素
    const fileName = contextMenuNode.name || contextMenuNode.key?.split('/').pop() || '';
    Modal.confirm({
      title: '删除文件',
      content: `确定要删除文件 "${fileName}" 吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        if (onFileDelete) {
          await onFileDelete(fileName, contextMenuNode);
        }
      }
    });
    
    if (isMountedRef.current) {
      setContextMenuVisible(false);
    }
  };

  const contextMenu = (
    <Menu>
      {contextMenuNode && contextMenuNode.type === 'folder' && (
        <Menu.Item key="create" icon={<PlusOutlined />} onClick={handleCreateFolder}>
          新建子文件夹
        </Menu.Item>
      )}
      {contextMenuNode && (
        <>
          {contextMenuNode.type === 'folder' && <Menu.Divider />}
          
          {contextMenuNode.type !== 'folder' && (
            <>
              <Menu.Item key="transfer" icon={<SwapOutlined />} onClick={handleFileTransfer}>
                转移文件
              </Menu.Item>
              <Menu.Divider />
            </>
          )}
          
          <Menu.Item key="rename" icon={<EditOutlined />} onClick={handleRename}>
            重命名
          </Menu.Item>
          <Menu.Item key="copy" icon={<CopyOutlined />} onClick={handleCopy}>
            复制路径
          </Menu.Item>
          
          {contextMenuNode.type === 'folder' ? (
            <Menu.Item key="delete" icon={<DeleteOutlined />} onClick={handleDeleteFolder} danger>
              删除文件夹
            </Menu.Item>
          ) : (
            <Menu.Item key="delete" icon={<DeleteOutlined />} onClick={handleFileDelete} danger>
              删除文件
            </Menu.Item>
          )}
        </>
      )}
    </Menu>
  );

  const handleRefresh = () => {
    loadFileTree();
    if (isMountedRef.current) {
      message.success('文件树已刷新');
    }
  };

  const handleCreateRootFolder = () => {
    if (isMountedRef.current) {
      setContextMenuNode(null);
      // 使用Modal.confirm替代Modal组件
      let tempFolderName = '';
      Modal.confirm({
        title: '新建文件夹',
        icon: null,
        content: (
          <Input
            placeholder="请输入文件夹名称"
            onChange={(e) => {
              tempFolderName = e.target.value;
            }}
            onPressEnter={() => {
              document.querySelector('.ant-modal-confirm-btns .ant-btn-primary').click();
            }}
          />
        ),
        okText: '创建',
        cancelText: '取消',
        onOk: async () => {
          if (!tempFolderName.trim()) {
            message.error('请输入文件夹名称');
            return Promise.reject('请输入文件夹名称');
          }
          
          try {
            // 根目录路径为空字符串
            const parentPath = '';
            
            await http.post('/api/file/folder/', {
              path: parentPath,
              name: tempFolderName.trim()
            });
            
            message.success('文件夹创建成功');
            loadFileTree();
            return Promise.resolve();
          } catch (error) {
            console.error('Create folder error:', error);
            const errorMsg = error.response?.data?.error || error.message || '创建文件夹失败';
            message.error(errorMsg);
            return Promise.reject(error);
          }
        }
      });
    }
  };

  // 拖拽处理函数
  const onDragStart = (info) => {

    if (!info || !info.node) {
      console.warn('Invalid drag start info:', info);
      return false;
    }
  };

  const onDragEnter = (info) => {
    console.log('Drag enter:', info);
    if (info && info.node && isMountedRef.current) {
      setDragOverNode(info.node);
    }
  };

  const onDragOver = (info) => {
    console.log('Drag over:', info);
  };

  const onDragLeave = (info) => {
    console.log('Drag leave:', info);
    if (isMountedRef.current) {
      setDragOverNode(null);
    }
  };

  const onDrop = async (info) => {
    console.log('Drop:', info);
    if (isMountedRef.current) {
      setDragOverNode(null);
    }
    
    const dragNode = info.dragNode;
    const dropNode = info.node;
    const dropPosition = info.dropPosition;
    const dropToGap = info.dropToGap;

    // 检查参数有效性
    if (!dragNode || !dropNode || !dragNode.key || !dropNode.key) {
      if (isMountedRef.current) {
        message.error('拖拽参数无效');
      }
      return;
    }

    // 不允许拖拽到自己身上
    if (dragNode.key === dropNode.key) {
      if (isMountedRef.current) {
        message.warning('不能移动到自身');
      }
      return;
    }

    // 不允许文件夹拖拽到自己的子目录
    if (dropNode.key.startsWith(dragNode.key + '/')) {
      if (isMountedRef.current) {
        message.warning('不能移动到自己的子目录');
      }
      return;
    }

    let targetPath = '';
    
    // 如果拖拽到文件夹上（不是间隙）
    if (dropNode.type === 'folder' && !dropToGap) {
      targetPath = dropNode.key;
    } else if (dropToGap) {
      // 如果拖拽到间隙中，移动到父目录
      const dropParentPath = dropNode.key.includes('/') ? 
        dropNode.key.substring(0, dropNode.key.lastIndexOf('/')) : '';
      targetPath = dropParentPath;
    } else {
      // 拖拽到文件上，移动到该文件的父目录
      const dropParentPath = dropNode.key.includes('/') ? 
        dropNode.key.substring(0, dropNode.key.lastIndexOf('/')) : '';
      targetPath = dropParentPath;
    }

    try {
      const fileName = dragNode.key.includes('/') ? 
        dragNode.key.substring(dragNode.key.lastIndexOf('/') + 1) : dragNode.key;
      
      await http.put('/api/file/folder/', {
        source_path: dragNode.key,
        target_path: targetPath,
        name: fileName
      });

      if (isMountedRef.current) {
        message.success('移动成功');
        loadFileTree();
        
        // 通知父组件刷新文件列表
        if (onFileMove) {
          onFileMove();
        }
      }
    } catch (error) {
      if (isMountedRef.current) {
        message.error(error.response?.data?.error || '移动失败');
      }
    }
  };

  // 判断节点是否可以拖拽
  const allowDrop = (info) => {
    const { dragNode, node, dropPosition } = info;
    
    // 检查参数有效性
    if (!dragNode || !node || !dragNode.key || !node.key) {
      return false;
    }
    
    // 不允许拖拽到自身
    if (dragNode.key === node.key) {
      return false;
    }
    
    // 不允许文件夹拖拽到自己的子目录
    if (node.key.startsWith(dragNode.key + '/')) {
      return false;
    }
    
    return true;
  };

  // 处理从文件列表拖拽到文件树的操作
  const handleExternalDrop = async (e) => {
    e.preventDefault();
    
    try {
      const dragData = JSON.parse(e.dataTransfer.getData('text/plain'));
      if (!dragData || !dragData.path) return;

      // 这里可以添加逻辑来确定拖拽的目标位置
      // 现在简单地移动到根目录
      const targetPath = '';
      
      await http.put('/api/file/folder/', {
        source_path: dragData.path,
        target_path: targetPath,
        name: dragData.name
      });

      if (isMountedRef.current) {
        message.success('移动成功');
        loadFileTree();
        
        if (onFileMove) {
          onFileMove();
        }
      }
    } catch (error) {
      console.error('External drop error:', error);
    }
  };

  return (
    <div className={styles.fileTree}>
      {showActions && (
        <div className={styles.toolbar}>
          <Space>
            <Button
              type="primary"
              size="small"
              icon={<PlusOutlined />}
              onClick={handleCreateRootFolder}
            >
              新建文件夹
            </Button>
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            >
              刷新
            </Button>
          </Space>
        </div>
      )}
      
      <div 
        className={styles.treeContainer} 
        style={{ height }}
        onDragOver={(e) => e.preventDefault()}
        onDrop={handleExternalDrop}
      >
        <Dropdown
          overlay={contextMenu}
          trigger={['contextMenu']}
          open={contextMenuVisible}
          onOpenChange={(open) => {
            if (isMountedRef.current) {
              setContextMenuVisible(open);
            }
          }}
        >
          <div>
            <Tree
              className={styles.tree}
              showLine
              showIcon={false}
              draggable
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              onSelect={onTreeSelect}
              selectedKeys={selectedKeys}
              onRightClick={handleRightClick}
              treeData={processTreeData(treeData)}
              loading={loading}
              onDragStart={onDragStart}
              onDragEnter={onDragEnter}
              onDragOver={onDragOver}
              onDragLeave={onDragLeave}
              onDrop={onDrop}
              allowDrop={allowDrop}
            />
            {!loading && (!treeData || treeData.length === 0) && (
              <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                暂无文件或文件夹
              </div>
            )}
          </div>
        </Dropdown>
      </div>
    </div>
  );
}

export default FileTree;
