/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React from 'react';
import { observer } from 'mobx-react';
import { Menu, Input, Button, PageHeader, Modal, Space, Radio, Form } from 'antd';
import {
  DiffOutlined,
  TableOutlined,
  PlusOutlined
} from '@ant-design/icons';
import packageStore from '../package/store';
import styles from './index.module.css';
import history from 'libs/history';
import { AuthDiv, AuthButton, Breadcrumb } from 'components';
import DiffConfig from './DiffConfig';
import TableView from './TableView';
import store from './store';

@observer
class Index extends React.Component {
  constructor(props) {
    super(props);
    this.textView = null;
    this.JSONView = null;
    this.state = {
      view: '1'
    }
  }

  componentDidMount() {
    const {type, id} = this.props.match.params;
    store.initial(type, id)
      .then(() => {
        // 根据类型加载不同的数据
        if (type === 'task') {
          // 任务类型加载配套数据
          if (packageStore.records.length === 0) {
            packageStore.fetchRecords().then(() => {
              if (packageStore.records.length === 0) {
                Modal.error({
                  title: '无可用配套',
                  content: <div>任务配置依赖配套，请在 <a href="/config/environment">配套管理</a> 中创建配套。</div>
                })
              } else {
                this.updateEnv()
              }
            })
          } else {
            this.updateEnv()
          }
        }
      })
  }

  updateEnv = (env) => {
    if (store.type === 'task') {
      store.package = env || packageStore.records[0] || {};
    }
    this.handleRefresh()
  };

  handleRefresh = () => {
    store.fetchRecords().then(() => {
      if (this.textView) this.textView.updateValue();
      if (this.JSONView) this.JSONView.updateValue();
    })
  };

  getBreadcrumbText = () => {
    return '任务管理';
  };

  getPageTitle = () => {
    return '配套列表';
  };

  getRecords = () => {
    return packageStore.records;
  };

  getKeyName = () => {
    return '版本号';
  };

  render() {
    const {view} = this.state;
    const records = this.getRecords();
    const currentItem = store.package;
    
    return (
      <AuthDiv auth={`config.${store.type}.view_config`}>
        <Breadcrumb>
          <Breadcrumb.Item>配置中心</Breadcrumb.Item>
          <Breadcrumb.Item onClick={() => history.goBack()}>{this.getBreadcrumbText()}</Breadcrumb.Item>
          <Breadcrumb.Item>{store.obj.name}</Breadcrumb.Item>
        </Breadcrumb>
        <div className={styles.container}>
          <div className={styles.left}>
            <PageHeader
              title={this.getPageTitle()}
              style={{padding: '0 0 10px 10px'}}
              onBack={() => history.goBack()}
              extra={<Button type="link" icon={<DiffOutlined/>} onClick={store.showDiff}>对比配置</Button>}/>
            <Menu
              mode="inline"
              selectedKeys={[String(currentItem.id)]}
              style={{border: 'none'}}
              onSelect={({item}) => this.updateEnv(item.props.env)}>
              {records.map(item => (
                <Menu.Item key={item.id} env={item}>{item.name} ({item.key})</Menu.Item>
              ))}
            </Menu>
          </div>
          <div className={styles.right}>
            <Form layout="inline" style={{marginBottom: 16}}>
              <Form.Item label="视图" style={{paddingLeft: 0}}>
                <Radio.Group value={view} onChange={e => this.setState({view: e.target.value})}>
                  <Radio.Button value="1"><TableOutlined title="表格视图"/></Radio.Button>
                </Radio.Group>
              </Form.Item>
              <Form.Item label={this.getKeyName()}>
                <Input allowClear value={store.f_name} onChange={e => store.f_name = e.target.value}
                       placeholder="请输入"/>
              </Form.Item>
              <Space style={{flex: 1, justifyContent: 'flex-end'}}>
                <AuthButton
                  auth={`config.${store.type}.edit_config`}
                  disabled={view !== '1'}
                  type="primary"
                  icon={<PlusOutlined/>}
                  onClick={() => store.showForm()}>
                  新增版本配套
                </AuthButton>
              </Space>
            </Form>

            {view === '1' && <TableView/>}
          </div>
        </div>
        {store.diffVisible && <DiffConfig/>}
      </AuthDiv>
    )
  }
}

export default Index
