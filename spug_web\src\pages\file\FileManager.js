/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Space, 
  Upload, 
  message, 
  Card,
  Row,
  Col,
  Tag,
  Breadcrumb,
  Spin,
  Tooltip,
  Popconfirm,
  Modal,
  Input,
  Form,
  Tabs,
  Checkbox,
  Menu,
  Alert
} from 'antd';
import { 
  InboxOutlined,
  UploadOutlined,
  PlusOutlined,
  ReloadOutlined,
  DownloadOutlined,
  EditOutlined,
  DeleteOutlined,
  FileOutlined,
  FolderOutlined,
  CodeOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  FileZipOutlined,
  FileTextOutlined,
  CloudServerOutlined,
  ArrowLeftOutlined,
  SwapOutlined
} from '@ant-design/icons';
import { http, X_TOKEN } from 'libs';
import FolderTreeSelect from '../../components/FolderTreeSelect';
import styles from './FileManager.module.less';
import FileTree from '../../components/FileTree';
import DragUpload from '../../components/DragUpload';
import { observer } from 'mobx-react';

function FileManager(props) {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [currentPath, setCurrentPath] = useState('');
  const [selectedFilePath, setSelectedFilePath] = useState('');
  
  // 远程文件夹相关状态
  const [activeTab, setActiveTab] = useState('local');
  const [remoteModalVisible, setRemoteModalVisible] = useState(false);
  const [connectedRemotePath, setConnectedRemotePath] = useState('');
  const [remoteCredentials, setRemoteCredentials] = useState(null);
  const [remoteForm] = Form.useForm();
  const [localPathForm] = Form.useForm(); // 添加本地路径选择表单
  const [cacheStatus, setCacheStatus] = useState({});
  const [refreshingCache, setRefreshingCache] = useState(false);
  const [preloadingTree, setPreloadingTree] = useState(false);
  const [showCacheStatus, setShowCacheStatus] = useState(false);
  const [selectedFolder, setSelectedFolder] = useState(null);
  const [remoteFolders, setRemoteFolders] = useState([]);
  const [currentFolder, setCurrentFolder] = useState(null);
  const [filteredFiles, setFilteredFiles] = useState([]);
  const [searchValue] = useState('');
  
  // 远程文件夹管理状态
  const [remoteFolderManageVisible, setRemoteFolderManageVisible] = useState(false);
  const [editingFolder, setEditingFolder] = useState(null);
  const [showFolderForm, setShowFolderForm] = useState(false);
  const [folderForm] = Form.useForm();

  useEffect(() => {
    loadFiles();
    loadCacheStatus();
    fetchRemoteFolders();
  }, []);

  useEffect(() => {
    if (searchValue) {
      const filtered = files.filter(file => file.name.toLowerCase().includes(searchValue.toLowerCase()));
      setFilteredFiles(filtered);
    } else {
      setFilteredFiles(files);
    }
  }, [searchValue, files]);
  
  const loadCacheStatus = async () => {
    try {
      const response = await http.get('/api/file/remote/cache/');
      setCacheStatus(response);
    } catch (error) {
      console.error('获取缓存状态失败:', error);
    }
  };

  const loadFiles = async (path = currentPath) => {
    setLoading(true);    
    try {
      let response;
      if (activeTab === 'remote' && connectedRemotePath && remoteCredentials) {
        // 加载远程文件夹（使用临时连接API）
        const params = {
          remote_path: remoteCredentials.remote_path,
          username: remoteCredentials.username,
          password: remoteCredentials.password,
          domain: remoteCredentials.domain
        };
        if (path) params.path = path;
        response = await http.get('/api/file/remote/temp/', { params });
        
        // 显示缓存状态信息
        if (response.cache_status) {
        }
      } else if (activeTab === 'local') {
        // 加载本地文件
        const params = path ? { path } : {};
        response = await http.get('/api/file/manager/', { params });
      } else {
        // 其他情况，清空文件列表
        setFiles([]);
        setCurrentPath('');
        return;
      }
      
      setFiles(response.files || []);
      
      // 确保正确设置当前路径
      const newCurrentPath = response.current_path !== undefined ? response.current_path : (path || '');
      setCurrentPath(newCurrentPath);
    } catch (error) {
      console.error('加载文件列表失败:', error);
      message.error(`加载文件列表失败: ${error.response?.data?.error || error.message || '请检查网络连接'}`);
      setFiles([]);
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async (options) => {
    const { file, onSuccess, onError, onProgress } = options;
    
    const formData = new FormData();
    formData.append('file', file);
    if (currentPath) {
      formData.append('path', currentPath);
    }
    
    setUploadLoading(true);
    try {
      const response = await http.post('/api/file/manager/', formData, {
        onUploadProgress: (progressEvent) => {
          const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress({ percent });
        }
      });
      
      message.success(response.message);
      onSuccess(response);
      loadFiles(); // 重新加载文件列表
    } catch (error) {
      message.error('文件上传失败');
      onError(error);
    } finally {
      setUploadLoading(false);
    }
  };

  // 处理拖拽上传
  const handleDragUpload = async (fileList) => {
    if (activeTab === 'remote') {
      // 调用远程上传函数
      handleRemoteUpload(fileList);
      return;
    }
    
    setUploadLoading(true);
    try {
      const uploadPromises = fileList.map(file => {
        const formData = new FormData();
        formData.append('file', file);
        if (currentPath) {
          formData.append('path', currentPath);
        }
        return http.post('/api/file/manager/', formData);
      });
      
      await Promise.all(uploadPromises);
      message.success(`成功上传 ${fileList.length} 个文件`);
      loadFiles(); // 重新加载文件列表
    } catch (error) {
      message.error('文件上传失败');
    } finally {
      setUploadLoading(false);
    }
  };

  const handleDelete = async (filename) => {
    try {
      const record = files.find(f => f.name === filename);
      const filePath = currentPath ? `${currentPath}/${filename}` : filename;
      
      if (record && record.type === 'folder') {
        // 删除文件夹
        await http.delete('/api/file/folder/', { params: { path: filePath } });
        message.success('文件夹删除成功');
      } else {
        // 删除文件
        await http.delete('/api/file/manager/', { params: { filename: filePath } });
        message.success('文件删除成功');
      }
      loadFiles();
    } catch (error) {
      message.error(error.response?.data?.error || '删除失败');
    }
  };

  const handleDeleteFile = async (fileName, node) => {
    try {
      
      // 从节点路径中提取目录路径
      const filePath = node.key || '';
      const directory = filePath.includes('/') ? filePath.substring(0, filePath.lastIndexOf('/')) : '';
      
      const response = await http.delete('/api/file/manager/', {
        params: {
          filename: fileName,
          path: directory
        }
      });
      
      message.success(response.message || '文件删除成功');
      loadFiles(); // 重新加载文件列表
    } catch (error) {
      message.error(`删除失败: ${error.response?.data?.error || error.message}`);
    }
  };

  const handleDownload = async (filename) => {
    if (activeTab === 'remote') {
      // 远程文件下载到本地
      try {
        const filePath = currentPath ? `${currentPath}/${filename}` : filename;
        
        const response = await http.post('/api/file/remote/temp/', {
          remote_file_path: filePath,
          local_path: '',
          remote_path: remoteCredentials?.remote_path,
          username: remoteCredentials?.username,
          password: remoteCredentials?.password,
          domain: remoteCredentials?.domain
        });
        
        message.success(`文件 ${filename} 已下载到本地文件管理器。切换到"本地文件"标签页可以查看。`);
        
      } catch (error) {
        console.error('下载失败:', error);
        message.error(`下载失败: ${error.response?.data?.error || error.message || '未知错误'}`);
      }
    } else {
      // 本地文件直接下载
      const filePath = currentPath ? `${currentPath}/${filename}` : filename;
      window.open(`/api/file/download/${encodeURIComponent(filePath)}/`, '_blank');
    }
  };

  // 处理文件夹导航
  const handleFolderChange = (path) => {
    setCurrentPath(path);
    loadFiles(path);
  };

  // 处理文件树文件选择
  const handleFileSelect = (filePath, node) => {

    
    setSelectedFilePath(filePath);
    
    // 如果选择的是远程文件夹，切换到远程模式
    if (node.type === 'remote_folder') {
      const folderId = node.folder_id;
      
      if (folderId) {
        handleConnectToSavedRemoteFolder(folderId);
      } else {
        console.error('  - ❌ 远程文件夹缺少folder_id');
        message.error('远程文件夹配置缺少ID信息');
      }
    } else {
      console.log('  - 非远程文件夹，跳过处理');
    }
    console.log('🔍 文件树选择调试 - 结束');
  };

  // 处理文件移动后的刷新
  const handleFileMove = () => {
    message.success('文件移动功能开发中...');
  };

  // 远程文件夹连接功能
  const handleConnectRemote = () => {
    setRemoteModalVisible(true);
    remoteForm.resetFields();
  };

  // 远程文件夹管理功能
  const handleManageRemoteFolders = () => {
    setRemoteFolderManageVisible(true);
    fetchRemoteFolders();
  };

  const handleEditRemoteFolder = (folder) => {
    setEditingFolder(folder);
    setShowFolderForm(true);
    folderForm.setFieldsValue({
      name: folder.name,
      remote_path: folder.remote_path,
      username: folder.username,
      domain: folder.domain,
      description: folder.description
    });
  };

  const handleDeleteRemoteFolder = async (folderId) => {
    try {
      await http.delete('/api/file/remote/', { params: { id: folderId } });
      message.success('远程文件夹配置删除成功');
      fetchRemoteFolders();
    } catch (error) {
      message.error(`删除失败: ${error.response?.data?.error || error.message}`);
    }
  };

  const handleAddRemoteFolder = () => {
    setEditingFolder(null);
    setShowFolderForm(true);
    folderForm.resetFields();
  };

  const handleSaveRemoteFolder = async () => {
    try {
      const values = await folderForm.validateFields();
      
      if (editingFolder) {
        // 编辑
        await http.put('/api/file/remote/', {
          id: editingFolder.id,
          ...values
        });
        message.success('远程文件夹配置更新成功');
      } else {
        // 新增
        await http.post('/api/file/remote/', values);
        message.success('远程文件夹配置创建成功');
      }
      
      setRemoteFolderManageVisible(false);
      setEditingFolder(null);
      setShowFolderForm(false);
      folderForm.resetFields();
      fetchRemoteFolders();
    } catch (error) {
      message.error(`操作失败: ${error.response?.data?.error || error.message}`);
    }
  };

  const handleRemoteConnect = async () => {
    try {
      console.log('开始连接远程文件夹...');
      const values = await remoteForm.validateFields();
      console.log('表单验证通过，准备发送请求:', values);
      
      // 测试连接
      const response = await http.post('/api/file/remote/connect/', {
        remote_path: values.remote_path,
        username: values.username,
        password: values.password,
        domain: values.domain,
        save_as_persistent: values.save_as_persistent,
        folder_name: values.folder_name,
        description: values.description
      });
      
      console.log('服务器响应:', response);
      
      if (response.success) {
        setConnectedRemotePath(values.remote_path);
        setRemoteCredentials(response.credentials);
        setActiveTab('remote');
        setCurrentPath('');
        setRemoteModalVisible(false);
        remoteForm.resetFields();
        message.success(response.message || '远程文件夹连接成功！');
        
        // 如果保存了持久目录，自动预加载文件树
        if (response.saved_folder_id) {
          message.info('系统将自动预加载文件树，请稍候...');
          
          // 延迟执行预加载，给服务器一点时间
          setTimeout(async () => {
            try {
              setPreloadingTree(true);
              await http.post('/api/file/remote/cache/', {
                action: 'cache_file_tree',
                folder_id: response.saved_folder_id
              });
              message.success('文件树预加载完成！');
              loadCacheStatus();
            } catch (error) {
              console.error('预加载文件树失败:', error);
            } finally {
              setPreloadingTree(false);
            }
          }, 1000);
          
          // 刷新页面以更新文件树
          setTimeout(() => {
            window.location.reload();
          }, 5000);
        }
        
        // 设置初始文件列表
        if (response.files) {
          setFiles(response.files);
        } else {
          loadFiles(''); // 加载远程文件夹根目录
        }
      } else {
        message.error(response.message || '连接失败');
      }
    } catch (error) {
      console.error('连接异常:', error);
      message.error(`连接失败: ${error.response?.data?.error || error.message || '请检查路径和凭据'}`);
    }
  };

  const handleConnectToSavedRemoteFolder = async (folderId) => {
    try {

      
      // 获取远程文件夹配置
      const folderResponse = await http.get('/api/file/remote/');
      const folder = folderResponse.find(f => f.id === folderId);

      
      if (!folder) {
        console.error('  - ❌ 未找到匹配的远程文件夹');

        message.error('远程文件夹配置不存在');
        return;
      }

      
      // 尝试连接
      const connectData = {
        remote_path: folder.remote_path,
        username: folder.username,
        password: folder.password,
        domain: folder.domain
      };
      console.log('  - 连接参数:', {
        ...connectData,
        password: connectData.password ? '***有密码***' : '空'
      });
      
      const response = await http.post('/api/file/remote/connect/', connectData);
      

      
      if (response.success) {
        setConnectedRemotePath(folder.remote_path);
        setRemoteCredentials({
          remote_path: folder.remote_path,
          username: folder.username,
          password: folder.password,
          domain: folder.domain
        });
        setActiveTab('remote');
        setCurrentPath('');
        
        // 设置初始文件列表
        if (response.files) {
          console.log('  - 设置初始文件列表，文件数量:', response.files.length);
          setFiles(response.files);
        } else {
          console.log('  - 响应中无文件列表，调用loadFiles加载');
          loadFiles('');
        }
        
        message.success(`已连接到远程文件夹: ${folder.name}`);
      } else {
        console.error('  - ❌ 连接失败:', response.message);
        message.error(response.message || '连接失败');
      }
      
      console.log('🔗 连接远程文件夹调试 - 结束');
      
    } catch (error) {
      console.error('🔗 连接远程文件夹异常 - 错误详情:');
      console.error('  - error对象:', error);
      console.error('  - error.message:', error.message);
      console.error('  - error.response:', error.response);
      console.error('  - error.response?.data:', error.response?.data);
      console.error('  - error.response?.status:', error.response?.status);
      
      message.error(`连接失败: ${error.response?.data?.error || error.message || '未知错误'}`);
    }
  };

  const handleTabChange = (key) => {
    
    // 如果切换到相同标签页，不需要处理
    if (key === activeTab) return;
    
    setActiveTab(key);
    setCurrentPath(''); // 重置到根目录
    setFiles([]); // 清空当前文件列表
    
    if (key === 'local') {
      // 切换到本地文件模式
      console.log('切换到本地文件模式');
      setConnectedRemotePath('');
      setRemoteCredentials(null);
      
      // 立即加载本地文件
      setTimeout(() => {
        loadFiles('');
      }, 50);
    } else if (key === 'remote') {
      // 切换到远程文件模式
      if (connectedRemotePath && remoteCredentials) {
        // 如果已经连接了远程文件夹，加载文件列表
        setTimeout(() => {
          loadFiles('');
        }, 50);
      } else {
        // 如果没有连接远程文件夹，显示空列表
        setFiles([]);
      }
    }
  };

  const handleRefreshCache = async () => {
    setRefreshingCache(true);
    try {
      const response = await http.post('/api/file/remote/cache/', {
        action: 'refresh_all'
      });
      
      if (response.message) {
        message.success(response.message);
        loadCacheStatus(); // 刷新缓存状态
        
        // 如果当前在远程模式，重新加载文件列表
        if (activeTab === 'remote') {
          loadFiles(currentPath);
        }
      }
    } catch (error) {
      message.error(`刷新缓存失败: ${error.response?.data?.error || error.message}`);
    } finally {
      setRefreshingCache(false);
    }
  };

  // 预加载整个文件树
  const handlePreloadFileTree = async () => {
    if (!remoteCredentials || !connectedRemotePath) {
      message.error('请先连接到远程文件夹');
      return;
    }
    
    setPreloadingTree(true);
    try {
      // 查找对应的远程文件夹ID
      const folderResponse = await http.get('/api/file/remote/');
      const folder = folderResponse.find(f => f.remote_path === remoteCredentials.remote_path);
      
      if (!folder) {
        message.error('未找到对应的远程文件夹配置，请先保存为持久目录');
        return;
      }
      
      // 开始预加载
      const response = await http.post('/api/file/remote/cache/', {
        action: 'cache_file_tree',
        folder_id: folder.id
      });
      
      if (response.message) {
        message.success(response.message);
        loadCacheStatus(); // 刷新缓存状态
      }
    } catch (error) {
      message.error(`预加载文件树失败: ${error.response?.data?.error || error.message}`);
    } finally {
      setPreloadingTree(false);
    }
  };

  // 判断是否为可编辑文件
  const isEditableFile = (filename, type) => {
    const editableTypes = ['text', 'code', 'script'];
    const editableExts = ['.txt', '.md', '.log', '.conf', '.cfg', '.sh', '.py', '.js', '.json', '.html', '.css', '.xml', '.yml', '.yaml', '.ini', '.properties'];
    const ext = filename.substring(filename.lastIndexOf('.')).toLowerCase();
    return editableTypes.includes(type) || editableExts.includes(ext) || !filename.includes('.');
  };

  // 编辑文件 - 在新标签页打开
  const handleEditFile = (filename) => {
    console.log('点击编辑文件:', filename); // 调试日志
    
    const filePath = currentPath ? `${currentPath}/${filename}` : filename;
    const url = `/file/editor?path=${encodeURIComponent(filePath)}`;
    window.open(url, '_blank');
  };

  // 新增脚本功能
  const handleCreateScript = () => {
    let fileName = '';
    let fileContent = '';
    
    // 第一步：输入文件名
    Modal.confirm({
      title: '新建文件',
      content: (
        <div>
          <p>请输入文件名：</p>
          <Input 
            placeholder="例如: script.sh, config.txt"
            onChange={(e) => fileName = e.target.value}
            onPressEnter={() => {
              if (fileName.trim()) {
                document.querySelector('.ant-modal-confirm-btns .ant-btn-primary').click();
              }
            }}
          />
        </div>
      ),
      onOk: () => {
        if (!fileName.trim()) {
          message.error('请输入文件名');
          return Promise.reject();
        }
        
        // 第二步：输入文件内容
        return new Promise((resolve, reject) => {
          Modal.confirm({
            title: `编辑文件: ${fileName}`,
            width: 800,
            content: (
              <div>
                <p>请输入文件内容：</p>
                <Input.TextArea
                  rows={15}
                  placeholder="请输入文件内容..."
                  onChange={(e) => fileContent = e.target.value}
                  style={{ 
                    fontFamily: 'monospace',
                    fontSize: '14px'
                  }}
                />
              </div>
            ),
            onOk: async () => {
              try {
                // 创建文件并上传
                const blob = new Blob([fileContent], { type: 'text/plain' });
                const file = new File([blob], fileName, { type: 'text/plain' });
                
                const formData = new FormData();
                formData.append('file', file);
                if (currentPath) {
                  formData.append('path', currentPath);
                }
                
                await http.post('/api/file/manager/', formData);
                message.success(`文件 ${fileName} 创建成功`);
                loadFiles();
                resolve();
              } catch (error) {
                message.error('创建文件失败');
                reject(error);
              }
            },
            onCancel: () => reject()
          });
        });
      }
    });
  };

  // 返回上级目录
  const handleGoUpDirectory = () => {
    if (!currentPath) return;
    
    const pathParts = currentPath.split('/').filter(Boolean);
    if (pathParts.length > 0) {
      const parentPath = pathParts.slice(0, -1).join('/');
      handleFolderChange(parentPath);
    }
  };

  // 获取面包屑导航
  const getBreadcrumbItems = () => {
    const items = [];
    
    // 添加根目录
    items.push({ 
      title: <a onClick={() => handleFolderChange('')}>根目录</a>
    });
    
    // 如果有当前路径，显示路径和返回按钮
    if (currentPath && currentPath !== '') {
      // 添加返回上级按钮（在根目录后面）
      items.push({
        title: (
          <Button 
            type="link" 
            size="small" 
            onClick={handleGoUpDirectory}
            style={{ padding: '0 4px', height: 'auto', color: '#1890ff', marginLeft: '8px' }}
          >
            ← 返回上级
          </Button>
        )
      });
      
      // 添加路径分割
      const pathParts = currentPath.split('/').filter(Boolean);
      pathParts.forEach((part, index) => {
        const path = pathParts.slice(0, index + 1).join('/');
        if (index === pathParts.length - 1) {
          // 最后一项不需要链接，用粗体显示当前目录
          items.push({ title: <strong>{part}</strong> });
        } else {
          items.push({
            title: <a onClick={() => handleFolderChange(path)}>{part}</a>
          });
        }
      });
    }
    return items;
  };

  const getFileIcon = (type) => {
    const iconMap = {
      folder: <FolderOutlined style={{ color: '#faad14', fontSize: '16px' }} />,
      text: <FileTextOutlined style={{ color: '#52c41a', fontSize: '16px' }} />,
      code: <CodeOutlined style={{ color: '#1890ff', fontSize: '16px' }} />,
      script: <CodeOutlined style={{ color: '#722ed1', fontSize: '16px' }} />,
      archive: <FileZipOutlined style={{ color: '#fa8c16', fontSize: '16px' }} />,
      image: <FileImageOutlined style={{ color: '#eb2f96', fontSize: '16px' }} />,
      document: <FilePdfOutlined style={{ color: '#f5222d', fontSize: '16px' }} />,
      unknown: <FileOutlined style={{ color: '#8c8c8c', fontSize: '16px' }} />
    };
    return iconMap[type] || iconMap.unknown;
  };

  const getFileTypeTag = (type) => {
    const typeMap = {
      folder: { color: 'gold', text: '文件夹' },
      text: { color: 'green', text: '文本' },
      code: { color: 'blue', text: '代码' },
      script: { color: 'purple', text: '脚本' },
      archive: { color: 'orange', text: '压缩包' },
      image: { color: 'magenta', text: '图片' },
      document: { color: 'red', text: '文档' },
      unknown: { color: 'default', text: '未知' }
    };
    const config = typeMap[type] || typeMap.unknown;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取本地文件夹列表
  const getLocalFolders = async () => {
    try {
      const response = await http.get('/api/file/tree/');
      return response.filter(item => item.type === 'folder');
    } catch (error) {
      console.error('获取本地文件夹列表失败:', error);
      return [];
    }
  };

  const handleTransferFile = async (fileName) => {
    try {
      // 确保fileName是字符串类型
      if (typeof fileName !== 'string') {
        console.error('❌ fileName不是字符串类型:', typeof fileName, fileName);
        message.error('文件名参数错误');
        return;
      }

      
      // 确定源文件信息
      let sourceType = 'local';
      let sourceFolderId = null;
      let sourceFilePath = '';
      
      if (activeTab === 'remote') {
        sourceType = 'remote';
        // 确定使用哪个文件夹ID和路径
        if (selectedFolder && currentFolder) {
          // 保存的远程文件夹模式
          sourceFolderId = selectedFolder;
          sourceFilePath = currentPath ? `${currentPath}/${fileName}` : fileName;
        } else if (connectedRemotePath && remoteCredentials) {
          // 临时连接模式 - 需要根据路径查找对应的文件夹ID
          const matchedFolder = remoteFolders.find(folder => 
            folder.remote_path === connectedRemotePath
          );
          
          if (matchedFolder) {
            sourceFolderId = matchedFolder.id;
            sourceFilePath = currentPath ? `${currentPath}/${fileName}` : fileName;
          } else {
            console.error('  - 源：临时连接模式，未找到匹配的文件夹配置');
            message.error('无法找到当前远程文件夹的配置信息');
            return;
          }
        } else {
          message.error('请先连接或选择远程文件夹');
          return;
        }
      } else {
        // 本地文件
        sourceType = 'local';
        sourceFilePath = currentPath ? `${currentPath}/${fileName}` : fileName;
      }
      
      


      // 重置表单
      localPathForm.resetFields();
      
      // 显示选择目标文件夹的弹窗
      Modal.confirm({
        title: '转移文件',
        icon: <SwapOutlined style={{ color: '#1890ff' }} />,
        width: 700,
        content: (
          <div style={{ marginTop: 16 }}>
            <Form form={localPathForm} layout="vertical">
              <Form.Item
                name="source_info"
                label="源文件信息"
              >
                <Alert
                  message={`源文件：${fileName}`}
                  description={`当前位置：${sourceType === 'remote' ? '远程文件夹' : '本地文件夹'} - ${sourceFilePath}`}
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              </Form.Item>
              
              <Form.Item
                name="target_path"
                label="选择目标位置"
                initialValue=""
                rules={[{ required: false }]}
              >
                <FolderTreeSelect
                  mode="both"
                  placeholder="选择目标文件夹（本地或远程）"
                  onChange={(value, extra) => {
                    console.log('TreeSelect选择 - value:', value);
                    console.log('TreeSelect选择 - extra.type:', extra?.type);
                    console.log('TreeSelect选择 - extra.folderId:', extra?.folderId);
                    localPathForm.setFieldsValue({ target_path: value });
                  }}
                />
              </Form.Item>
              
              <div style={{ marginTop: 8 }}>
                <Alert
                  message="转移规则说明"
                  description={
                    <div>
                      <p>• 远程 → 本地：下载文件到本地文件夹</p>
                      <p>• 本地 → 远程：上传文件到远程文件夹</p>
                      <p>• 本地 → 本地：移动/复制文件到其他本地文件夹</p>
                      <p>• 远程 → 远程：在远程文件夹间转移文件</p>
                    </div>
                  }
                  type="info"
                  showIcon
                />
              </div>
            </Form>
          </div>
        ),
        onOk: async () => {
          try {
            // 获取表单值
            const values = await localPathForm.validateFields();
            const targetPath = values.target_path || '';
            
            // 解析目标类型
            let targetType = 'local';
            let targetFolderId = null;
            let targetLocalPath = '';
            
            if (targetPath.startsWith('remote:')) {
              targetType = 'remote';
              targetFolderId = parseInt(targetPath.replace('remote:', ''));
            } else {
              targetType = 'local';
              targetLocalPath = targetPath;
            }
            
            setLoading(true);
            
            // 根据源和目标类型确定转移类型和提示信息
            let transferType = `${sourceType}_to_${targetType}`;
            let loadingMessage = '';
            let successMessage = '';
            
            switch (transferType) {
              case 'remote_to_local':
                loadingMessage = '正在从远程下载文件到本地...';
                successMessage = `文件 ${fileName} 已成功从远程下载到本地${targetLocalPath ? ' ' + targetLocalPath : '根'}目录`;
                break;
              case 'local_to_remote':
                loadingMessage = '正在上传文件到远程文件夹...';
                successMessage = `文件 ${fileName} 已成功上传到远程文件夹`;
                break;
              case 'local_to_local':
                loadingMessage = '正在移动文件到目标文件夹...';
                successMessage = `文件 ${fileName} 已成功移动到${targetLocalPath ? ' ' + targetLocalPath : '根'}目录`;
                break;
              case 'remote_to_remote':
                loadingMessage = '正在在远程文件夹间转移文件...';
                successMessage = `文件 ${fileName} 已成功在远程文件夹间转移`;
                break;
              default:
                loadingMessage = '正在转移文件...';
                successMessage = `文件 ${fileName} 转移成功`;
            }
            
            message.loading(loadingMessage);
            
            // 调试信息
            console.log('📡 发送通用转移请求:');
            console.log('  - 转移类型:', transferType);
            console.log('  - 源类型:', sourceType);
            console.log('  - 源文件夹ID:', sourceFolderId);
            console.log('  - 源文件路径:', sourceFilePath);
            console.log('  - 目标类型:', targetType);
            console.log('  - 目标文件夹ID:', targetFolderId);
            console.log('  - 目标本地路径:', targetLocalPath);
            
            // 构建请求数据对象
            const requestData = {
              transfer_type: transferType,
              source_type: sourceType,
              source_folder_id: sourceFolderId,
              source_file_path: sourceFilePath,
              target_type: targetType,
              target_folder_id: targetFolderId,
              target_local_path: targetLocalPath,
              file_name: fileName
            };
            
            console.log('📦 请求数据:', JSON.stringify(requestData, null, 2));
            
            // 调用通用转移API
            const response = await http.post('/api/file/transfer/', requestData);
            
            console.log('✅ 转移响应:', response);
            
            // 使用后端返回的消息，如果没有则使用前端预定义的消息
            const finalMessage = response.data?.message || response.message || successMessage;
            message.success(finalMessage);
            
            // 刷新当前文件列表
            if (activeTab === 'remote') {
              if (selectedFolder && currentFolder) {
                fetchFiles(selectedFolder, currentPath);
              } else {
                loadFiles(currentPath);
              }
            } else {
              loadFiles(currentPath);
            }
            
            return response;
          } catch (error) {
            console.error('❌ 转移文件失败:', error.message || '未知错误');
            console.error('  - error.status:', error.response?.status);
            console.error('  - error.data:', error.response?.data);
            
            const errorMessage = error.response?.data?.error || 
                               error.response?.data?.message || 
                               error.message || 
                               '请求异常';
            message.error(`转移文件失败: ${errorMessage}`);
            return Promise.reject(error);
          } finally {
            setLoading(false);
          }
        },
        okText: '开始转移',
        cancelText: '取消',
      });
    } catch (error) {
      console.error('转移文件失败:', error.message || '未知错误');
      
      const errorMessage = error.response?.data?.error || 
                         error.response?.data?.message || 
                         error.message || 
                         '请求异常';
      message.error(`转移文件失败: ${errorMessage}`);
    }
  };

  const columns = [
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {getFileIcon(record.type)}
          {record.type === 'folder' ? (
            <a 
              onClick={() => handleFolderChange(currentPath ? `${currentPath}/${text}` : text)}
              style={{ fontWeight: 'bold', color: '#1890ff' }}
            >
              {text}
            </a>
          ) : (
            <span style={{
              color: isEditableFile(text, record.type) ? '#1890ff' : 'inherit',
              fontWeight: isEditableFile(text, record.type) ? 'bold' : 'normal'
            }}>{text}</span>
          )}
        </Space>
      ),
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => getFileTypeTag(type),
      width: 80,
    },
    {
      title: '大小',
      dataIndex: 'size_human',
      key: 'size_human',
      width: 100,
    },
    {
      title: '修改时间',
      dataIndex: 'modified',
      key: 'modified',
      width: 160,
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          {record.type !== 'folder' && isEditableFile(record.name, record.type) && (
            <Tooltip title="编辑">
              <Button
                type="link"
                size="small"
                icon={<EditOutlined style={{fontSize: '16px'}} />}
                onClick={() => {
                  console.log('编辑按钮被点击，文件名:', record.name, '文件类型:', record.type);
                  handleEditFile(record.name);
                }}
              />
            </Tooltip>
          )}
          {record.type !== 'folder' && (
            <Tooltip title={activeTab === 'remote' ? "下载到本地" : "下载"}>
              <Button
                type="link"
                size="small"
                icon={<DownloadOutlined style={{fontSize: '16px'}} />}
                onClick={() => handleDownload(record.name)}
              />
            </Tooltip>
          )}
          {/* 转移文件按钮，支持本地和远程文件 */}
          {record.type !== 'folder' && (
            <Tooltip title="转移文件">
              <Button
                type="link"
                size="small"
                icon={<SwapOutlined style={{fontSize: '16px', color: '#1890ff'}} />}
                onClick={() => handleTransferFile(record.name)}
              />
            </Tooltip>
          )}
          <Popconfirm
            title={record.type === 'folder' ? "确定要删除这个文件夹吗？只能删除空文件夹。" : "确定要删除这个文件吗？"}
            onConfirm={() => handleDelete(record.name)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined style={{fontSize: '16px'}} />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const fetchRemoteFolders = () => {
    setLoading(true);
    http.get('/api/file/remote/')
      .then(res => {
        setRemoteFolders(res);
        if (res.length > 0) {
          setSelectedFolder(res[0].id);
        }
      })
      .finally(() => setLoading(false));
  };

  const handleFolderSelect = (folder_id) => {
    setSelectedFolder(folder_id);
    setCurrentFolder(remoteFolders.find(x => x.id === folder_id));
    setCurrentPath('');
    fetchFiles(folder_id, '');
  };

  const handlePathClick = (folder_id, path) => {
    setCurrentPath(path);
    fetchFiles(folder_id, path);
  };

  const fetchFiles = (folder_id, path) => {
    setLoading(true);
    http.get('/api/file/remote/manager/', {params: {folder_id, path}})
      .then(res => {
        setFiles(res);
        setFilteredFiles(res);
      })
      .finally(() => setLoading(false));
  };

  const handleRefresh = () => {
    if (selectedFolder && currentPath !== undefined) {
      fetchFiles(selectedFolder, currentPath);
    }
  };

  const fetchCacheStatus = async () => {
    await loadCacheStatus();
  };

  const handleShowCacheStatus = () => {
    fetchCacheStatus();
    setShowCacheStatus(true);
  };

  const handleFileClick = (file) => {
    if (file.type === 'folder') {
      const newPath = currentPath ? `${currentPath}/${file.name}` : file.name;
      setCurrentPath(newPath);
      fetchFiles(selectedFolder, newPath);
    } else {
      // 处理文件点击
      const downloadUrl = `/api/file/remote/download/?folder_id=${selectedFolder}&path=${currentPath}&file_name=${file.name}`;
      window.open(`${downloadUrl}&x_token=${X_TOKEN}`, '_blank');
    }
  };

  const handleRemoteUpload = (files) => {
    if (!connectedRemotePath || !remoteCredentials) {
      message.error('请先连接远程文件夹');
      return;
    }
    
    const formData = new FormData();
    files.forEach(file => formData.append('file', file));
    
    // 如果有selectedFolder，使用folder_id方式
    if (selectedFolder) {
      formData.append('folder_id', selectedFolder);
      formData.append('path', currentPath || '');
    } else {
      // 否则使用凭据方式
      formData.append('remote_path', remoteCredentials.remote_path);
      formData.append('username', remoteCredentials.username);
      formData.append('password', remoteCredentials.password);
      if (remoteCredentials.domain) {
        formData.append('domain', remoteCredentials.domain);
      }
      formData.append('path', currentPath || '');
    }
    
    setUploadLoading(true);
    http.post('/api/file/remote/upload/', formData)
      .then(() => {
        message.success('上传成功');
        loadFiles(); // 重新加载文件列表
      })
      .catch(error => {
        message.error(`上传失败: ${error.response?.data?.error || error.message || '未知错误'}`);
      })
      .finally(() => {
        setUploadLoading(false);
      });
  };

  const renderBreadcrumb = () => {
    if (!currentFolder) return null;
    
    const paths = currentPath ? currentPath.split('/') : [];
    return (
      <div className={styles.breadcrumb}>
        <span 
          className={styles.breadcrumbItem} 
          onClick={() => handlePathClick(selectedFolder, '')}>
          {currentFolder.name}
        </span>
        {paths.map((path, index) => {
          const currentPathTo = paths.slice(0, index + 1).join('/');
          return (
            <span key={index}>
              <span className={styles.breadcrumbSeparator}>/</span>
              <span 
                className={styles.breadcrumbItem} 
                onClick={() => handlePathClick(selectedFolder, currentPathTo)}>
                {path}
              </span>
            </span>
          );
        })}
      </div>
    );
  };

  const renderFolderMenu = () => {
    return (
      <Menu>
        {remoteFolders.map(folder => (
          <Menu.Item key={folder.id} onClick={() => handleFolderSelect(folder.id)}>
            {folder.name}
          </Menu.Item>
        ))}
        <Menu.Divider />
        <Menu.Item key="connect" onClick={handleConnectRemote}>
          连接新的远程文件夹
        </Menu.Item>
      </Menu>
    );
  };

  const renderCacheStatusModal = () => {
    return (
      <Modal
        title="缓存状态"
        visible={showCacheStatus}
        onCancel={() => setShowCacheStatus(false)}
        footer={[
          <Button key="refresh" type="primary" onClick={fetchCacheStatus}>
            刷新状态
          </Button>,
          <Button key="close" onClick={() => setShowCacheStatus(false)}>
            关闭
          </Button>
        ]}
      >
        <div>
          <p><strong>缓存统计：</strong></p>
          <p>总缓存数量: {cacheStatus.total_caches || 0}</p>
          <p>有效缓存: {cacheStatus.valid_caches || 0}</p>
          <p>无效缓存: {cacheStatus.invalid_caches || 0}</p>
          <p>新鲜缓存: {cacheStatus.fresh_caches || 0}</p>
          <p>过期缓存: {cacheStatus.expired_caches || 0}</p>
          <p>缓存过期时间: {cacheStatus.cache_expire_minutes || 5} 分钟</p>
          <p>最大递归深度: {cacheStatus.max_depth || 3}</p>
          <p>最大线程数: {cacheStatus.max_workers || 10}</p>
          
          <p><strong>加速状态：</strong></p>
          <p>
            funboost加速: 
            {cacheStatus.funboost_available ? 
              <Tag color="green">已启用</Tag> : 
              <Tag color="orange">未启用</Tag>
            }
          </p>
          <p>当前框架: {cacheStatus.framework || 'threading'}</p>
        </div>
      </Modal>
    );
  };

  return (
    <Spin spinning={loading}>
      <div className={styles.fileManager}>
        <Row gutter={[16, 16]} style={{ height: '100%' }}>
          {/* 左侧：文件树 */}
          <Col span={6}>
            <Card
              title="文件树"
              bodyStyle={{ padding: 0, height: 'calc(100vh - 300px)' }}
            >
              <FileTree
                onSelect={handleFileSelect}
                onFolderChange={handleFolderChange}
                onFileMove={handleFileMove}
                onFileTransfer={handleTransferFile}
                onFileDelete={handleDeleteFile}
                selectedPath={selectedFilePath}
                height="100%"
              />
            </Card>
          </Col>
          
          {/* 右侧：文件列表 */}
          <Col span={18}>
            <Card
              title={
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>文件管理</span>
                    <Space>
                      <Button 
                        type="default" 
                        icon={<CloudServerOutlined />}
                        onClick={handleConnectRemote}
                        size="small"
                      >
                        连接远程文件夹
                      </Button>
                      <Button 
                        type="default" 
                        icon={<EditOutlined />}
                        onClick={handleManageRemoteFolders}
                        size="small"
                      >
                        管理远程文件夹
                      </Button>
                    </Space>
                  </div>
                  <Tabs
                    activeKey={activeTab}
                    onChange={handleTabChange}
                    size="small"
                    items={[
                      {
                        key: 'local',
                        label: (
                          <span>
                            <FolderOutlined />
                            本地文件
                          </span>
                        ),
                      },
                      {
                        key: 'remote',
                        label: (
                          <span>
                            <CloudServerOutlined />
                            远程文件夹
                            {connectedRemotePath && <Tag color="green" style={{ marginLeft: 4 }}>已连接</Tag>}
                          </span>
                        ),
                        disabled: !connectedRemotePath,
                      },
                    ]}
                  />
                  <Breadcrumb 
                    items={getBreadcrumbItems()}
                    style={{ fontSize: '12px' }}
                  />
                </Space>
              }
              extra={
                <Space>
                  {activeTab === 'local' && (
                    <>
                      <Button 
                        type="primary" 
                        icon={<PlusOutlined />}
                        onClick={handleCreateScript}
                      >
                        新建文件
                      </Button>
                      <Upload
                        customRequest={handleUpload}
                        showUploadList={false}
                        multiple
                      >
                        <Button 
                          icon={<UploadOutlined />}
                          loading={uploadLoading}
                        >
                          上传文件
                        </Button>
                      </Upload>
                    </>
                  )}
                  {activeTab === 'remote' && (
                    <Button 
                      icon={<ReloadOutlined />} 
                      onClick={handleRefreshCache}
                      loading={refreshingCache}
                      type="dashed"
                    >
                      刷新缓存
                    </Button>
                  )}
                  <Button 
                    icon={<ReloadOutlined />} 
                    onClick={() => loadFiles()}
                    loading={loading}
                  >
                    刷新
                  </Button>
                </Space>
              }
              bodyStyle={{ padding: '16px' }}
            >
              {/* 拖拽上传区域 */}
              {files.length === 0 ? (
                <DragUpload
                  onUpload={handleDragUpload}
                  multiple={true}
                  maxSize={0}
                  disabled={uploadLoading}
                  className={styles.fileManagerDragArea}
                >
                  <div className={styles.fileManagerDragArea}>
                    <InboxOutlined className={styles.icon} />
                    <p className={styles.text}>
                      拖拽文件到这里或点击上传<br/>
                      支持多文件上传，无大小限制
                    </p>
                  </div>
                </DragUpload>
              ) : (
                <>
                  {/* 简化的拖拽上传区域 */}
                  <div style={{ marginBottom: 16 }}>
                    <DragUpload
                      onUpload={handleDragUpload}
                      multiple={true}
                      maxSize={0}
                      disabled={uploadLoading}
                    >
                      <div className={styles.transferDragArea}>
                        <InboxOutlined className={styles.icon} />
                        <p className={styles.text}>拖拽文件到这里快速上传</p>
                      </div>
                    </DragUpload>
                  </div>

                  {/* 当前状态指示器 */}
                  {activeTab === 'remote' && connectedRemotePath && (
                    <div style={{ 
                      padding: '8px 12px', 
                      background: '#e6f7ff', 
                      border: '1px solid #91d5ff', 
                      borderRadius: '6px', 
                      marginBottom: '16px',
                      fontSize: '12px'
                    }}>
                      <CloudServerOutlined style={{ color: '#1890ff', marginRight: '4px' }} />
                      正在浏览远程文件夹: <code style={{ background: '#f0f0f0', padding: '2px 4px' }}>{connectedRemotePath}</code>
                      <br />
                      <span style={{ color: '#666' }}>
                        点击"下载"可将远程文件保存到本地文件管理器
                        {cacheStatus && (
                          <>
                            {' | '}
                            缓存状态: {cacheStatus.fresh_caches || 0} 个新鲜缓存, {cacheStatus.expired_caches || 0} 个已过期
                            {' | '}
                            过期时间: {cacheStatus.cache_expire_minutes || 5} 分钟
                            {' | '}
                            最大递归深度: {cacheStatus.max_depth || 3} 级
                            {cacheStatus.funboost_available && (
                              <>
                                {' | '}
                                <Tag color="green">funboost加速已启用</Tag>
                              </>
                            )}
                          </>
                        )}
                      </span>
                      <div style={{ marginTop: '8px', display: 'flex', justifyContent: 'space-between' }}>
                        <Space>
                          <Button 
                            size="small" 
                            type="primary" 
                            icon={<ReloadOutlined />}
                            onClick={handleRefreshCache}
                            loading={refreshingCache}
                          >
                            刷新所有缓存
                          </Button>
                          <Button 
                            size="small" 
                            icon={<CloudServerOutlined />}
                            onClick={handlePreloadFileTree}
                            loading={preloadingTree}
                          >
                            预加载整个文件树
                          </Button>
                        </Space>
                        <span>
                          {(refreshingCache || preloadingTree) && (
                            <Tag color="processing">正在加载数据，请稍候...</Tag>
                          )}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* 当前目录和返回按钮 */}
                  {currentPath && (
                    <div style={{ 
                      padding: '8px 12px', 
                      background: '#f5f5f5', 
                      borderRadius: '6px', 
                      marginBottom: '16px',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <span style={{ fontSize: '14px', color: '#666' }}>
                        当前目录: <strong>{currentPath || '根目录'}</strong>
                      </span>
                      <Button 
                        type="primary" 
                        size="small" 
                        icon={<ArrowLeftOutlined />}
                        onClick={handleGoUpDirectory}
                      >
                        返回上级
                      </Button>
                    </div>
                  )}

                  <Table 
                    columns={columns}
                    dataSource={filteredFiles}
                    rowKey="name"
                    loading={loading}
                    pagination={{
                      pageSize: 15,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 个文件`,
                    }}
                    size="middle"
                    scroll={{ y: 400 }}
                  />
                </>
              )}
            </Card>
          </Col>
        </Row>

        {/* 远程文件夹连接Modal */}
        <Modal
          title="连接远程文件夹"
          visible={remoteModalVisible}
          open={remoteModalVisible}
          onCancel={() => {
            console.log('点击了取消按钮');
            setRemoteModalVisible(false);
          }}
          onOk={handleRemoteConnect}
          width={600}
          getContainer={false}
          destroyOnClose={false}
          zIndex={1000}
          mask={true}
          maskClosable={true}
          forceRender={true}
        >
          {remoteModalVisible && (
            <Form form={remoteForm} layout="vertical">
            <Form.Item
              name="remote_path"
              label="远程路径"
              rules={[
                { required: true, message: '请输入远程路径' },
                { 
                  pattern: /^\\\\[^\\]+\\.+/,
                  message: '路径格式错误，应以 \\\\ 开头，例如：\\\\10.227.8.36\\devicetest'
                }
              ]}
              extra="请输入完整的网络共享路径，支持子目录"
            >
              <Input 
                placeholder="例如: \\\\10.227.8.36\\devicetest\\00.固件与驱动\\7.GPU"
                style={{ fontFamily: 'monospace' }}
              />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="username" label="用户名">
                  <Input placeholder="访问远程文件夹的用户名" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="domain" label="域名">
                  <Input placeholder="域名（可选）" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item name="password" label="密码">
              <Input.Password placeholder="访问远程文件夹的密码" />
            </Form.Item>

            <Form.Item name="save_as_persistent" valuePropName="checked">
              <Checkbox>保存为持久目录（推荐）</Checkbox>
            </Form.Item>

            <Form.Item
              name="folder_name"
              label="目录名称"
              dependencies={['save_as_persistent']}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue('save_as_persistent'),
                  message: '请输入目录名称',
                }),
              ]}
            >
              <Input placeholder="为这个远程文件夹起一个名称，如：昆仑芯GPU-P800" />
            </Form.Item>

            <Form.Item name="description" label="描述（可选）">
              <Input.TextArea 
                rows={2} 
                placeholder="描述这个远程文件夹的用途，如：用于GPU测试的固件和驱动文件"
              />
            </Form.Item>

            <div style={{ background: '#f0f2f5', padding: '12px', borderRadius: '6px', marginTop: '16px' }}>
              <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>
                <strong>使用说明：</strong><br/>
                • 支持Windows网络共享路径，格式: \\\\服务器IP\\共享名\\子目录<br/>
                • <strong>保存为持久目录</strong>：勾选后将在文件树中显示，可用于测试计划和文件分发<br/>
                • <strong>优先尝试</strong>：如果当前Windows用户已可访问该路径，可不填用户名密码<br/>
                • 如果连接失败，请填写正确的用户名和密码再次尝试
              </p>
            </div>
          </Form>
          )}
        </Modal>

        <DragUpload
          visible={uploadLoading}
          uploading={uploadLoading}
          onOk={handleUpload}
          onCancel={() => setUploadLoading(false)}
        />
        
        {renderCacheStatusModal()}

        {/* 远程文件夹管理Modal */}
        <Modal
          title={editingFolder ? "编辑远程文件夹" : "新增远程文件夹"}
          visible={remoteFolderManageVisible}
          onCancel={() => {
            setRemoteFolderManageVisible(false);
            setEditingFolder(null);
            setShowFolderForm(false);
            folderForm.resetFields();
          }}
          onOk={handleSaveRemoteFolder}
          width={800}
          destroyOnClose={true}
        >
          <div style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <span style={{ fontWeight: 'bold' }}>远程文件夹配置列表</span>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={handleAddRemoteFolder}
                size="small"
              >
                新增
              </Button>
            </div>
            
            <Table
              dataSource={remoteFolders}
              rowKey="id"
              size="small"
              scroll={{ y: 200 }}
              pagination={false}
              columns={[
                {
                  title: '名称',
                  dataIndex: 'name',
                  width: 120,
                  ellipsis: true,
                },
                {
                  title: '远程路径',
                  dataIndex: 'remote_path',
                  ellipsis: true,
                },
                {
                  title: '用户名',
                  dataIndex: 'username',
                  width: 100,
                  ellipsis: true,
                },
                {
                  title: '操作',
                  width: 120,
                  render: (_, record) => (
                    <Space size="small">
                      <Button
                        type="link"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => handleEditRemoteFolder(record)}
                      >
                        编辑
                      </Button>
                      <Popconfirm
                        title="确定要删除这个远程文件夹配置吗？"
                        onConfirm={() => handleDeleteRemoteFolder(record.id)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button
                          type="link"
                          size="small"
                          danger
                          icon={<DeleteOutlined />}
                        >
                          删除
                        </Button>
                      </Popconfirm>
                    </Space>
                  ),
                },
              ]}
            />
          </div>

          {showFolderForm && (
            <Form form={folderForm} layout="vertical">
              <Form.Item
                name="name"
                label="配置名称"
                rules={[{ required: true, message: '请输入配置名称' }]}
              >
                <Input placeholder="为这个远程文件夹起一个名称，如：昆仑芯GPU-P800" />
              </Form.Item>

              <Form.Item
                name="remote_path"
                label="远程路径"
                rules={[
                  { required: true, message: '请输入远程路径' },
                  { 
                    pattern: /^\\\\[^\\]+\\.+/,
                    message: '路径格式错误，应以 \\\\ 开头，例如：\\\\10.227.8.36\\devicetest'
                  }
                ]}
                extra="请输入完整的网络共享路径，支持子目录"
              >
                <Input 
                  placeholder="例如: \\\\10.227.8.36\\devicetest\\00.固件与驱动\\7.GPU"
                  style={{ fontFamily: 'monospace' }}
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="username" label="用户名">
                    <Input placeholder="访问远程文件夹的用户名" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="domain" label="域名">
                    <Input placeholder="域名（可选）" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item name="password" label="密码">
                <Input.Password placeholder="访问远程文件夹的密码" />
              </Form.Item>

              <Form.Item name="description" label="描述（可选）">
                <Input.TextArea 
                  rows={2} 
                  placeholder="描述这个远程文件夹的用途，如：用于GPU测试的固件和驱动文件"
                />
              </Form.Item>

              <div style={{ background: '#f0f2f5', padding: '12px', borderRadius: '6px' }}>
                <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>
                  <strong>使用说明：</strong><br/>
                  • 支持Windows网络共享路径，格式: \\\\服务器IP\\共享名\\子目录<br/>
                  • 配置保存后将在文件树中显示，可用于测试计划和文件分发<br/>
                  • 如果当前Windows用户已可访问该路径，可不填用户名密码<br/>
                  • 点击保存时会自动测试连接
                </p>
              </div>
            </Form>
          )}
        </Modal>
      </div>
    </Spin>
  );
}

export default observer(FileManager); 