# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.

import logging
from celery import shared_task
from apps.file.redis_cache_service import cache_service
from apps.file.models import RemoteFolder

logger = logging.getLogger(__name__)

@shared_task
def refresh_remote_folder_caches():
    """定时刷新远程文件夹缓存任务"""
    try:
        logger.info("🔄 开始定时刷新远程文件夹缓存...")
        stats = cache_service.refresh_all_caches()
        
        # 记录执行结果
        logger.info(f"📊 缓存刷新完成: {stats}")
        
        # 清理过期缓存
        cleaned_count = cache_service.clear_expired_caches()
        if cleaned_count > 0:
            logger.info(f"🗑️ 清理过期缓存: {cleaned_count} 条")
        
        return {
            'success': True,
            'stats': stats,
            'cleaned_caches': cleaned_count
        }
        
    except Exception as e:
        logger.error(f"❌ 定时缓存刷新失败: {e}")
        return {
            'success': False,
            'error': str(e)
        }

@shared_task
def preload_all_file_trees():
    """预加载所有远程文件夹的文件树"""
    try:
        logger.info("🔄 开始预加载所有远程文件夹文件树...")
        
        active_folders = RemoteFolder.objects.filter(is_active=True)
        total_folders = active_folders.count()
        success_folders = 0
        failed_folders = 0
        
        for folder in active_folders:
            try:
                logger.info(f"📂 预加载文件夹: {folder.name}")
                stats = cache_service.cache_entire_file_tree(folder.id)
                
                if 'error' not in stats:
                    success_folders += 1
                    logger.info(f"✅ 文件夹预加载成功: {folder.name}, 路径数: {stats.get('total_paths')}")
                else:
                    failed_folders += 1
                    logger.error(f"❌ 文件夹预加载失败: {folder.name}, 错误: {stats.get('error')}")
                    
            except Exception as e:
                failed_folders += 1
                logger.error(f"❌ 文件夹预加载异常: {folder.name} - {e}")
        
        stats = {
            'total_folders': total_folders,
            'success_folders': success_folders,
            'failed_folders': failed_folders
        }
        
        logger.info(f"📊 文件树预加载完成: {stats}")
        return {
            'success': True,
            'stats': stats
        }
        
    except Exception as e:
        logger.error(f"❌ 文件树预加载失败: {e}")
        return {
            'success': False,
            'error': str(e)
        }

@shared_task
def refresh_single_folder_cache(folder_id, path='', recursive=False):
    """刷新单个文件夹缓存任务"""
    try:
        if recursive:
            logger.info(f"🔄 递归刷新文件夹缓存: {folder_id}")
            stats = cache_service.cache_entire_file_tree(folder_id)
            
            if 'error' not in stats:
                logger.info(f"✅ 文件夹递归缓存刷新成功: {folder_id}, 路径数: {stats.get('total_paths')}")
                return {'success': True, 'stats': stats}
            else:
                logger.warning(f"⚠️ 文件夹递归缓存刷新失败: {folder_id}, 错误: {stats.get('error')}")
                return {'success': False, 'error': stats.get('error')}
        else:
            logger.info(f"🔄 刷新文件夹缓存: {folder_id}:{path}")
            success = cache_service.refresh_folder_cache(folder_id, path)
            
            if success:
                logger.info(f"✅ 文件夹缓存刷新成功: {folder_id}:{path}")
                return {'success': True}
            else:
                logger.warning(f"⚠️ 文件夹缓存刷新失败: {folder_id}:{path}")
                return {'success': False, 'error': '刷新失败'}
            
    except Exception as e:
        logger.error(f"❌ 文件夹缓存刷新异常: {folder_id}:{path} - {e}")
        return {'success': False, 'error': str(e)}

@shared_task
def cleanup_expired_caches():
    """清理过期缓存任务"""
    try:
        logger.info("🗑️ 开始清理过期缓存...")
        count = cache_service.clear_expired_caches()
        
        if count > 0:
            logger.info(f"✅ 清理过期缓存完成: {count} 条")
        else:
            logger.info("ℹ️ 没有过期缓存需要清理")
            
        return {'success': True, 'cleaned_count': count}
        
    except Exception as e:
        logger.error(f"❌ 清理过期缓存失败: {e}")
        return {'success': False, 'error': str(e)} 