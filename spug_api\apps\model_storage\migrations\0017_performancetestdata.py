# Generated by Django 2.2.28 on 2025-07-29 11:12

from django.db import migrations, models
import libs.mixins


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0016_modeldata'),
    ]

    operations = [
        migrations.CreateModel(
            name='PerformanceTestData',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filename', models.CharField(max_length=256, verbose_name='文件名')),
                ('success_requests', models.IntegerField(default=0, verbose_name='成功请求数')),
                ('benchmark_duration', models.FloatField(default=0.0, verbose_name='基准时长(s)')),
                ('input_tokens', models.IntegerField(default=0, verbose_name='输入Token总数')),
                ('output_tokens', models.IntegerField(default=0, verbose_name='生成Token总数')),
                ('request_throughput', models.FloatField(default=0.0, verbose_name='请求吞吐量(req/s)')),
                ('output_token_throughput', models.FloatField(default=0.0, verbose_name='输出Token吞吐量(tok/s)')),
                ('total_token_throughput', models.FloatField(default=0.0, verbose_name='总Token吞吐量(tok/s)')),
                ('avg_ttft', models.FloatField(default=0.0, verbose_name='平均TTFT(ms)')),
                ('median_ttft', models.FloatField(default=0.0, verbose_name='中位TTFT(ms)')),
                ('p99_ttft', models.FloatField(default=0.0, verbose_name='P99 TTFT(ms)')),
                ('avg_tpot', models.FloatField(default=0.0, verbose_name='平均TPOT(ms)')),
                ('median_tpot', models.FloatField(default=0.0, verbose_name='中位TPOT(ms)')),
                ('p99_tpot', models.FloatField(default=0.0, verbose_name='P99 TPOT(ms)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '性能测试数据',
                'verbose_name_plural': '性能测试数据',
                'db_table': 'model_storage_performance_test_data',
                'ordering': ('-created_at',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
