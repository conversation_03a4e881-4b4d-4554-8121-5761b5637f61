.fileTree {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;

  .treeActions {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
    border-radius: 6px 6px 0 0;
  }

  .treeContainer {
    flex: 1;
    overflow: auto;
    padding: 8px;

    .tree {
      .treeNode {
        display: flex;
        align-items: center;
        flex: 1;
        overflow: hidden;

        .nodeIcon {
          margin-right: 6px;
          display: flex;
          align-items: center;
        }

        .nodeTitle {
          font-weight: 500;
          color: #333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          flex: 1;
        }

        .nodeInfo {
          font-size: 11px;
          color: #999;
          margin-left: 8px;
          white-space: nowrap;

          span {
            margin-right: 4px;
          }
        }

        .nodeSize {
          font-size: 11px;
          color: #999;
          margin-left: 8px;
          white-space: nowrap;
        }
      }

      // 自定义Tree组件样式
      :global(.ant-tree-node-content-wrapper) {
        flex: 1;
        display: flex;
        align-items: center;
        overflow: hidden;

        &:hover {
          background-color: #f5f5f5;
        }

        &.ant-tree-node-selected {
          background-color: #e6f7ff;
        }
      }

      :global(.ant-tree-treenode) {
        padding: 2px 0;

        .ant-tree-node-content-wrapper {
          padding: 2px 4px;
          border-radius: 4px;
          transition: all 0.2s;
        }
      }

      :global(.ant-tree-switcher) {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      :global(.ant-tree-indent-unit) {
        width: 20px;
      }

      // 拖拽样式
      :global(.ant-tree-node-content-wrapper.drag-over) {
        background-color: #e6f7ff !important;
        border: 1px dashed #1890ff;
      }

      :global(.ant-tree-node-content-wrapper.drag-over-gap-top) {
        border-top: 2px solid #1890ff;
      }

      :global(.ant-tree-node-content-wrapper.drag-over-gap-bottom) {
        border-bottom: 2px solid #1890ff;
      }

      :global(.ant-tree-draggable-icon) {
        color: #999;
        cursor: grab;
      }

      :global(.ant-tree-node-content-wrapper.dragging) {
        opacity: 0.5;
      }

      :global(.ant-tree .ant-tree-node-content-wrapper:hover .ant-tree-draggable-icon) {
        color: #1890ff;
      }

      // 拖拽提示
      :global(.ant-tree-drop-indicator) {
        position: absolute;
        height: 2px;
        background-color: #1890ff;
        z-index: 1;
      }
      
      // 拖拽时的蒙层效果
      :global(.ant-tree-treenode.drop-target) {
        background-color: rgba(24, 144, 255, 0.1);
        border: 1px dashed #1890ff;
      }
      
      // 不可拖拽的节点
      :global(.ant-tree-treenode.drop-disabled) {
        opacity: 0.5;
        pointer-events: none;
      }
    }
  }
}

// 右键菜单样式
:global(.ant-dropdown-menu) {
  .ant-dropdown-menu-item {
    display: flex;
    align-items: center;
    
    .anticon {
      margin-right: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .fileTree {
    .treeContainer {
      .tree {
        .treeNode {
          .nodeInfo,
          .nodeSize {
            display: none;
          }
        }
      }
    }
  }
} 