# coding=utf8
"""
此文件nb_log_config.py是自动生成到python项目的根目录的。
在这里面写的变量会覆盖此文件nb_log_config_default中的值。对nb_log包进行默认的配置。
但最终配置方式是由get_logger_and_add_handlers方法的各种传参决定，如果方法相应的传参为None则使用这里面的配置。
"""

"""
如果反对日志有各种彩色，可以设置 DEFAULUT_USE_COLOR_HANDLER = False
如果反对日志有块状背景彩色，可以设置 DISPLAY_BACKGROUD_COLOR_IN_CONSOLE = False
如果想屏蔽nb_log包对怎么设置pycharm的颜色的提示，可以设置 WARNING_PYCHARM_COLOR_SETINGS = False
如果想改变日志模板，可以设置 FORMATTER_KIND 参数，只带了7种模板，可以自定义添加喜欢的模板
LOG_PATH 配置存放日志的文件夹路径，这个按需配置，如果不配置默认是项目根目录下的logs文件夹

"""

# noinspection PyUnresolvedReferences
import logging
import os
import sys

# noinspection PyUnresolvedReferences
from pathlib import Path  # noqa

# 项目根目录
# print("nb_log_config.py这个文件所在的路径是：", Path(__file__).absolute().parent)
# print("nb_log_config.py这个文件所在的路径的上一级目录是：", Path(__file__).absolute().parent.parent)
# print("项目根目录是：", Path(__file__).absolute().parent)
# print("sys.path是：", sys.path)

# 如果直接使用下面这行，而不是使用 LOG_PATH = '/pythonlogs'，这样自动创建pythonlogs文件夹，方便。但不能自动创建多层文件夹。
# 如果使用了 LOG_PATH = 'pythonlogs/subdir1/subdir2' 多层文件夹，则需要你自己手动创建好目录层级。
# LOG_PATH = Path(__file__).absolute().parent / Path("pythonlogs")  # 日志文件夹的路径，会自动创建这个文件夹。

# 如果不修改这个值，则默认日志是存放在项目根目录下的logs文件夹中。
LOG_PATH = Path(__file__).absolute().parent / Path("logs")  # 日志文件夹的路径，会自动创建这个文件夹。

# 设置了这个变量，nb_log包的自动设置PYTHONPATH的功能会失效，不再强制配置PYTHONPATH
# PYTHONPATH_SWITCH = False

# 设置nb_log库的日志级别
LOG_LEVEL_FILTER = logging.DEBUG  # 日志过滤级别

# 屏蔽nb_log库的debug日志
FILTER_WORDS_LEVEL_DEBUG = ['funboost', 'nb_log']  # 这些包的debug日志会被忽略

# 默认日志文件块大小500m
MEGABYTES = 1024 * 1024
LOG_FILE_SIZE = 50 * MEGABYTES  # 日志文件切片大小
LOG_FILE_BACKUP_COUNT = 3  # 日志文件备份数量

# 不需要彩色日志
DEFAULUT_USE_COLOR_HANDLER = False  # 是否默认使用有彩的日志
DISPLAY_BACKGROUD_COLOR_IN_CONSOLE = False  # 在控制台是否显示彩色块状的日志

# 关闭pycharm相关的提示
WARNING_PYCHARM_COLOR_SETINGS = False  # 设置pycharm配置彩色日志是否提示

# 不显示logo
SHOW_LOGO_FIGLET = False  # 是否在日志文件和控制台显示logo

# 默认值
DEFAULT_ADD_MULTIPROCESSING_SAFE_ROATING_FILE_HANDLER = False  # 是否默认同时将日志记录到记log文件中
AUTO_PATCH_PRINT = False  # 是否自动打print的猴子补丁，如果打了猴子补丁，print自动变色和可点击跳转。
FORMATTER_KIND = 5  # 日志模板种类 