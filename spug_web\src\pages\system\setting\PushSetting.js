/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React, {useEffect, useState} from 'react';
import {observer} from 'mobx-react';
import {Form, Input, Button, Spin, Switch, message} from 'antd';
import css from './index.module.css';
import {http} from 'libs';
import store from './store';

export default observer(function () {
  const [loading, setLoading] = useState(false);
  const [fetching, setFetching] = useState(false);
  const [settings, setSettings] = useState({
    email_enabled: false,
    email_server: '',
    email_port: 587,
    email_username: '',
    email_password: '',
    webhook_enabled: false,
    webhook_url: ''
  });

  useEffect(() => {
    fetchSettings();
  }, []);

  function fetchSettings() {
    setFetching(true);
    http.get('/api/setting/notification/')
      .then(res => setSettings(res))
      .finally(() => setFetching(false));
  }

  function handleSave() {
    setLoading(true);
    http.post('/api/setting/notification/', settings)
      .then(() => {
        message.success('保存成功');
        store.fetchSettings();
      })
      .finally(() => setLoading(false));
  }

  function handleChange(key, value) {
    setSettings({...settings, [key]: value});
  }

  return (
    <Spin spinning={fetching}>
      <div className={css.title}>通知设置</div>
      <div style={{maxWidth: 500}}>
        <Form layout="vertical">
          <Form.Item label="邮件通知">
            <Switch 
              checked={settings.email_enabled}
              onChange={value => handleChange('email_enabled', value)}
            />
            <div style={{color: '#999', fontSize: 12, marginTop: 4}}>
              启用邮件通知功能
            </div>
          </Form.Item>

          {settings.email_enabled && (
            <>
              <Form.Item label="SMTP服务器">
                <Input
                  value={settings.email_server}
                  onChange={e => handleChange('email_server', e.target.value)}
                  placeholder="例如：smtp.company.com"
                />
              </Form.Item>

              <Form.Item label="SMTP端口">
                <Input
                  type="number"
                  value={settings.email_port}
                  onChange={e => handleChange('email_port', Number(e.target.value))}
                  placeholder="587"
                />
              </Form.Item>

              <Form.Item label="用户名">
                <Input
                  value={settings.email_username}
                  onChange={e => handleChange('email_username', e.target.value)}
                  placeholder="邮箱用户名"
                />
              </Form.Item>

              <Form.Item label="密码">
                <Input.Password
                  value={settings.email_password}
                  onChange={e => handleChange('email_password', e.target.value)}
                  placeholder="邮箱密码或授权码"
                />
              </Form.Item>
            </>
          )}

          <Form.Item label="Webhook通知">
            <Switch 
              checked={settings.webhook_enabled}
              onChange={value => handleChange('webhook_enabled', value)}
            />
            <div style={{color: '#999', fontSize: 12, marginTop: 4}}>
              启用Webhook通知功能
            </div>
          </Form.Item>

          {settings.webhook_enabled && (
            <Form.Item label="Webhook URL">
              <Input
                value={settings.webhook_url}
                onChange={e => handleChange('webhook_url', e.target.value)}
                placeholder="https://your-webhook-url.com/api/notify"
              />
            </Form.Item>
          )}

          <Form.Item>
            <Button type="primary" onClick={handleSave} loading={loading}>
              保存设置
            </Button>
          </Form.Item>
        </Form>
      </div>
    </Spin>
  )
})