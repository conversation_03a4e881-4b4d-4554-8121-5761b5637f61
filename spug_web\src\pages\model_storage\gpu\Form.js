import React from 'react';
import { observer } from 'mobx-react';
import { Modal, Form, Input, message, Button } from 'antd';
import { ThunderboltOutlined, SaveOutlined } from '@ant-design/icons';
import http from 'libs/http';
import styles from './Form.module.less';

function GpuForm({ store, fetchGpus }) {
  const [form] = Form.useForm();
  const isCreating = !store.record.id;

  const handleSubmit = () => {
    form.validateFields().then(values => {
      const url = isCreating ? '/api/model-storage/gpus/' : `/api/model-storage/gpus/${store.record.id}/`;
      const httpMethod = isCreating ? http.post : http.patch;

      httpMethod(url, values)
        .then(() => {
          message.success(isCreating ? '创建成功' : '更新成功');
          store.formVisible = false;
          fetchGpus();
        });
    });
  };

  return (
    <Modal
      title={
        <div className={styles.modalTitle}>
          <ThunderboltOutlined style={{ marginRight: 8, color: '#667eea' }} />
          {isCreating ? '新建GPU设备' : '编辑GPU设备'}
        </div>
      }
      visible={store.formVisible}
      onCancel={() => store.formVisible = false}
      maskClosable={false}
      width={600}
      className={styles.gpuModal}
      footer={[
        <Button key="cancel" onClick={() => store.formVisible = false}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          icon={<SaveOutlined />}
          onClick={handleSubmit}
          className={styles.gradientButton}
        >
          {isCreating ? '创建' : '更新'}
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={store.record}
        className={styles.gpuForm}
      >
        <Form.Item
          label="GPU名称"
          name="name"
          rules={[{ required: true, message: '请输入GPU名称' }]}
        >
          <Input
            placeholder="例如：RTX 4090、A100、V100"
            size="large"
            className={styles.formInput}
          />
        </Form.Item>

        <Form.Item
          label="厂商"
          name="vendor"
          rules={[{ required: true, message: '请输入厂商名称' }]}
        >
          <Input
            placeholder="例如：NVIDIA、AMD、Kunlunxin"
            size="large"
            className={styles.formInput}
          />
        </Form.Item>

        <Form.Item
          label="设备描述"
          name="description"
        >
          <Input.TextArea
            placeholder="请输入GPU设备的详细描述信息，如性能参数、用途等"
            rows={3}
            className={styles.formTextarea}
          />
        </Form.Item>

        <Form.Item
          label="手动录入测试模型"
          name="manual_models"
          help="多个模型请用英文逗号分隔，例如：ModelA,ModelB,ModelC"
        >
          <Input.TextArea
            placeholder="例如：BERT-Base,GPT-3.5,LLaMA-7B"
            rows={3}
            className={styles.formTextarea}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default observer(GpuForm); 