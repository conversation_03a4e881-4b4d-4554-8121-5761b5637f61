import React, { useState, useRef, useEffect } from 'react';
import { Input, Button, Space, Divider, Upload, message, Tabs, Card } from 'antd';
import {
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  PictureOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  EyeOutlined,
  EditOutlined,
  TableOutlined,
  LinkOutlined,
  CodeOutlined
} from '@ant-design/icons';

const { TextArea } = Input;
const { TabPane } = Tabs;

/**
 * Markdown富文本编辑器
 * 支持Markdown语法和实时预览
 */
export default function MarkdownRichTextEditor({ 
  value = '', 
  onChange, 
  placeholder = '请输入内容...', 
  height = 300,
  disabled = false 
}) {
  const [content, setContent] = useState(value);
  const [activeTab, setActiveTab] = useState('edit');
  const [focused, setFocused] = useState(false);
  const textAreaRef = useRef(null);

  useEffect(() => {
    setContent(value);
  }, [value]);

  // 处理内容变化
  const handleChange = (e) => {
    const newContent = e.target.value;
    setContent(newContent);
    if (onChange) {
      onChange(newContent);
    }
  };

  // 获取选中文本
  const getSelectedText = () => {
    const textarea = textAreaRef.current?.resizableTextArea?.textArea;
    if (!textarea) return { start: 0, end: 0, text: '' };
    
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = textarea.value.substring(start, end);
    
    return { start, end, text };
  };

  // 插入文本
  const insertText = (beforeText, afterText = '', replaceSelected = true) => {
    const textarea = textAreaRef.current?.resizableTextArea?.textArea;
    if (!textarea) return;

    const { start, end, text } = getSelectedText();
    const currentContent = content;
    
    let newContent;
    let newCursorPos;
    
    if (replaceSelected && text) {
      // 替换选中文本
      newContent = currentContent.substring(0, start) + 
                   beforeText + text + afterText + 
                   currentContent.substring(end);
      newCursorPos = start + beforeText.length + text.length + afterText.length;
    } else {
      // 在光标位置插入
      newContent = currentContent.substring(0, start) + 
                   beforeText + afterText + 
                   currentContent.substring(start);
      newCursorPos = start + beforeText.length;
    }
    
    setContent(newContent);
    if (onChange) {
      onChange(newContent);
    }
    
    // 设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  // 格式化命令
  const formatCommands = {
    bold: () => insertText('**', '**'),
    italic: () => insertText('*', '*'),
    underline: () => insertText('<u>', '</u>'),
    code: () => insertText('`', '`'),
    heading1: () => insertText('\n# ', ''),
    heading2: () => insertText('\n## ', ''),
    heading3: () => insertText('\n### ', ''),
    orderedList: () => insertText('\n1. ', ''),
    unorderedList: () => insertText('\n- ', ''),
    link: () => insertText('[', '](url)'),
    image: () => insertText('![', '](image-url)'),
    table: () => insertText('\n| 列1 | 列2 | 列3 |\n|-----|-----|-----|\n| 内容1 | 内容2 | 内容3 |\n', ''),
    quote: () => insertText('\n> ', ''),
    divider: () => insertText('\n---\n', ''),
    alignCenter: () => insertText('\n<div align="center">\n', '\n</div>\n'),
    alignRight: () => insertText('\n<div align="right">\n', '\n</div>\n')
  };

  // 处理图片上传
  const handleImageUpload = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageMarkdown = `![${file.name}](${e.target.result})`;
      insertText(imageMarkdown, '', false);
      message.success('图片已插入，生成Word时将自动处理');
    };
    reader.readAsDataURL(file);
    return false; // 阻止默认上传
  };

  // 处理粘贴
  const handlePaste = (e) => {
    const items = e.clipboardData.items;
    
    for (let item of items) {
      if (item.type.indexOf('image') !== -1) {
        e.preventDefault();
        const file = item.getAsFile();
        handleImageUpload(file);
        break;
      }
    }
  };

  // 渲染Markdown预览
  const renderPreview = () => {
    if (!content.trim()) {
      return <div style={{ color: '#999', padding: '20px', textAlign: 'center' }}>暂无内容</div>;
    }

    // 简单的Markdown渲染（实际项目中建议使用marked或react-markdown）
    let html = content
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      .replace(/`(.*)`/gim, '<code>$1</code>')
      .replace(/!\[([^\]]*)\]\(([^)]*)\)/gim, '<img alt="$1" src="$2" style="max-width: 100%; height: auto;" />')
      .replace(/\[([^\]]*)\]\(([^)]*)\)/gim, '<a href="$2">$1</a>')
      .replace(/^- (.*$)/gim, '<li>$1</li>')
      .replace(/^1\. (.*$)/gim, '<li>$1</li>')
      .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
      .replace(/---/gim, '<hr>')
      .replace(/\n/gim, '<br>');

    return (
      <div 
        style={{ 
          padding: '12px', 
          minHeight: height,
          lineHeight: '1.6',
          fontSize: '14px'
        }}
        dangerouslySetInnerHTML={{ __html: html }}
      />
    );
  };

  // 工具栏
  const renderToolbar = () => (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      padding: '8px 12px',
      borderBottom: '1px solid #f0f0f0',
      background: '#fafafa',
      flexWrap: 'wrap'
    }}>
      <Space size="small">
        <Button
          type="text"
          size="small"
          icon={<BoldOutlined />}
          onClick={formatCommands.bold}
          disabled={disabled}
          title="粗体 (**文字**)"
        />
        <Button
          type="text"
          size="small"
          icon={<ItalicOutlined />}
          onClick={formatCommands.italic}
          disabled={disabled}
          title="斜体 (*文字*)"
        />
        <Button
          type="text"
          size="small"
          icon={<CodeOutlined />}
          onClick={formatCommands.code}
          disabled={disabled}
          title="代码 (`代码`)"
        />
      </Space>
      
      <Divider type="vertical" />
      
      <Space size="small">
        <Button
          type="text"
          size="small"
          onClick={formatCommands.heading1}
          disabled={disabled}
          title="一级标题"
        >
          H1
        </Button>
        <Button
          type="text"
          size="small"
          onClick={formatCommands.heading2}
          disabled={disabled}
          title="二级标题"
        >
          H2
        </Button>
        <Button
          type="text"
          size="small"
          onClick={formatCommands.heading3}
          disabled={disabled}
          title="三级标题"
        >
          H3
        </Button>
      </Space>
      
      <Divider type="vertical" />
      
      <Space size="small">
        <Button
          type="text"
          size="small"
          icon={<OrderedListOutlined />}
          onClick={formatCommands.orderedList}
          disabled={disabled}
          title="有序列表"
        />
        <Button
          type="text"
          size="small"
          icon={<UnorderedListOutlined />}
          onClick={formatCommands.unorderedList}
          disabled={disabled}
          title="无序列表"
        />
        <Button
          type="text"
          size="small"
          icon={<TableOutlined />}
          onClick={formatCommands.table}
          disabled={disabled}
          title="插入表格"
        />
      </Space>
      
      <Divider type="vertical" />
      
      <Space size="small">
        <Button
          type="text"
          size="small"
          icon={<LinkOutlined />}
          onClick={formatCommands.link}
          disabled={disabled}
          title="插入链接"
        />
        <Upload
          accept="image/*"
          showUploadList={false}
          beforeUpload={handleImageUpload}
          disabled={disabled}
        >
          <Button
            type="text"
            size="small"
            icon={<PictureOutlined />}
            disabled={disabled}
            title="插入图片"
          />
        </Upload>
      </Space>
    </div>
  );

  // 容器样式
  const containerStyle = {
    border: `1px solid ${focused ? '#40a9ff' : '#d9d9d9'}`,
    borderRadius: '6px',
    background: '#fff',
    transition: 'all 0.3s',
    boxShadow: focused ? '0 0 0 2px rgba(24, 144, 255, 0.2)' : 'none',
    opacity: disabled ? 0.6 : 1
  };

  return (
    <div style={containerStyle}>
      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        size="small"
        tabBarStyle={{ margin: 0, padding: '0 12px', background: '#fafafa' }}
      >
        <TabPane 
          tab={<span><EditOutlined />编辑</span>} 
          key="edit"
        >
          {renderToolbar()}
          <TextArea
            ref={textAreaRef}
            value={content}
            onChange={handleChange}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            onPaste={handlePaste}
            placeholder={placeholder}
            disabled={disabled}
            style={{
              minHeight: height,
              border: 'none',
              borderRadius: '0 0 6px 6px',
              resize: 'vertical'
            }}
            autoSize={{ minRows: 8, maxRows: 20 }}
          />
        </TabPane>
        
        <TabPane 
          tab={<span><EyeOutlined />预览</span>} 
          key="preview"
        >
          <div style={{ 
            border: 'none',
            borderRadius: '0 0 6px 6px',
            background: '#fff'
          }}>
            {renderPreview()}
          </div>
        </TabPane>
      </Tabs>
      
      {/* 提示信息 */}
      <div style={{ 
        fontSize: '12px', 
        color: '#999', 
        padding: '8px 12px',
        borderTop: '1px solid #f0f0f0',
        background: '#fafafa',
        borderRadius: '0 0 6px 6px'
      }}>
        💡 支持Markdown语法 | **粗体** *斜体* `代码` # 标题 - 列表 | 可粘贴图片 | 生成Word时格式完美保持
      </div>
    </div>
  );
}
