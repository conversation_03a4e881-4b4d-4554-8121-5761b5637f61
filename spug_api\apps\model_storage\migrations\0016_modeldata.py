# Generated by Django 2.2.28 on 2025-07-28 22:18

from django.db import migrations, models
import libs.mixins


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0015_wordtemplate_original_filename'),
    ]

    operations = [
        migrations.CreateModel(
            name='ModelData',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_name', models.CharField(max_length=256, verbose_name='模型名称')),
                ('model_version', models.CharField(default='v1.0', max_length=64, verbose_name='模型版本')),
                ('model_size', models.CharField(blank=True, default='', max_length=64, verbose_name='模型大小')),
                ('framework', models.CharField(choices=[('pytorch', 'PyTorch'), ('tensorflow', 'TensorFlow'), ('onnx', 'ONNX'), ('huggingface', 'Hugging Face'), ('other', '其他')], default='pytorch', max_length=32, verbose_name='框架')),
                ('accuracy', models.FloatField(blank=True, help_text='准确率百分比', null=True, verbose_name='准确率')),
                ('inference_speed', models.CharField(blank=True, default='', max_length=128, verbose_name='推理速度')),
                ('gpu_memory', models.CharField(blank=True, default='', max_length=64, verbose_name='GPU内存需求')),
                ('dataset', models.CharField(blank=True, default='', max_length=256, verbose_name='训练数据集')),
                ('status', models.CharField(choices=[('development', '开发中'), ('testing', '测试中'), ('production', '生产环境'), ('deprecated', '已废弃')], default='development', max_length=32, verbose_name='状态')),
                ('description', models.TextField(blank=True, default='', verbose_name='模型描述')),
                ('parameters_count', models.BigIntegerField(blank=True, null=True, verbose_name='参数量')),
                ('training_time', models.CharField(blank=True, default='', max_length=64, verbose_name='训练时长')),
                ('model_file_path', models.CharField(blank=True, default='', max_length=512, verbose_name='模型文件路径')),
                ('config_file_path', models.CharField(blank=True, default='', max_length=512, verbose_name='配置文件路径')),
                ('latency', models.FloatField(blank=True, null=True, verbose_name='延迟(ms)')),
                ('throughput', models.FloatField(blank=True, null=True, verbose_name='吞吐量(req/s)')),
                ('memory_usage', models.CharField(blank=True, default='', max_length=64, verbose_name='内存使用量')),
                ('created_by', models.CharField(blank=True, default='', max_length=128, verbose_name='创建者')),
                ('tags', models.TextField(blank=True, default='', help_text='JSON格式存储标签列表', verbose_name='标签')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '模型数据',
                'verbose_name_plural': '模型数据',
                'db_table': 'model_storage_model_data',
                'ordering': ('-created_at',),
                'unique_together': {('model_name', 'model_version')},
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
