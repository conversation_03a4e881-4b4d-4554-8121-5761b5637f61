# Generated by Django 2.2.28 on 2025-08-05 17:47

from django.db import migrations, models
import libs.mixins


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0019_auto_20250805_1658'),
    ]

    operations = [
        migrations.CreateModel(
            name='ModelPerformanceData',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_name', models.CharField(max_length=255, unique=True, verbose_name='模型名称')),
                ('framework', models.CharField(blank=True, default='', max_length=128, verbose_name='框架')),
                ('framework_version', models.CharField(blank=True, default='', max_length=64, verbose_name='框架版本')),
                ('model_version', models.CharField(blank=True, default='', max_length=64, verbose_name='模型版本')),
                ('test_results', models.TextField(default='[]', help_text='存储完整的测试结果表格，每行一个测试文件的数据，JSON格式', verbose_name='测试结果数据')),
                ('total_test_files', models.IntegerField(default=0, verbose_name='测试文件总数')),
                ('last_test_date', models.DateTimeField(blank=True, null=True, verbose_name='最后测试时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '模型性能数据',
                'verbose_name_plural': '模型性能数据',
                'db_table': 'model_storage_model_performance_data',
                'ordering': ('-updated_at',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
