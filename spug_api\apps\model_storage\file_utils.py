# -*- coding: utf-8 -*-
"""
安全的文件操作工具类
"""

import os
import json
import tempfile
import logging
import threading
import time
from pathlib import Path
from typing import Any, Dict, Optional, Union
from django.conf import settings
from contextlib import contextmanager

# 跨平台文件锁支持
try:
    import fcntl
    HAS_FCNTL = True
except ImportError:
    # Windows 系统没有 fcntl 模块
    HAS_FCNTL = False
    try:
        import msvcrt
        HAS_MSVCRT = True
    except ImportError:
        HAS_MSVCRT = False

logger = logging.getLogger('model_storage.file_utils')

class FileOperationError(Exception):
    """文件操作异常"""
    pass

class FileLockManager:
    """跨平台文件锁管理器"""

    def __init__(self):
        self._locks = {}
        self._lock = threading.Lock()

    def _acquire_file_lock(self, file_handle, exclusive=True):
        """获取文件锁（跨平台实现）"""
        if HAS_FCNTL:
            # Unix/Linux 系统使用 fcntl
            lock_type = fcntl.LOCK_EX if exclusive else fcntl.LOCK_SH
            fcntl.flock(file_handle.fileno(), lock_type | fcntl.LOCK_NB)
        elif HAS_MSVCRT:
            # Windows 系统使用 msvcrt
            try:
                msvcrt.locking(file_handle.fileno(), msvcrt.LK_NBLCK, 1)
            except OSError:
                raise FileOperationError("无法获取文件锁")
        else:
            # 如果都不支持，只使用线程锁
            logger.warning("系统不支持文件锁，仅使用线程锁")

    def _release_file_lock(self, file_handle):
        """释放文件锁（跨平台实现）"""
        if HAS_FCNTL:
            fcntl.flock(file_handle.fileno(), fcntl.LOCK_UN)
        elif HAS_MSVCRT:
            try:
                msvcrt.locking(file_handle.fileno(), msvcrt.LK_UNLCK, 1)
            except OSError:
                pass  # 忽略释放锁时的错误

    @contextmanager
    def acquire_lock(self, file_path: str, timeout: float = 30.0):
        """获取文件锁"""
        file_path = os.path.abspath(file_path)

        # 首先获取线程锁
        with self._lock:
            if file_path not in self._locks:
                self._locks[file_path] = threading.Lock()
            thread_lock = self._locks[file_path]

        # 尝试获取线程锁
        acquired = thread_lock.acquire(timeout=timeout)
        if not acquired:
            raise FileOperationError(f"无法在 {timeout} 秒内获取线程锁: {file_path}")

        try:
            yield
        finally:
            thread_lock.release()

# 全局文件锁管理器
file_lock_manager = FileLockManager()

class SafeFileOperations:
    """安全的文件操作类"""

    def __init__(self):
        self._temp_dir = None

    @property
    def temp_dir(self) -> str:
        """获取临时目录路径（延迟初始化）"""
        if self._temp_dir is None:
            self._temp_dir = self._get_temp_dir()
            self.ensure_directory_exists(self._temp_dir)
        return self._temp_dir

    def _get_temp_dir(self) -> str:
        """获取临时目录路径"""
        try:
            # 优先使用配置的临时目录
            from django.conf import settings
            temp_dir = getattr(settings, 'MODEL_STORAGE_TEMP_DIR', None)
            if temp_dir:
                return temp_dir
        except Exception:
            # 如果Django settings不可用，使用默认路径
            pass

        # 使用系统临时目录下的子目录
        system_temp = tempfile.gettempdir()
        return os.path.join(system_temp, 'spug_model_storage')
    
    def ensure_directory_exists(self, directory: str) -> None:
        """确保目录存在"""
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
        except PermissionError:
            raise FileOperationError(f"没有权限创建目录: {directory}")
        except OSError as e:
            raise FileOperationError(f"创建目录失败: {directory}, 错误: {e}")
    
    def check_file_permissions(self, file_path: str, operation: str = 'read') -> bool:
        """检查文件权限"""
        try:
            if operation == 'read':
                return os.access(file_path, os.R_OK)
            elif operation == 'write':
                if os.path.exists(file_path):
                    return os.access(file_path, os.W_OK)
                else:
                    # 检查父目录的写权限
                    parent_dir = os.path.dirname(file_path)
                    return os.access(parent_dir, os.W_OK)
            elif operation == 'execute':
                return os.access(file_path, os.X_OK)
            else:
                return False
        except Exception:
            return False
    
    def get_safe_file_path(self, filename: str, prefix: str = '') -> str:
        """获取安全的文件路径"""
        # 清理文件名，移除危险字符
        safe_filename = "".join(c for c in filename if c.isalnum() or c in ('-', '_', '.'))
        if prefix:
            safe_filename = f"{prefix}_{safe_filename}"
        
        return os.path.join(self.temp_dir, safe_filename)
    
    @contextmanager
    def safe_file_write(self, file_path: str, mode: str = 'w', encoding: str = 'utf-8'):
        """安全的文件写入上下文管理器"""
        file_path = os.path.abspath(file_path)

        # 检查权限
        if not self.check_file_permissions(file_path, 'write'):
            raise FileOperationError(f"没有写入权限: {file_path}")

        # 确保父目录存在
        parent_dir = os.path.dirname(file_path)
        self.ensure_directory_exists(parent_dir)

        # 使用临时文件进行原子写入
        temp_file = f"{file_path}.tmp.{int(time.time())}.{os.getpid()}"

        try:
            with file_lock_manager.acquire_lock(file_path):
                # 创建临时文件并写入
                with open(temp_file, mode, encoding=encoding) as f:
                    # 如果支持文件锁，在临时文件上也加锁
                    if HAS_FCNTL or HAS_MSVCRT:
                        try:
                            file_lock_manager._acquire_file_lock(f, exclusive=True)
                        except Exception as e:
                            logger.warning(f"无法获取文件锁，继续使用线程锁: {e}")

                    yield f

                    # 确保数据写入磁盘
                    f.flush()
                    os.fsync(f.fileno())

                # 原子性地移动临时文件到目标位置
                if os.name == 'nt':  # Windows
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    os.rename(temp_file, file_path)
                else:  # Unix/Linux
                    os.rename(temp_file, file_path)

        except Exception as e:
            # 清理临时文件
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except Exception:
                    pass
            raise FileOperationError(f"文件写入失败: {file_path}, 错误: {e}")
    
    @contextmanager
    def safe_file_read(self, file_path: str, mode: str = 'r', encoding: str = 'utf-8'):
        """安全的文件读取上下文管理器"""
        file_path = os.path.abspath(file_path)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileOperationError(f"文件不存在: {file_path}")

        # 检查权限
        if not self.check_file_permissions(file_path, 'read'):
            raise FileOperationError(f"没有读取权限: {file_path}")

        try:
            with file_lock_manager.acquire_lock(file_path):
                with open(file_path, mode, encoding=encoding) as f:
                    # 如果支持文件锁，加共享锁
                    if HAS_FCNTL or HAS_MSVCRT:
                        try:
                            file_lock_manager._acquire_file_lock(f, exclusive=False)
                        except Exception as e:
                            logger.warning(f"无法获取文件锁，继续使用线程锁: {e}")

                    yield f
        except Exception as e:
            raise FileOperationError(f"文件读取失败: {file_path}, 错误: {e}")
    
    def safe_json_write(self, file_path: str, data: Any, indent: int = None) -> None:
        """安全的JSON文件写入"""
        try:
            with self.safe_file_write(file_path, 'w', 'utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=indent)
            logger.debug(f"JSON文件写入成功: {file_path}")
        except Exception as e:
            logger.error(f"JSON文件写入失败: {file_path}, 错误: {e}")
            raise
    
    def safe_json_read(self, file_path: str, default: Any = None) -> Any:
        """安全的JSON文件读取"""
        try:
            with self.safe_file_read(file_path, 'r', 'utf-8') as f:
                data = json.load(f)
            logger.debug(f"JSON文件读取成功: {file_path}")
            return data
        except FileOperationError:
            if default is not None:
                logger.warning(f"JSON文件读取失败，返回默认值: {file_path}")
                return default
            raise
        except json.JSONDecodeError as e:
            logger.error(f"JSON文件格式错误: {file_path}, 错误: {e}")
            if default is not None:
                return default
            raise FileOperationError(f"JSON文件格式错误: {file_path}")
        except Exception as e:
            logger.error(f"JSON文件读取异常: {file_path}, 错误: {e}")
            raise FileOperationError(f"JSON文件读取异常: {file_path}")
    
    def cleanup_old_files(self, directory: str, max_age_hours: int = 24) -> int:
        """清理旧文件"""
        if not os.path.exists(directory):
            return 0
        
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        cleaned_count = 0
        
        try:
            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                if os.path.isfile(file_path):
                    file_age = current_time - os.path.getmtime(file_path)
                    if file_age > max_age_seconds:
                        try:
                            os.remove(file_path)
                            cleaned_count += 1
                            logger.debug(f"清理旧文件: {file_path}")
                        except Exception as e:
                            logger.warning(f"清理文件失败: {file_path}, 错误: {e}")
        except Exception as e:
            logger.error(f"清理目录失败: {directory}, 错误: {e}")
        
        return cleaned_count

# 全局安全文件操作实例
safe_file_ops = SafeFileOperations()

class NetworkIOTracker:
    """网络IO跟踪器"""

    def __init__(self):
        self._file_path = None

    @property
    def file_path(self) -> str:
        """获取文件路径（延迟初始化）"""
        if self._file_path is None:
            self._file_path = safe_file_ops.get_safe_file_path('server_metrics_netio.json', 'netio')
        return self._file_path
    
    def save_network_data(self, bytes_sent: int, bytes_recv: int, timestamp: float) -> bool:
        """保存网络数据"""
        data = {
            'bytes_sent': bytes_sent,
            'bytes_recv': bytes_recv,
            'time': timestamp
        }
        
        try:
            safe_file_ops.safe_json_write(self.file_path, data)
            return True
        except FileOperationError as e:
            logger.error(f"保存网络IO数据失败: {e}")
            return False
    
    def load_network_data(self) -> Optional[Dict[str, Union[int, float]]]:
        """加载网络数据"""
        try:
            return safe_file_ops.safe_json_read(self.file_path)
        except FileOperationError:
            return None
    
    def cleanup_old_data(self) -> None:
        """清理旧数据"""
        safe_file_ops.cleanup_old_files(safe_file_ops.temp_dir, max_age_hours=24)

# 全局网络IO跟踪器实例
network_io_tracker = NetworkIOTracker()
