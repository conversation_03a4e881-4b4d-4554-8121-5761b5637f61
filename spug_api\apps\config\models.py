# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from django.db import models
from libs import ModelMixin, human_datetime
from apps.account.models import User


class Environment(models.Model, ModelMixin):
    name = models.CharField(max_length=50)
    key = models.CharField(max_length=50)
    desc = models.CharField(max_length=255, null=True)
    sort_id = models.IntegerField(default=0, db_index=True)
    created_at = models.CharField(max_length=20, default=human_datetime)
    created_by = models.ForeignKey(User, on_delete=models.PROTECT)

    def __repr__(self):
        return f'<Environment {self.name!r}>'

    class Meta:
        db_table = 'environments'
        ordering = ('-sort_id',)


# 新增配套管理模型（复用Environment表结构，但语义不同）
class Package(models.Model, ModelMixin):
    name = models.Char<PERSON><PERSON>(max_length=50, verbose_name="配套名称")
    key = models.CharField(max_length=50, verbose_name="配套标识")
    desc = models.Char<PERSON><PERSON>(max_length=255, null=True, verbose_name="配套描述")
    sort_id = models.IntegerField(default=0, db_index=True)
    created_at = models.CharField(max_length=20, default=human_datetime)
    created_by = models.ForeignKey(User, on_delete=models.PROTECT)

    def __repr__(self):
        return f'<Package {self.name!r}>'

    class Meta:
        db_table = 'packages'
        ordering = ('-sort_id',)


class Service(models.Model, ModelMixin):
    name = models.CharField(max_length=50)
    key = models.CharField(max_length=50, unique=True)
    desc = models.CharField(max_length=255, null=True)
    created_at = models.CharField(max_length=20, default=human_datetime)
    created_by = models.ForeignKey(User, on_delete=models.PROTECT)

    def __repr__(self):
        return f'<Service {self.name!r}>'

    class Meta:
        db_table = 'services'
        ordering = ('-id',)


class Config(models.Model, ModelMixin):
    TYPES = (
        ('app', 'App'),
        ('src', 'Service'),
        ('task', 'Task')  # 新增任务类型
    )
    type = models.CharField(max_length=5, choices=TYPES)
    o_id = models.IntegerField()
    key = models.CharField(max_length=50, verbose_name="版本号")
    env = models.ForeignKey(Environment, on_delete=models.PROTECT, null=True, blank=True)
    package = models.ForeignKey(Package, on_delete=models.PROTECT, null=True, blank=True, verbose_name="关联配套")
    value = models.TextField(null=True, verbose_name="文件路径")
    desc = models.CharField(max_length=255, null=True, verbose_name="备注")
    is_public = models.BooleanField(default=False)
    updated_at = models.CharField(max_length=20)
    updated_by = models.ForeignKey(User, on_delete=models.PROTECT)

    def __repr__(self):
        return f'<Config {self.key!r}>'

    class Meta:
        db_table = 'configs'
        ordering = ('-key',)


# 新增任务版本配套模型
class TaskVersionPackage(models.Model, ModelMixin):
    task_id = models.IntegerField(verbose_name="任务ID")
    package = models.ForeignKey(Package, on_delete=models.PROTECT, verbose_name="配套")
    version = models.CharField(max_length=50, verbose_name="版本号")
    file_path = models.TextField(verbose_name="文件路径")
    desc = models.CharField(max_length=255, null=True, verbose_name="备注")
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    created_at = models.CharField(max_length=20, default=human_datetime)
    created_by = models.ForeignKey(User, on_delete=models.PROTECT)
    updated_at = models.CharField(max_length=20, null=True)
    updated_by = models.ForeignKey(User, on_delete=models.PROTECT, null=True, related_name='+')

    def __repr__(self):
        return f'<TaskVersionPackage task_id={self.task_id} package={self.package.key} version={self.version}>'

    class Meta:
        db_table = 'task_version_packages'
        ordering = ('-id',)
        unique_together = [['task_id', 'package', 'version']]


class ConfigHistory(models.Model, ModelMixin):
    ACTIONS = (
        ('1', '新增'),
        ('2', '更新'),
        ('3', '删除')
    )
    type = models.CharField(max_length=5)
    o_id = models.IntegerField()
    key = models.CharField(max_length=50)
    env_id = models.IntegerField(null=True)
    package_id = models.IntegerField(null=True)
    value = models.TextField(null=True)
    desc = models.CharField(max_length=255, null=True)
    is_public = models.BooleanField()
    old_value = models.TextField(null=True)
    action = models.CharField(max_length=2, choices=ACTIONS)
    updated_at = models.CharField(max_length=20)
    updated_by = models.ForeignKey(User, on_delete=models.PROTECT)

    def __repr__(self):
        return f'<ConfigHistory {self.key!r}>'

    class Meta:
        db_table = 'config_histories'
        ordering = ('key',)
