.modern-log-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f0f2f5;
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background-color: #fff;
    padding: 16px;
  }
  
  .log-toolbar {
    margin-bottom: 8px;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }
  
  .log-container {
    flex: 1;
    border-radius: 4px;
    overflow: auto;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    
    .log-line {
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }
    }
  }
}

/* 滚动条样式 */
.log-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.log-container::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.log-container::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* 日志级别颜色 */
.log-level-info {
  border-left-color: #1890ff;
}

.log-level-warning {
  border-left-color: #faad14;
}

.log-level-error {
  border-left-color: #ff4d4f;
}

.log-level-success {
  border-left-color: #52c41a;
}

/* 搜索高亮 */
.search-highlight {
  background-color: #ffeb3b;
  color: #000;
  padding: 1px 2px;
  border-radius: 2px;
}

.search-highlight-current {
  background-color: #ff9500;
  color: #fff;
  padding: 1px 2px;
  border-radius: 2px;
} 