/**
 * 配套管理表单
 */
import React, { useState } from 'react';
import { observer } from 'mobx-react';
import { Modal, Form, Input, InputNumber, message } from 'antd';
import http from 'libs/http';
import store from './store';

export default observer(function ComForm() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = () => {
    setLoading(true);
    const formData = form.getFieldsValue();
    formData['id'] = store.record.id;
    http.post('/api/config/package/', formData)
      .then(res => {
        message.success('操作成功');
        store.formVisible = false;
        store.fetchRecords()
      })
      .catch(() => setLoading(false));
  };

  const info = store.record;
  return (
    <Modal
      visible
      width={800}
      maskClosable={false}
      title={store.record.id ? '编辑配套' : '新增配套'}
      onCancel={() => store.formVisible = false}
      confirmLoading={loading}
      onOk={handleSubmit}>
      <Form 
        form={form} 
        labelCol={{span: 6}} 
        wrapperCol={{span: 14}}
        initialValues={info}>
        <Form.Item 
          name="name"
          label="配套名称"
          rules={[{ required: true, message: '请输入配套名称' }]}>
          <Input placeholder="请输入配套名称"/>
        </Form.Item>
        <Form.Item 
          name="key"
          label="配套标识"
          rules={[{ required: true, message: '请输入配套标识' }]}>
          <Input placeholder="请输入配套标识"/>
        </Form.Item>
        <Form.Item 
          name="desc"
          label="描述信息">
          <Input.TextArea rows={3} placeholder="请输入描述信息"/>
        </Form.Item>
        <Form.Item 
          name="sort_id"
          label="排序">
          <InputNumber min={0} placeholder="请输入排序值"/>
        </Form.Item>
      </Form>
    </Modal>
  )
}) 