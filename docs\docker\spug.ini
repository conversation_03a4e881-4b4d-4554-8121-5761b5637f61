[supervisord]
nodaemon=true

[program:nginx]
command = nginx -g "daemon off;"
autostart = true

[program:redis]
command = redis-server /etc/redis.conf
autostart = true

[program:spug-api]
command = sh /data/spug/spug_api/tools/start-api.sh
autostart = true
stdout_logfile = /data/spug/spug_api/logs/api.log
redirect_stderr = true

[program:spug-ws]
command = sh /data/spug/spug_api/tools/start-ws.sh
autostart = true
stdout_logfile = /data/spug/spug_api/logs/ws.log
redirect_stderr = true

[program:spug-worker]
command = sh /data/spug/spug_api/tools/start-worker.sh
autostart = true
stdout_logfile = /data/spug/spug_api/logs/worker.log
redirect_stderr = true

[program:spug-monitor]
command = sh /data/spug/spug_api/tools/start-monitor.sh
autostart = true
startsecs = 3
stdout_logfile = /data/spug/spug_api/logs/monitor.log
redirect_stderr = true

[program:spug-scheduler]
command = sh /data/spug/spug_api/tools/start-scheduler.sh
autostart = true
startsecs = 3
stdout_logfile = /data/spug/spug_api/logs/scheduler.log
redirect_stderr = true
