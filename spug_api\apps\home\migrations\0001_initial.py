# Generated by Django 2.2.28 on 2025-07-02 16:52

from django.db import migrations, models
import libs.mixins


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Navigation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=64)),
                ('desc', models.CharField(max_length=128)),
                ('logo', models.TextField()),
                ('links', models.TextField()),
                ('sort_id', models.IntegerField(db_index=True, default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'navigations',
                'ordering': ('-sort_id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='Notice',
            fields=[
                ('id', models.<PERSON>Field(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('content', models.Text<PERSON>ield()),
                ('is_stress', models.BooleanField(default=False)),
                ('read_ids', models.TextField(default='[]')),
                ('sort_id', models.IntegerField(db_index=True, default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'notices',
                'ordering': ('-sort_id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
