/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React from 'react';
import {
  DesktopOutlined,
  CloudServerOutlined,
  CodeOutlined,
  DeploymentUnitOutlined,
  AlertOutlined,
  SettingOutlined,
  DatabaseOutlined,
  MonitorOutlined,
  FileTextOutlined
} from '@ant-design/icons';

import HomeIndex from './pages/home';
import HostIndex from './pages/host';
import ExecTask from './pages/exec/task';
import ExecTemplate from './pages/exec/template';
import ExecTransfer from './pages/exec/transfer';
import FileManager from './pages/file/FileManager';
import FileEditor from './pages/file/FileEditor';
import CommandCards from './pages/exec/command-cards';
import TestPlanEditor from './pages/exec/command-cards/TestPlanEditor';
import TestCaseSets from './pages/exec/test-case-sets';
import TestCaseSetEditor from './pages/exec/test-case-sets/TestCaseSetEditor';
import TestCaseTemplates from './pages/exec/test-case-templates';
import ResultExtraction from './pages/exec/result-extraction';
import TestResults from './pages/exec/test-results';
// import VTableDemo from './pages/exec/result-extraction/VTableDemo';
import VTableAnalysisPage from './pages/exec/result-extraction/VTableAnalysisPage';
import AntdTableAnalysisPage from './pages/exec/result-extraction/AntdTableAnalysisPage';
import AdvancedTableAnalysisPage from './pages/exec/result-extraction/AdvancedTableAnalysisPage';
import AGGridComparePage from './pages/exec/result-extraction/AGGridComparePage';
import LogAnalysisPage from './pages/exec/log-analysis';
import FileDetailCompare from './pages/model-storage/FileDetailCompare';
import DocStatistics from './pages/model-storage/DocStatistics';
import ReleasePlan from './pages/model-storage/ReleasePlan';
import ReleasePlanGantt from './pages/model-storage/ReleasePlanGantt';
import ModelPerformanceManager from './pages/model_storage/ModelPerformanceManager';
// import DeployApp from './pages/deploy/app';
// import DeployRepository from './pages/deploy/repository';
// import DeployRequest from './pages/deploy/request';
// import ScheduleIndex from './pages/schedule';
import ConfigEnvironment from './pages/config/environment';
// import ConfigService from './pages/config/service';
// import ConfigApp from './pages/config/app';
import ConfigTask from './pages/config/task';
import ConfigSetting from './pages/config/setting';
import MonitorIndex from './pages/monitor';
import AlarmIndex from './pages/alarm/alarm';
import AlarmGroup from './pages/alarm/group';
import AlarmContact from './pages/alarm/contact';
import SystemAccount from './pages/system/account';
import SystemRole from './pages/system/role';
import SystemSetting from './pages/system/setting';
import SystemLogin from './pages/system/login';
import WelcomeIndex from './pages/welcome/index';
import WelcomeInfo from './pages/welcome/info';
import GpuDashboard from './pages/host/gpu-dashboard';
import ModelStorageMonitor from './pages/model-storage';
import GpuManager from './pages/model_storage/gpu';
import ModelDataTableTanStack from './pages/model_storage/ModelDataTableTanStack';
import WordTemplateManager from './pages/model-storage/WordTemplateManager';
import WordTemplateEditor from './pages/model-storage/WordTemplateEditor';
import TemplateEditTest from './pages/model-storage/TemplateEditTest';
import DocumentGenerator from './pages/model-storage/DocumentGenerator';
import DocumentInstanceManager from './pages/model-storage/DocumentInstanceManager';

export default [
  {icon: <DesktopOutlined/>, title: '工作台', path: '/home', component: HomeIndex},
  // {
  //   icon: <DashboardOutlined/>,
  //   title: 'Dashboard',
  //   auth: 'dashboard.dashboard.view',
  //   path: '/dashboard',
  //   component: DashboardIndex
  // },
  // {
  //   icon: <FlagOutlined/>, title: '应用发布', auth: 'deploy.app.view|deploy.repository.view|deploy.request.view', child: [
  //     {title: '发布配置', auth: 'deploy.app.view', path: '/deploy/app', component: DeployApp},
  //     {title: '构建仓库', auth: 'deploy.repository.view', path: '/deploy/repository', component: DeployRepository},
  //     {title: '发布申请', auth: 'deploy.request.view', path: '/deploy/request', component: DeployRequest},
  //   ]
  // },
  {
    icon: <DeploymentUnitOutlined/>, title: '模型测试规划', auth: 'config.package.view|config.task.view|exec.command_cards.view|exec.test_case_sets.view|exec.test_results.view', child: [
      {title: '测试计划', auth: 'exec.command_cards.view', path: '/exec/command-cards', component: CommandCards},
      {title: '测试用例集', auth: 'exec.test_case_sets.view', path: '/exec/test-case-sets', component: TestCaseSets},
      {title: '用例池', auth: 'exec.task.do', path: '/exec/test-case-templates', component: TestCaseTemplates},
      {title: '结果展示', auth: 'exec.test_results.view', path: '/exec/test-results', component: TestResults},
      {title: '日志分析', auth: 'exec.task.do', path: '/exec/log-analysis', component: LogAnalysisPage},
      {title: '配套管理', auth: 'config.package.view', path: '/config/environment', component: ConfigEnvironment},
      {title: '任务管理', auth: 'config.task.view', path: '/config/task', component: ConfigTask},
      {path: '/exec/test-plan/:id', component: TestPlanEditor},
      {path: '/exec/test-case-set/:id', component: TestCaseSetEditor},
      {path: '/config/setting/:type/:id', component: ConfigSetting},
    ]
  },
  {
    icon: <DatabaseOutlined/>,
    title: '模型任务管理',
    auth: 'model_storage.storage.view|model_storage.gpu_management.view|model_storage.file_compare.view|model_storage.doc_statistics.view|model_storage.release_plan.view|model_storage.test_tasks.view|model_storage.performance_test_data.view',
    child: [
      {title: '监控概览', auth: 'model_storage.storage.view', path: '/model-storage/dashboard', component: ModelStorageMonitor},
      {title: 'GPU管理', auth: 'model_storage.gpu_management.view', path: '/model-storage/gpu', component: GpuManager},
      {title: '性能数据管理', auth: 'model_storage.model_performance_data.view', path: '/model-storage/performance-manager', component: ModelPerformanceManager},
      {title: '文件对比', auth: 'model_storage.file_compare.view', path: '/model-storage/file-compare', component: FileDetailCompare},
      {title: '文档统计', auth: 'model_storage.doc_statistics.view', path: '/model-storage/doc-statistics', component: DocStatistics},
      {title: '发布计划', auth: 'model_storage.release_plan.view', path: '/model-storage/release-plan', component: ReleasePlan},

    ]
  },
  {
    icon: <FileTextOutlined/>,
    title: '文档管理',
    auth: 'document_management.word_template.view|document_management.document_generator.view|document_management.document_instance.view',
    child: [
      {title: 'Word模板管理', auth: 'document_management.word_template.view', path: '/document-management/word-templates', component: WordTemplateManager},
      {title: '文档生成器', auth: 'document_management.document_generator.view', path: '/document-management/document-generator', component: DocumentGenerator},
      {title: '文档实例管理', auth: 'document_management.document_instance.view', path: '/document-management/document-instances', component: DocumentInstanceManager},
    ]
  },
  // {
  //   icon: <ScheduleOutlined/>,
  //   title: '任务计划',
  //   auth: 'schedule.schedule.view',
  //   path: '/schedule',
  //   component: ScheduleIndex
  // },
  {icon: <CloudServerOutlined/>, title: '主机管理', auth: 'host.host.view', path: '/host', component: HostIndex},
  {
    icon: <CodeOutlined/>, title: '批量执行', auth: 'exec.task.do|exec.template.view|exec.transfer.do', child: [
      {title: '执行任务', auth: 'exec.task.do', path: '/exec/task', component: ExecTask},
      {title: '模板管理', auth: 'exec.template.view', path: '/exec/template', component: ExecTemplate},
      {title: '文件分发', auth: 'exec.transfer.do', path: '/exec/transfer', component: ExecTransfer},
      {title: '文件管理', auth: 'exec.transfer.file_manager', path: '/file/manager', component: FileManager},
    ]
  },
  {icon: <MonitorOutlined/>, title: '监控中心', auth: 'monitor.monitor.view', path: '/monitor', component: MonitorIndex},

  {
    icon: <AlertOutlined/>, title: '报警中心', auth: 'alarm.alarm.view|alarm.contact.view|alarm.group.view', child: [
      {title: '报警历史', auth: 'alarm.alarm.view', path: '/alarm/alarm', component: AlarmIndex},
      {title: '报警联系人', auth: 'alarm.contact.view', path: '/alarm/contact', component: AlarmContact},
      {title: '报警联系组', auth: 'alarm.group.view', path: '/alarm/group', component: AlarmGroup},
    ]
  },
  {
    icon: <SettingOutlined/>, title: '系统管理', auth: "system.account.view|system.role.view|system.setting.view|system.login.view", child: [
      {title: '登录日志', auth: 'system.login.view', path: '/system/login', component: SystemLogin},
      {title: '账户管理', auth: 'system.account.view', path: '/system/account', component: SystemAccount},
      {title: '角色管理', auth: 'system.role.view', path: '/system/role', component: SystemRole},
      {title: '系统设置', auth: 'system.setting.view', path: '/system/setting', component: SystemSetting},
    ]
  },
  {path: '/welcome/index', component: WelcomeIndex},
  {path: '/welcome/info', component: WelcomeInfo},
  {path: '/file/editor', component: FileEditor},
  {path: '/host/gpu-dashboard/:id', component: GpuDashboard},
  {path: '/result-extraction', component: ResultExtraction},
  {path: '/exec/result-extraction/vtable-analysis', component: VTableAnalysisPage},
  {path: '/exec/result-extraction/antd-table-analysis', component: AntdTableAnalysisPage},
  {path: '/exec/result-extraction/advanced-table', component: AdvancedTableAnalysisPage},
  {path: '/exec/result-extraction/ag-grid-compare', component: AGGridComparePage},
  {path: '/model-storage/file-compare', component: FileDetailCompare},
  {path: '/model-storage/doc-statistics', component: DocStatistics},
  {path: '/model-storage/release-plan', component: ReleasePlan},
  {path: '/model-storage/release-plan-gantt/:id', component: ReleasePlanGantt},
  {path: '/model-storage/gpu', component: GpuManager},
  {path: '/model-storage/performance-manager', component: ModelPerformanceManager},
  {path: '/model-storage/model-data/:modelName', component: ModelDataTableTanStack}, // 使用TanStack React Table
  {path: '/model-storage/word-templates', component: WordTemplateManager},
  {path: '/model-storage/word-templates/edit/:id', component: WordTemplateEditor},
  {path: '/model-storage/template-edit-test', component: TemplateEditTest},
  {path: '/model-storage/document-generator', component: DocumentGenerator},
  {path: '/model-storage/document-instances', component: DocumentInstanceManager},

  // 新的文档管理路径
  {path: '/document-management/word-templates', component: WordTemplateManager},
  {path: '/document-management/word-templates/edit/:id', component: WordTemplateEditor},
  {path: '/document-management/document-generator', component: DocumentGenerator},
  {path: '/document-management/document-instances', component: DocumentInstanceManager},
]
