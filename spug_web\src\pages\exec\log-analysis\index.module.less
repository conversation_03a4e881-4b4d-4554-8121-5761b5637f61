.logAnalysisPage {
  min-height: 100vh;
  background: #f0f2f5;

  .pageHeader {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 0 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .headerContent {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;

      .headerLeft {
        .ant-breadcrumb {
          margin-bottom: 8px;
          
          :global(.ant-breadcrumb-link) {
            color: rgba(255, 255, 255, 0.8);
          }
          
          :global(.ant-breadcrumb-separator) {
            color: rgba(255, 255, 255, 0.6);
          }
        }

        h3 {
          color: #fff !important;
          margin: 8px 0 0 0 !important;
          font-weight: 600;
        }
      }

      .headerRight {
        :global(.ant-btn) {
          border-radius: 6px;
          font-weight: 500;
        }

        :global(.ant-btn-primary) {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.3);
          backdrop-filter: blur(10px);

          &:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.4);
          }
        }

        :global(.ant-btn:not(.ant-btn-primary)) {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.2);
          color: #fff;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            color: #fff;
          }
        }
      }
    }
  }

  .pageContent {
    padding: 24px;

    .contentWrapper {
      max-width: 1200px;
      margin: 0 auto;

      .welcomeCard {
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border: none;

        :global(.ant-card-body) {
          padding: 48px;
        }

        .welcomeContent {
          text-align: center;

          .welcomeIcon {
            font-size: 72px;
            color: #722ed1;
            margin-bottom: 24px;
          }

          .featureList {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 32px;

            .featureItem {
              display: flex;
              align-items: flex-start;
              gap: 16px;
              padding: 24px;
              background: #fafafa;
              border-radius: 8px;
              text-align: left;

              .featureIcon {
                font-size: 24px;
                color: #722ed1;
                margin-top: 4px;
              }

              h4 {
                margin: 0 0 8px 0;
                font-size: 16px;
                font-weight: 600;
                color: #262626;
              }

              p {
                margin: 0;
                color: #666;
                line-height: 1.5;
              }
            }
          }
        }
      }

      .resultsContainer {
        .ant-alert {
          border-radius: 8px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .logAnalysisPage {
    .pageHeader {
      padding: 0 16px;

      .headerContent {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        .headerRight {
          width: 100%;
          
          :global(.ant-space) {
            width: 100%;
            justify-content: flex-end;
          }
        }
      }
    }

    .pageContent {
      padding: 16px;

      .contentWrapper {
        .welcomeCard {
          :global(.ant-card-body) {
            padding: 24px;
          }

          .welcomeContent {
            .featureList {
              grid-template-columns: 1fr;
              gap: 16px;

              .featureItem {
                padding: 16px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .logAnalysisPage {
    .pageHeader {
      .headerContent {
        .headerLeft {
          h3 {
            font-size: 18px !important;
          }
        }

        .headerRight {
          :global(.ant-btn) {
            font-size: 12px;
            padding: 4px 8px;
            height: auto;
          }
        }
      }
    }

    .pageContent {
      .contentWrapper {
        .welcomeCard {
          .welcomeContent {
            .welcomeIcon {
              font-size: 48px;
            }

            h2 {
              font-size: 20px !important;
            }

            .featureList {
              .featureItem {
                flex-direction: column;
                text-align: center;
                gap: 12px;

                .featureIcon {
                  font-size: 20px;
                  margin-top: 0;
                }
              }
            }
          }
        }
      }
    }
  }
}
