.commandCardsPage {
  .commandCard {
    margin-bottom: 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
      border-color: #1890ff;
    }
    
    .cardTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      
      span {
        flex: 1;
        margin-right: 8px;
      }
    }
    
    .cardContent {
      .description {
        color: #666;
        margin-bottom: 12px;
        font-size: 13px;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      .commandCount {
        margin-bottom: 8px;
      }
      
      .commandPreview {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }
    }
  }
  
  .selectedCard {
    border: 2px solid #1890ff !important;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2) !important;
    transform: scale(1.02);
    
    &:hover {
      transform: scale(1.02) translateY(-1px);
    }
  }
}

// 拖拽排序样式
.dragHandle {
  color: #999;
  cursor: grab;
  font-size: 16px;
  
  &:hover {
    color: #1890ff;
  }
  
  &:active {
    cursor: grabbing;
  }
}

.rowDragging {
  background: #fafafa !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  
  td {
    background: #fafafa !important;
  }
} 