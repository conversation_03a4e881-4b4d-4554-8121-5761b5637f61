/**
 * 监控卡片预览组件 - 用于测试新的卡片设计
 */
import React from 'react';
import { Card, Space, Tooltip } from 'antd';
import styles from './index.module.less';

// 模拟数据
const mockData = [
  {
    id: '1_7',
    name: 'python进程监控',
    type: '智能进程检测',
    group: '模型测试',
    target: '**********',
    status: '1', // 正常运行
    latest_run_time: '2025-07-24 20:14:15',
    desc: '监控Python进程运行状态',
    process_info: [
      {
        pid: '141',
        cmd: '/usr/bin/python3 /app/model_server.py',
        etime: '2-15:30:45',
        location: '容器:qwen3_32b'
      },
      {
        pid: '312',
        cmd: '/usr/bin/python3 /app/worker.py',
        etime: '1-08:22:15',
        location: '容器:qwen3_32b'
      },
      {
        pid: '445',
        cmd: '/usr/bin/python3 /app/scheduler.py',
        etime: '0-12:45:30',
        location: '容器:qwen3_32b'
      }
    ]
  },
  {
    id: '2_7',
    name: 'nginx服务监控',
    type: '智能进程检测',
    group: '基础服务',
    target: '**********',
    status: '0', // 未运行
    latest_run_time: '2025-07-24 19:30:22',
    desc: '监控Nginx服务状态',
    process_info: null
  },
  {
    id: '3_8',
    name: 'GPU模型推理服务',
    type: '智能进程检测',
    group: '模型服务',
    target: '192.2.29.10',
    status: '1', // 正常运行
    latest_run_time: '2025-07-24 20:10:33',
    desc: '监控GPU模型推理服务',
    process_info: [
      {
        pid: '1024',
        cmd: '/usr/bin/python3 /app/inference_server.py --gpu=0',
        etime: '5-10:15:20',
        location: '容器:inference_gpu0'
      },
      {
        pid: '1025',
        cmd: '/usr/bin/python3 /app/inference_server.py --gpu=1',
        etime: '5-10:15:18',
        location: '容器:inference_gpu1'
      }
    ]
  }
];

function CardItem(props) {
  const {status, type, group, desc, name, target, latest_run_time, process_info} = props.data
  
  // 构建详细信息悬浮提示
  const baseInfo = [
    <div key="group">分组: {group}</div>,
    <div key="type">类型: {type}</div>,
    <div key="name">名称: {name}</div>,
    <div key="target">目标: {target}</div>,
    <div key="status">状态: {status === '1' ? '正常' : '未运行'}</div>,
    <div key="update">更新: {latest_run_time || '---'}</div>,
    <div key="desc">描述: {desc}</div>
  ];
  
  // 如果是智能进程检测且有进程信息，添加进程详情
  if (type === '智能进程检测' && process_info && process_info.length > 0) {
    baseInfo.push(<div key="separator" style={{borderTop: '1px solid #ddd', margin: '8px 0'}}></div>);
    baseInfo.push(<div key="process-title" style={{fontWeight: 'bold'}}>进程详情:</div>);
    
    process_info.slice(0, 3).forEach((proc, index) => {
      baseInfo.push(
        <div key={`process-${index}`} style={{marginLeft: '8px', fontSize: '12px'}}>
          <div>进程 {index + 1}:</div>
          <div>  PID: {proc.pid}</div>
          <div>  命令: {proc.cmd}</div>
          <div>  运行时间: {proc.etime}</div>
          <div>  位置: {proc.location}</div>
        </div>
      );
    });
    
    if (process_info.length > 3) {
      baseInfo.push(<div key="more" style={{marginLeft: '8px', fontSize: '12px', color: '#999'}}>... 还有 {process_info.length - 3} 个进程</div>);
    }
  }
  
  const title = <div>{baseInfo}</div>;
  
  // 计算运行状态和时间显示
  const isRunning = status === '1' || status === '2'; // 正常或警告状态表示运行中
  const processCount = process_info ? process_info.length : 0;
  
  // 获取最长运行时间的进程
  let maxRuntime = '未运行';
  if (process_info && process_info.length > 0) {
    const runtimes = process_info.map(proc => proc.etime || '0').filter(time => time !== '0');
    if (runtimes.length > 0) {
      maxRuntime = runtimes[0]; // 取第一个进程的运行时间
    }
  }
  
  // 卡片样式
  const cardStyle = {
    background: isRunning ? '#f6ffed' : '#fff2f0',
    border: `2px solid ${isRunning ? '#52c41a' : '#ff4d4f'}`,
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    transition: 'all 0.3s ease',
    cursor: 'pointer',
  };
  
  return (
    <Tooltip title={title} placement="top">
      <div 
        className={styles.modernCard} 
        style={cardStyle}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = '0 4px 16px rgba(0,0,0,0.15)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        }}
      >
        {/* 上半部分：进程名称和状态 */}
        <div className={styles.cardHeader}>
          <div className={styles.processName} title={name}>
            {name.length > 12 ? `${name.substring(0, 12)}...` : name}
          </div>
          <div className={styles.statusIndicator}>
            <span 
              className={styles.statusDot} 
              style={{backgroundColor: isRunning ? '#52c41a' : '#ff4d4f'}}
            ></span>
            <span className={styles.statusText}>
              {isRunning ? '运行中' : '未运行'}
            </span>
          </div>
        </div>
        
        {/* 分隔线 */}
        <div className={styles.cardDivider}></div>
        
        {/* 下半部分：运行信息 */}
        <div className={styles.cardFooter}>
          {type === '智能进程检测' ? (
            <>
              <div className={styles.processCount}>
                <span className={styles.countNumber}>{processCount}</span>
                <span className={styles.countLabel}>个进程</span>
              </div>
              <div className={styles.runtime} title={`运行时间: ${maxRuntime}`}>
                <span className={styles.runtimeLabel}>运行时间:</span>
                <span className={styles.runtimeValue}>
                  {maxRuntime === '未运行' ? '未运行' : maxRuntime}
                </span>
              </div>
            </>
          ) : (
            <div className={styles.lastUpdate}>
              最后检查: {latest_run_time || '未运行'}
            </div>
          )}
        </div>
      </div>
    </Tooltip>
  )
}

function CardPreview() {
  return (
    <Card title="监控卡片预览" style={{margin: 24}}>
      <div className={styles.cardGrid}>
        {mockData.map(item => (
          <CardItem key={item.id} data={item}/>
        ))}
      </div>
    </Card>
  );
}

export default CardPreview;
