import { observable, action } from 'mobx';
import http from '../../libs/http';
import { message } from 'antd';

class Store {
  @observable metrics = {};
  @observable fileTree = [];
  @observable selectedFile = null;
  @observable loading = false;
  @observable testPlanStats = {
    overview: null,
    personnel: null,
    gpu_resources: null,
    risk_alerts: null
  };

  @action
  fetchMetrics = async () => {
    try {
      const res = await http.get('/api/model-storage/server-metrics/');
      
      // http拦截器已经提取了data字段，所以res直接就是服务器数据
      if (res && res.cpu_usage !== undefined) {
        const newMetrics = {
          cpu: res.cpu_usage || 0,
          memory: res.memory_usage || 0,
          disk: res.disk_usage || 0,
          networkUpload: res.network_upload || '0.00MB/s',
          networkDownload: res.network_download || '0.00MB/s',
          networkTotal: res.network_total || '0.00MB/s',
          timestamp: res.timestamp,
          targetHost: res.target_host
        };
        
        this.metrics = newMetrics;
      }
    } catch (error) {
      message.error('获取服务器指标失败：' + error.message);
    }
  };

  @action
  fetchFileTree = async () => {
    try {
      this.loading = true;
      const res = await http.get('/api/model-storage/lazy-load-tree/', {
        params: {
          path: '/HDD_Raid/SVN_MODEL_REPO',
          root: true
        }
      });
      this.fileTree = res.data?.children || [];
    } catch (error) {
      message.error('获取文件列表失败：' + error.message);
    } finally {
      this.loading = false;
    }
  };

  @action
  setSelectedFile = (file) => {
    this.selectedFile = file;
  };

  @action
  compareWithRemote = async (filePath) => {
    try {
      const res = await http.post('/api/model-storage/file-compare/', { filePath });
      return res;
    } catch (error) {
      message.error('文件对比失败：' + error.message);
      return null;
    }
  };

  @action
  syncFile = async (filePath) => {
    try {
      await http.post('/api/model-storage/sync-single-file/', { filePath });
      message.success('同步成功');
      this.fetchFileTree();
    } catch (error) {
      message.error('同步失败：' + error.message);
    }
  };

  @action
  fetchTestPlanStatistics = async () => {
    try {
      const res = await http.get('/api/model-storage/test-plan-statistics/');
      this.testPlanStats = {
        overview: res.overview || null,
        personnel: res.personnel || null,
        gpu_resources: res.gpu_resources || null,
        risk_alerts: res.risk_alerts || null
      };
    } catch (error) {
      // 设置默认数据避免显示错误
      this.testPlanStats = {
        overview: {
          total_plans: 0,
          total_tasks: 0,
          completion_rate: 0,
          avg_progress: 0
        },
        personnel: {
          total_testers: 0,
          tester_stats: []
        },
        gpu_resources: {
          total_devices: 0,
          vendor_distribution: [],
          popular_models: []
        },
        risk_alerts: {
          high_priority_tasks: 0,
          delayed_tasks: 0,
          blocked_tasks: 0,
          total_risk_tasks: 0
        }
      };
    }
  };
}

export default new Store();