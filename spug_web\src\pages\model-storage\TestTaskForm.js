import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, DatePicker, Select, InputNumber, Switch } from 'antd';
import moment from 'moment';
import http from 'libs/http';
import UserSelector from 'components/UserSelector';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

export default function TestTaskForm({ visible, initialValues, onCancel, onOk }) {
  const [form] = Form.useForm();
  const [gpuOptions, setGpuOptions] = useState([]);
  const [testCaseSetOptions, setTestCaseSetOptions] = useState([]);

  const handleOk = () => {
    form.validateFields()
      .then(values => {
        const data = { ...values };
        
        if (values.date_range && values.date_range.length === 2) {
          data.start_date = values.date_range[0].format('YYYY-MM-DD');
          data.end_date = values.date_range[1].format('YYYY-MM-DD');
        } else {
          data.start_date = null;
          data.end_date = null;
        }
        delete data.date_range;
        
        // 将status字段映射为test_status
        if (data.status) {
          data.test_status = data.status;
          delete data.status;
        }
        
        // 将优先级从显示值映射为后端值
        if (data.priority) {
          data.priority = priorityMap[data.priority] || data.priority;
        }
        
        onOk(data);
      })
      .catch(info => {
        // 表单验证失败
      });
  };

  const priorityMap = {
    'P1-高': 'p1',
    'P2-中': 'p2',
    'P3-低': 'p3'
  };

  const reversePriorityMap = {
    'p1': 'P1-高',
    'p2': 'P2-中',
    'p3': 'P3-低'
  };

  // 添加模型类型反向映射
  const reverseModelTypeMap = {
    '推理': 'inference',
    '训练': 'training',
    '微调': 'fine_tuning',
    '预训练': 'pre-training',
    '优化': 'optimization'
  };

  // GPU型号映射表将从GPU管理系统动态获取，不再使用硬编码
  const [gpuModelMap, setGpuModelMap] = useState({});

  // 获取GPU列表和测试用例集列表
  useEffect(() => {
    if (visible) {
      // 获取GPU列表
      http.get('/api/model-storage/gpus/')
        .then(res => {
          // 处理分页和非分页数据格式
          const gpus = res.results || res || [];
          const options = gpus.map(gpu => ({
            value: gpu.name,
            label: gpu.name
          }));
          setGpuOptions(options);

          // 构建GPU映射表（用于表单值映射）
          const mapping = {};
          gpus.forEach(gpu => {
            mapping[gpu.name] = gpu.name; // 实际值和显示值相同
          });
          setGpuModelMap(mapping);
        })
        .catch(err => {
          console.error('获取GPU列表失败:', err);
          // 如果获取失败，设置空选项
          setGpuOptions([]);
          setGpuModelMap({});
        });

      // 获取测试用例集列表
      http.get('/api/exec/test-case-sets/')
        .then(res => {
          const caseSetsData = res.data || res || [];
          const options = caseSetsData.map(caseSet => ({
            value: caseSet.id,
            label: `${caseSet.name}${caseSet.category ? ` (${caseSet.category})` : ''}`,
            description: caseSet.description
          }));
          setTestCaseSetOptions(options);
        })
        .catch(err => {
          setTestCaseSetOptions([]);
        });
    }
  }, [visible]);

  // 表单值设置
  useEffect(() => {
    if (initialValues && visible) {
      // 逐个字段映射
      const priorityMapped = reversePriorityMap[initialValues.priority] || initialValues.priority_display || 'P2-中';
      const statusMapped = initialValues.test_status;
      const dateRange = (initialValues.start_date && initialValues.end_date) ? [moment(initialValues.start_date), moment(initialValues.end_date)] : undefined;
      const modelTypeMapped = reverseModelTypeMap[initialValues.model_type_display] || initialValues.model_type;
      // 使用动态GPU映射表，如果没有映射则直接使用原值
      const gpuModelMapped = gpuModelMap[initialValues.gpu_model_display] || initialValues.gpu_model_display || initialValues.gpu_model;

      const mappedValues = {
        ...initialValues,
        priority: priorityMapped,
        status: statusMapped,
        date_range: dateRange,
        document_output: initialValues.document_output || false,
        model_type: modelTypeMapped,
        gpu_model: gpuModelMapped,
        progress: initialValues.progress || 0,
        test_case_set_id: initialValues.test_case_set_id
      };

      // 延迟设置表单值，确保表单已渲染
      setTimeout(() => {
        form.setFieldsValue(mappedValues);
        form.validateFields([]);
      }, 200);
    }
  }, [initialValues?.id, visible, form, gpuModelMap]);

  return (
    <Modal
      title={initialValues ? '编辑测试任务' : '新增测试任务'}
      visible={visible}
      onOk={handleOk}
      onCancel={onCancel}
      destroyOnClose
      maskClosable={false}
    >
      <Form 
        form={form} 
        initialValues={initialValues} 
        key={initialValues?.id || 'new'} 
        labelCol={{ span: 6 }} 
        wrapperCol={{ span: 16 }}
      >
        <Form.Item name="model_name" label="模型名称" rules={[{ required: true, message: '请输入模型名称' }]}>
          <Input placeholder="例如：Mixtral-8x7B" />
        </Form.Item>
        <Form.Item name="tester" label="人员名称" rules={[{ required: true, message: '请选择或输入人员名称' }]}>
          <UserSelector placeholder="请输入人员名称，支持模糊搜索" />
        </Form.Item>
        <Form.Item name="model_type" label="模型类型">
          <Select placeholder="请选择模型类型" allowClear>
            <Option value="inference">推理</Option>
            <Option value="training">训练</Option>
            <Option value="fine_tuning">微调</Option>
            <Option value="pre-training">预训练</Option>
            <Option value="optimization">优化</Option>
          </Select>
        </Form.Item>
        <Form.Item name="gpu_model" label="GPU型号">
          <Select placeholder="请选择GPU型号" allowClear>
            {gpuOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item name="test_case_set_id" label="关联用例集">
          <Select placeholder="请选择测试用例集" allowClear>
            {testCaseSetOptions.map(option => (
              <Option key={option.value} value={option.value} title={option.description}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item name="date_range" label="起止日期">
          <RangePicker />
        </Form.Item>
        <Form.Item name="priority" label="优先级">
          <Select placeholder="请选择优先级" allowClear>
            <Option value="P1-高">P1-高</Option>
            <Option value="P2-中">P2-中</Option>
            <Option value="P3-低">P3-低</Option>
          </Select>
        </Form.Item>
        <Form.Item name="progress" label="任务进度">
          <InputNumber min={0} max={100} formatter={value => `${value}%`} parser={value => value.replace('%', '')} style={{ width: '100%' }}/>
        </Form.Item>
        <Form.Item name="status" label="任务状态">
          <Select placeholder="请选择任务状态" allowClear>
            <Option value="pending">待开始</Option>
            <Option value="in_progress">进行中</Option>
            <Option value="completed">已完成</Option>
            <Option value="cancelled">已取消</Option>
            <Option value="blocked">阻塞中</Option>
            <Option value="delayed">已延期</Option>
          </Select>
        </Form.Item>
        <Form.Item name="document_output" label="资料输出" valuePropName="checked">
          <Switch
            checkedChildren="是"
            unCheckedChildren="否"
          />
        </Form.Item>
        <Form.Item name="notes" label="备注">
          <TextArea rows={3} placeholder="请输入备注信息" />
        </Form.Item>
      </Form>
    </Modal>
  );
}