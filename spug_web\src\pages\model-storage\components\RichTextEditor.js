import React, { useState, useRef } from 'react';
import { 
  Button, 
  Space, 
  Divider, 
  Upload, 
  message, 
  Modal,
  Input,
  Select,
  Row,
  Col
} from 'antd';
import { 
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  FontSizeOutlined,
  FontColorsOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  TableOutlined,
  PictureOutlined,
  LinkOutlined,
  UndoOutlined,
  RedoOutlined
} from '@ant-design/icons';
import styles from './RichTextEditor.module.less';

const { Option } = Select;

export default function RichTextEditor({ 
  value, 
  onChange, 
  placeholder = '请输入内容...', 
  height = 300 
}) {
  const [content, setContent] = useState(value || '');
  const [tableModalVisible, setTableModalVisible] = useState(false);
  const [linkModalVisible, setLinkModalVisible] = useState(false);
  const [tableRows, setTableRows] = useState(3);
  const [tableCols, setTableCols] = useState(3);
  const [linkText, setLinkText] = useState('');
  const [linkUrl, setLinkUrl] = useState('');
  const editorRef = useRef(null);

  const handleContentChange = () => {
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      setContent(newContent);
      if (onChange) {
        onChange(newContent);
      }
    }
  };

  const execCommand = (command, value = null) => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      editorRef.current.focus();
    }
  };

  const handleFontSize = (size) => {
    execCommand('fontSize', size);
  };

  const handleFontColor = (color) => {
    execCommand('foreColor', color);
  };

  const handleImageUpload = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const img = `<img src="${e.target.result}" style="max-width: 100%; height: auto; margin: 10px 0;" />`;
      execCommand('insertHTML', img);
      handleContentChange();
    };
    reader.readAsDataURL(file);
    return false; // 阻止默认上传行为
  };

  // 处理粘贴事件
  const handlePaste = (e) => {
    const items = e.clipboardData.items;

    for (let item of items) {
      if (item.type.indexOf('image') !== -1) {
        e.preventDefault();
        const file = item.getAsFile();
        handleImageUpload(file);
        message.success('图片粘贴成功');
        break;
      }
    }
  };

  // 处理拖拽
  const handleDrop = (e) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length > 0) {
      imageFiles.forEach(file => handleImageUpload(file));
      message.success(`成功添加 ${imageFiles.length} 张图片`);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleInsertTable = () => {
    let tableHTML = '<table border="1" style="border-collapse: collapse; width: 100%;">';
    for (let i = 0; i < tableRows; i++) {
      tableHTML += '<tr>';
      for (let j = 0; j < tableCols; j++) {
        tableHTML += '<td style="padding: 8px; border: 1px solid #ddd;">&nbsp;</td>';
      }
      tableHTML += '</tr>';
    }
    tableHTML += '</table><br>';
    
    execCommand('insertHTML', tableHTML);
    setTableModalVisible(false);
    setTableRows(3);
    setTableCols(3);
  };

  const handleInsertLink = () => {
    if (linkText && linkUrl) {
      const linkHTML = `<a href="${linkUrl}" target="_blank">${linkText}</a>`;
      execCommand('insertHTML', linkHTML);
      setLinkModalVisible(false);
      setLinkText('');
      setLinkUrl('');
    }
  };

  const fontSizes = [
    { label: '小', value: '1' },
    { label: '正常', value: '3' },
    { label: '大', value: '5' },
    { label: '特大', value: '7' }
  ];

  const colors = [
    '#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', 
    '#FF00FF', '#00FFFF', '#FFA500', '#800080', '#008000'
  ];

  return (
    <div className={styles.richTextEditor}>
      {/* 工具栏 */}
      <div className={styles.toolbar}>
        <Space split={<Divider type="vertical" />}>
          {/* 撤销重做 */}
          <Space size="small">
            <Button 
              size="small" 
              icon={<UndoOutlined />} 
              onClick={() => execCommand('undo')}
              title="撤销"
            />
            <Button 
              size="small" 
              icon={<RedoOutlined />} 
              onClick={() => execCommand('redo')}
              title="重做"
            />
          </Space>

          {/* 字体样式 */}
          <Space size="small">
            <Button 
              size="small" 
              icon={<BoldOutlined />} 
              onClick={() => execCommand('bold')}
              title="粗体"
            />
            <Button 
              size="small" 
              icon={<ItalicOutlined />} 
              onClick={() => execCommand('italic')}
              title="斜体"
            />
            <Button 
              size="small" 
              icon={<UnderlineOutlined />} 
              onClick={() => execCommand('underline')}
              title="下划线"
            />
          </Space>

          {/* 字体大小和颜色 */}
          <Space size="small">
            <Select
              size="small"
              defaultValue="3"
              style={{ width: 80 }}
              onChange={handleFontSize}
              suffixIcon={<FontSizeOutlined />}
            >
              {fontSizes.map(size => (
                <Option key={size.value} value={size.value}>
                  {size.label}
                </Option>
              ))}
            </Select>
            
            <div className={styles.colorPicker}>
              <Button 
                size="small" 
                icon={<FontColorsOutlined />}
                title="字体颜色"
              />
              <div className={styles.colorPanel}>
                {colors.map(color => (
                  <div
                    key={color}
                    className={styles.colorItem}
                    style={{ backgroundColor: color }}
                    onClick={() => handleFontColor(color)}
                  />
                ))}
              </div>
            </div>
          </Space>

          {/* 对齐方式 */}
          <Space size="small">
            <Button 
              size="small" 
              icon={<AlignLeftOutlined />} 
              onClick={() => execCommand('justifyLeft')}
              title="左对齐"
            />
            <Button 
              size="small" 
              icon={<AlignCenterOutlined />} 
              onClick={() => execCommand('justifyCenter')}
              title="居中对齐"
            />
            <Button 
              size="small" 
              icon={<AlignRightOutlined />} 
              onClick={() => execCommand('justifyRight')}
              title="右对齐"
            />
          </Space>

          {/* 列表 */}
          <Space size="small">
            <Button 
              size="small" 
              icon={<OrderedListOutlined />} 
              onClick={() => execCommand('insertOrderedList')}
              title="有序列表"
            />
            <Button 
              size="small" 
              icon={<UnorderedListOutlined />} 
              onClick={() => execCommand('insertUnorderedList')}
              title="无序列表"
            />
          </Space>

          {/* 插入功能 */}
          <Space size="small">
            <Button 
              size="small" 
              icon={<TableOutlined />} 
              onClick={() => setTableModalVisible(true)}
              title="插入表格"
            />
            <Upload
              accept="image/*"
              showUploadList={false}
              beforeUpload={handleImageUpload}
            >
              <Button 
                size="small" 
                icon={<PictureOutlined />}
                title="插入图片"
              />
            </Upload>
            <Button 
              size="small" 
              icon={<LinkOutlined />} 
              onClick={() => setLinkModalVisible(true)}
              title="插入链接"
            />
          </Space>
        </Space>
      </div>

      {/* 编辑区域 */}
      <div
        ref={editorRef}
        className={styles.editor}
        contentEditable
        style={{ height }}
        onInput={handleContentChange}
        onBlur={handleContentChange}
        onKeyUp={handleContentChange}
        onPaste={handlePaste}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        dangerouslySetInnerHTML={{ __html: content }}
        data-placeholder={placeholder}
        suppressContentEditableWarning={true}
      />

      {/* 提示信息 */}
      <div style={{
        fontSize: '12px',
        color: '#999',
        marginTop: '8px',
        padding: '4px 8px',
        background: '#f9f9f9',
        borderRadius: '4px'
      }}>
        💡 支持直接粘贴图片 (Ctrl+V) 或拖拽图片到编辑区域
      </div>

      {/* 插入表格模态框 */}
      <Modal
        title="插入表格"
        open={tableModalVisible}
        onOk={handleInsertTable}
        onCancel={() => setTableModalVisible(false)}
        okText="插入"
        cancelText="取消"
      >
        <Row gutter={16}>
          <Col span={12}>
            <div>行数：</div>
            <Select
              value={tableRows}
              onChange={setTableRows}
              style={{ width: '100%' }}
            >
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                <Option key={num} value={num}>{num}</Option>
              ))}
            </Select>
          </Col>
          <Col span={12}>
            <div>列数：</div>
            <Select
              value={tableCols}
              onChange={setTableCols}
              style={{ width: '100%' }}
            >
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                <Option key={num} value={num}>{num}</Option>
              ))}
            </Select>
          </Col>
        </Row>
      </Modal>

      {/* 插入链接模态框 */}
      <Modal
        title="插入链接"
        open={linkModalVisible}
        onOk={handleInsertLink}
        onCancel={() => setLinkModalVisible(false)}
        okText="插入"
        cancelText="取消"
      >
        <div style={{ marginBottom: 16 }}>
          <div>链接文本：</div>
          <Input
            value={linkText}
            onChange={(e) => setLinkText(e.target.value)}
            placeholder="请输入链接显示文本"
          />
        </div>
        <div>
          <div>链接地址：</div>
          <Input
            value={linkUrl}
            onChange={(e) => setLinkUrl(e.target.value)}
            placeholder="请输入链接地址"
          />
        </div>
      </Modal>
    </div>
  );
}
