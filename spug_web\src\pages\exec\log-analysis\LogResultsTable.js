/**
 * 日志分析结果表格组件
 * 专门用于独立的日志分析页面
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  message,
  Tooltip,
  Progress,
  Statistic,
  Row,
  Col,
  Alert,
  Modal,
  Spin,
  Divider,
  Badge
} from 'antd';
import {
  BarChartOutlined,
  DownloadOutlined,
  EyeOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  ThunderboltOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  BulbOutlined
} from '@ant-design/icons';
import styles from './LogResultsTable.module.less';

function LogResultsTable({
  results,
  config
}) {
  const [loading, setLoading] = useState(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);

  // 处理结果数据，提取统计信息
  const processResults = (results) => {
    if (!results || results.length === 0) return { tableData: [], stats: {} };

    const tableData = results.map((result, index) => ({
      key: result.id || index,
      fileName: result.name || `文件${index + 1}`,
      filePath: result.file_path || result.path || '-',
      fileSize: result.file_size || '-',
      responseTime: result.avg_response_time || result.response_time || '-',
      throughput: result.throughput || result.tps || '-',
      errorRate: result.error_rate || '0%',
      p95: result.p95_response_time || result.p95 || '-',
      p99: result.p99_response_time || result.p99 || '-',
      totalRequests: result.total_requests || result.request_count || '-',
      successRequests: result.success_requests || '-',
      failedRequests: result.failed_requests || '-',
      analysisTime: result.analysis_time || new Date().toLocaleString(),
      details: result.details || result.raw_data || {},
      // AI分析所需字段
      host_id: result.host_id,
      container_name: result.container_name,
      file_path: result.file_path || result.path
    }));

    // 计算统计信息
    const stats = {
      totalFiles: results.length,
      avgResponseTime: calculateAverage(results, 'avg_response_time'),
      totalRequests: results.reduce((sum, r) => sum + (parseInt(r.total_requests) || 0), 0),
      avgThroughput: calculateAverage(results, 'throughput')
    };

    return { tableData, stats };
  };

  const calculateAverage = (data, field) => {
    const values = data.map(item => parseFloat(item[field]) || 0).filter(v => v > 0);
    return values.length > 0 ? (values.reduce((a, b) => a + b, 0) / values.length).toFixed(2) : 0;
  };

  const { tableData, stats } = processResults(results);

  // 表格列定义
  const columns = [
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
      width: 200,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.filePath}
          </div>
        </div>
      )
    },
    {
      title: '平均响应时间',
      dataIndex: 'responseTime',
      key: 'responseTime',
      width: 120,
      render: (value) => (
        <Tag color={value && parseFloat(value) > 1000 ? 'red' : 'green'}>
          {value ? `${value}ms` : '-'}
        </Tag>
      )
    },
    {
      title: '吞吐量',
      dataIndex: 'throughput',
      key: 'throughput',
      width: 100,
      render: (value) => (
        <span style={{ color: '#1890ff', fontWeight: 500 }}>
          {value ? `${value} req/s` : '-'}
        </span>
      )
    },
    {
      title: 'P95响应时间',
      dataIndex: 'p95',
      key: 'p95',
      width: 120,
      render: (value) => (
        <Tag color="orange">
          {value ? `${value}ms` : '-'}
        </Tag>
      )
    },
    {
      title: 'P99响应时间',
      dataIndex: 'p99',
      key: 'p99',
      width: 120,
      render: (value) => (
        <Tag color="red">
          {value ? `${value}ms` : '-'}
        </Tag>
      )
    },
    {
      title: '总请求数',
      dataIndex: 'totalRequests',
      key: 'totalRequests',
      width: 100,
      render: (value) => (
        <span style={{ fontFamily: 'monospace' }}>
          {value || '-'}
        </span>
      )
    },
    {
      title: '错误率',
      dataIndex: 'errorRate',
      key: 'errorRate',
      width: 80,
      render: (value) => {
        const rate = parseFloat(value) || 0;
        return (
          <Tag color={rate > 5 ? 'red' : rate > 1 ? 'orange' : 'green'}>
            {value || '0%'}
          </Tag>
        );
      }
    },
    {
      title: '分析时间',
      dataIndex: 'analysisTime',
      key: 'analysisTime',
      width: 150,
      render: (value) => (
        <span style={{ fontSize: '12px', color: '#666' }}>
          {value}
        </span>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 查看详情
  const handleViewDetails = (record) => {
    message.info('详情功能开发中...');
  };

  // 导出数据
  const handleExport = () => {
    try {
      const exportData = {
        summary: stats,
        details: tableData,
        config: config,
        exportTime: new Date().toISOString()
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `log_analysis_${new Date().getTime()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败: ' + error.message);
    }
  };

  if (!results || results.length === 0) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
          <FileTextOutlined style={{ fontSize: 48, marginBottom: 16 }} />
          <p>暂无分析结果</p>
        </div>
      </Card>
    );
  }

  return (
    <div className={styles.logResultsTable}>
      {/* 统计概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="分析文件数"
              value={stats.totalFiles}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均响应时间"
              value={stats.avgResponseTime}
              suffix="ms"
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总请求数"
              value={stats.totalRequests}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均吞吐量"
              value={stats.avgThroughput}
              suffix="req/s"
              prefix={<ThunderboltOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 结果表格 */}
      <Card
        title={
          <Space>
            <BarChartOutlined />
            <span>日志分析结果</span>
            <Tag color="blue">{tableData.length} 个文件</Tag>
          </Space>
        }
        extra={
          <Button
            icon={<DownloadOutlined />}
            onClick={handleExport}
          >
            导出结果
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={tableData}
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          scroll={{ x: 1200 }}
          size="middle"
        />
      </Card>
    </div>
  );
}

export default LogResultsTable;
