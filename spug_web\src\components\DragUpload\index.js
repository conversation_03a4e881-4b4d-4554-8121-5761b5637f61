/**
 * DragUpload - 拖拽上传组件
 */
import React, { useState, useRef } from 'react';
import { Upload, message } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import styles from './index.module.less';

const { Dragger } = Upload;

function DragUpload({ 
  onUpload,
  multiple = true,
  accept,
  maxSize = 0, // MB，0表示无限制
  disabled = false,
  children,
  style,
  className,
  ...props 
}) {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const uploadRef = useRef();

  // 处理拖拽事件
  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // 只有当离开整个拖拽区域时才设置为false
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragging(false);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (disabled || isUploading) return;

    const { files } = e.dataTransfer;
    if (files && files.length > 0) {
      handleFiles(Array.from(files));
    }
  };

  // 处理文件
  const handleFiles = async (fileList) => {
    if (!multiple && fileList.length > 1) {
      message.warning('只能上传一个文件');
      return;
    }

    // 文件大小检查（仅当maxSize > 0时才检查）
    if (maxSize > 0) {
      const oversizeFiles = fileList.filter(file => file.size > maxSize * 1024 * 1024);
      if (oversizeFiles.length > 0) {
        message.error(`文件大小不能超过 ${maxSize}MB`);
        return;
      }
    }

    // 文件类型检查
    if (accept) {
      const acceptTypes = accept.split(',').map(type => type.trim());
      const invalidFiles = fileList.filter(file => {
        const fileExt = `.${file.name.split('.').pop().toLowerCase()}`;
        return !acceptTypes.some(acceptType => {
          if (acceptType.startsWith('.')) {
            return acceptType === fileExt;
          }
          return file.type.startsWith(acceptType.replace('*', ''));
        });
      });
      
      if (invalidFiles.length > 0) {
        message.error(`不支持的文件类型: ${invalidFiles.map(f => f.name).join(', ')}`);
        return;
      }
    }

    setIsUploading(true);
    try {
      if (onUpload) {
        await onUpload(fileList);
      }
    } catch (error) {
      console.error('Upload error:', error);
    } finally {
      setIsUploading(false);
    }
  };

  // Antd Upload组件的上传处理
  const handleUploadChange = ({ fileList }) => {
    const files = fileList.map(item => item.originFileObj).filter(Boolean);
    if (files.length > 0) {
      handleFiles(files);
    }
  };

  const uploadProps = {
    name: 'file',
    multiple,
    accept,
    showUploadList: false,
    beforeUpload: () => false, // 阻止自动上传
    onChange: handleUploadChange,
    disabled: disabled || isUploading,
    ...props
  };

  // 如果有自定义children，使用自定义UI
  if (children) {
    return (
      <div
        className={`${styles.dragUpload} ${isDragging ? styles.dragging : ''} ${className || ''}`}
        style={style}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <Upload {...uploadProps}>
          {children}
        </Upload>
        {isDragging && (
          <div className={styles.dragOverlay}>
            <div className={styles.dragText}>
              <InboxOutlined />
              <p>释放文件开始上传</p>
            </div>
          </div>
        )}
      </div>
    );
  }

  // 默认UI - 使用Antd的Dragger
  return (
    <div
      className={`${styles.dragUpload} ${isDragging ? styles.dragging : ''} ${className || ''}`}
      style={style}
    >
      <Dragger
        {...uploadProps}
        className={`${styles.dragger} ${isUploading ? styles.uploading : ''}`}
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">
          {isUploading ? '正在上传...' : '点击或拖拽文件到这里上传'}
        </p>
        <p className="ant-upload-hint">
          {multiple ? '支持单个或批量上传' : '支持单个文件上传'}
          {maxSize > 0 && ` (最大${maxSize}MB)`}
        </p>
      </Dragger>
    </div>
  );
}

export default DragUpload; 