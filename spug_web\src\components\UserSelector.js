import React, { useState, useEffect, useCallback } from 'react';
import { AutoComplete, Input } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { http } from 'libs';
import { debounce } from 'lodash';

/**
 * 用户选择器组件
 * 支持模糊搜索用户姓名和用户名
 */
export default function UserSelector({ 
  value, 
  onChange, 
  placeholder = "请输入人员名称", 
  style,
  disabled = false,
  allowClear = true 
}) {
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);

  // 防抖搜索函数
  const debouncedSearch = useCallback(
    debounce(async (searchText) => {
      if (!searchText || searchText.trim().length === 0) {
        setOptions([]);
        return;
      }

      setLoading(true);
      try {
        const response = await http.get('/api/model-storage/users/search/', {
          params: { search: searchText.trim() }
        });
        
        const users = response.data || [];
        const userOptions = users.map(user => ({
          value: user.nickname,
          label: (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <UserOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              <span style={{ fontWeight: 500 }}>{user.nickname}</span>
              {user.username !== user.nickname && (
                <span style={{ marginLeft: 8, color: '#999', fontSize: '12px' }}>
                  ({user.username})
                </span>
              )}
            </div>
          ),
          key: user.id
        }));
        
        setOptions(userOptions);
      } catch (error) {
        console.error('搜索用户失败:', error);
        setOptions([]);
      } finally {
        setLoading(false);
      }
    }, 300),
    []
  );

  // 处理搜索
  const handleSearch = (searchText) => {
    debouncedSearch(searchText);
  };

  // 处理选择
  const handleSelect = (selectedValue, option) => {
    onChange && onChange(selectedValue);
    // 选择后清空选项列表
    setOptions([]);
  };

  // 处理输入变化
  const handleChange = (inputValue) => {
    onChange && onChange(inputValue);
    // 如果输入为空，清空选项
    if (!inputValue) {
      setOptions([]);
    }
  };

  return (
    <AutoComplete
      value={value}
      options={options}
      onSearch={handleSearch}
      onSelect={handleSelect}
      onChange={handleChange}
      placeholder={placeholder}
      style={style}
      disabled={disabled}
      allowClear={allowClear}
      notFoundContent={loading ? '搜索中...' : '暂无匹配用户'}
      filterOption={false} // 禁用本地过滤，使用服务端搜索
    >
      <Input 
        prefix={<UserOutlined style={{ color: '#bfbfbf' }} />}
        placeholder={placeholder}
      />
    </AutoComplete>
  );
}
