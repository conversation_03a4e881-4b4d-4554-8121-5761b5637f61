# Generated by Django 2.2.28 on 2025-07-02 16:52

from django.db import migrations, models
import django.db.models.deletion
import libs.mixins
import libs.utils


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('repository', '0001_initial'),
        ('app', '0001_initial'),
        ('account', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DeployRequest',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('type', models.CharField(choices=[('1', '正常发布'), ('2', '回滚'), ('3', '自动发布')], default='1', max_length=2)),
                ('extra', models.TextField()),
                ('host_ids', models.TextField()),
                ('desc', models.Char<PERSON><PERSON>(max_length=255, null=True)),
                ('status', models.Char<PERSON>ield(choices=[('-3', '发布异常'), ('-1', '已驳回'), ('0', '待审核'), ('1', '待发布'), ('2', '发布中'), ('3', '发布成功')], max_length=2)),
                ('reason', models.CharField(max_length=255, null=True)),
                ('version', models.CharField(max_length=100, null=True)),
                ('spug_version', models.CharField(max_length=50, null=True)),
                ('plan', models.DateTimeField(null=True)),
                ('fail_host_ids', models.TextField(default='[]')),
                ('created_at', models.CharField(default=libs.utils.human_datetime, max_length=20)),
                ('approve_at', models.CharField(max_length=20, null=True)),
                ('do_at', models.CharField(max_length=20, null=True)),
                ('approve_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='account.User')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='+', to='account.User')),
                ('deploy', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.Deploy')),
                ('do_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='account.User')),
                ('repository', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='repository.Repository')),
            ],
            options={
                'db_table': 'deploy_requests',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
