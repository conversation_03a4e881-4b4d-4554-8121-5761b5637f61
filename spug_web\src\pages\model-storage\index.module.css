/* Styles for ReleasePlan and ReleasePlanGantt */
.ganttContainer {
  width: 100%;
  height: 500px;
}

/* 主容器样式 */
.container {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 统计卡片样式 */
.statisticsContainer {
  margin-top: 24px;
}

.overviewCard {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.overviewCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.overviewCard:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 16px 48px rgba(102, 126, 234, 0.4);
}

.overviewCard:hover::before {
  opacity: 1;
}

.overviewCard .ant-card-head {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.overviewCard .ant-card-head-title {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.personnelCard {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.3);
  border: none;
  background: linear-gradient(135deg, #4caf50 0%, #81c784 100%);
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.personnelCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.personnelCard:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 16px 48px rgba(76, 175, 80, 0.4);
}

.personnelCard:hover::before {
  opacity: 1;
}

.personnelCard .ant-card-head {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.personnelCard .ant-card-head-title {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.gpuCard {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(255, 152, 0, 0.3);
  border: none;
  background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.gpuCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gpuCard:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 16px 48px rgba(255, 152, 0, 0.4);
}

.gpuCard:hover::before {
  opacity: 1;
}

.gpuCard .ant-card-head {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.gpuCard .ant-card-head-title {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.riskCard {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(244, 67, 54, 0.3);
  border: none;
  background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.riskCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.riskCard:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 16px 48px rgba(244, 67, 54, 0.4);
}

.riskCard:hover::before {
  opacity: 1;
}

.riskCard .ant-card-head {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.riskCard .ant-card-head-title {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.personnelCard {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  color: white;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.personnelCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.personnelCard .ant-card-head {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.personnelCard .ant-card-head-title {
  color: white;
}

.gpuCard {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #333;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.gpuCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.gpuCard .ant-card-head {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.riskCard {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #333;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.riskCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.riskCard .ant-card-head {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 服务器监控紧凑样式 */
.compactServerCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}