# Generated by Django 2.2.28 on 2025-07-24 19:37

from django.db import migrations, models
import django.db.models.deletion
import libs.mixins
import libs.utils


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0001_initial'),
        ('exec', '0010_transfer_template'),
    ]

    operations = [
        migrations.AlterField(
            model_name='transfertemplate',
            name='created_at',
            field=models.CharField(default=libs.utils.human_datetime, max_length=20),
        ),
        migrations.AlterField(
            model_name='transfertemplate',
            name='updated_at',
            field=models.CharField(default=libs.utils.human_datetime, max_length=20),
        ),
        migrations.AlterField(
            model_name='transfertemplate',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='account.User'),
        ),
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('host_id', models.IntegerField(null=True)),
                ('command', models.TextField()),
                ('is_public', models.BooleanField(default=False)),
                ('created_at', models.CharField(default=libs.utils.human_datetime, max_length=20)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='account.User')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exec_task', to='account.User')),
            ],
            options={
                'db_table': 'exec_tasks',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
