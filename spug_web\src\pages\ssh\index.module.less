.container {
  display: flex;
  min-height: 100vh;
  width: 100vw;

  .sider {
    display: flex;
    flex-direction: column;
    width: 240px;
    min-width: 240px;
    background-color: #fafafa;
    position: relative;
    flex-shrink: 0;

    .split {
      position: absolute;
      width: 8px;
      height: 100vh;
      right: -4px;
      cursor: ew-resize;
      z-index: 1000;
    }

    .logo {
      height: 42px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #2563fc;
      flex-shrink: 0;

      img {
        height: 28px;
      }
    }

    .hosts {
      flex: 1;
      box-shadow: 2px 2px 2px #e0e0e0;
      display: flex;
      flex-direction: column;

      :global(.ant-tree-node-content-wrapper) {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      :global(.ant-tree) {
        background-color: #fafafa;
        flex: 1;
        overflow: auto;
      }

      .search {
        margin: 12px 10px;
        width: calc(100% - 60px);
        flex-shrink: 0;
      }
    }
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #eeeeee;
    min-width: 0;

    .tips {
      position: absolute;
      top: 12px;
      left: 12px;
      font-size: 12px;
      color: #666;
    }

    .fig {
      flex: 1;
      background-color: #2b2b2b;
      color: #A9B7C6;
      margin: 12px;
      padding: 900px 20px;
      text-align: center;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      line-height: 1.4;
      font-family: monospace;
      white-space: pre;
      overflow: auto;
    }

    .fig2 {
      flex: 1;
      background-color: #fff;
      color: #2b2b2b;
      margin: 12px;
      padding: 40px 20px;
      text-align: center;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      line-height: 1.4;
      font-family: monospace;
      white-space: pre;
      overflow: auto;
    }

    .tabRender {
      user-select: none;
      padding: 8px 8px 8px 16px;
      margin: 0 -8px 0 -16px;
      color: #2563fc;
    }

    .fileManger {
      margin: 12px;
      padding: 12px;
      border-radius: 6px;
      background: #fff;
      height: calc(100vh - 66px);
    }

    .setting {
      cursor: pointer;
      padding-right: 6px;
      margin-right: 6px;
      color: #fa8c16;
    }

    :global(.ant-tabs) {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    :global(.ant-tabs-content-holder) {
      flex: 1;
      overflow: hidden;
    }

    :global(.ant-tabs-content) {
      height: 100%;
      background: #eeeeee;
    }

    :global(.ant-tabs-tabpane) {
      height: 100%;
    }

    :global(.ant-tabs-nav) {
      height: 42px;
      margin: 0;
      padding-left: 12px;
      flex-shrink: 0;
    }

    :global(.ant-tabs-nav:before) {
      border: none;
    }

    :global(.ant-tabs-tab) {
      border: none;
      background: #fff;
    }

    :global(.ant-tabs-tab-active) {
      border-bottom: 2px solid #2563fc !important;
      transition: unset;
    }
  }

  // 右侧快捷命令面板
  .rightPanel {
    width: 220px;
    min-width: 220px;
    background-color: #fafafa;
    border-left: 1px solid #e0e0e0;
    height: 100vh;
    overflow: hidden;
    padding: 6px;
    flex-shrink: 0;
  }
}

.terminal {
  height: 100%;
  margin: 12px;

  :global(.xterm) {
    padding: 10px 0 6px 10px;
    height: calc(100vh - 90px);
  }

  :global(.xterm-viewport) {
    border-radius: 6px;
  }
}

.fileSize {
  padding-right: 24px !important;
}

.drawerContainer {
  :global(.ant-drawer-body) {
    padding: 10px 16px;
  }
}

.drawerHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  height: 24px;

  .action {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 24px;
  }

  .bread:hover {
    .edit {
      display: inline-block;
    }
  }

  .input {
    width: 60%;
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  }

  .edit {
    display: none;
    color: #2563fcbb;
    margin-left: 24px;
    cursor: pointer;
  }

  .progress {
    width: 94px;
    margin-left: 12px;
  }

  :global(.ant-breadcrumb) {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 24px;
  }

  :global(.ant-breadcrumb-separator) {
    margin: 0 4px;
    color: rgba(0, 0, 0, 0.85);
  }

  :global(.ant-breadcrumb-link) {
    color: rgba(0, 0, 0, 0.85);
  }
}

.drawerBtn {
  height: 22px;
  width: 22px;
}