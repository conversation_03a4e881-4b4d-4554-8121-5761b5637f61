/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React, { useState, useEffect } from 'react';
import { Avatar, Card, Col, Row, Modal } from 'antd';
import { LeftSquareOutlined, RightSquareOutlined, EditOutlined, PlusOutlined, CloseOutlined } from '@ant-design/icons';
import { AuthButton } from 'components';
import NavForm from './NavForm';
import { http } from 'libs';
import history from 'libs/history';
import styles from './index.module.less';

function NavIndex(props) {
  const [isEdit, setIsEdit] = useState(false);
  const [records, setRecords] = useState([]);
  const [record, setRecord] = useState();

  useEffect(() => {
    fetchRecords()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  function fetchRecords() {
    http.get('/api/home/<USER>/')
      .then(res => setRecords(res))
  }

  function handleSubmit() {
    fetchRecords();
    setRecord(null)
  }

  function handleSort(info, sort) {
    http.patch('/api/home/<USER>/', {id: info.id, sort})
      .then(() => fetchRecords())
  }

  function handleDelete(item) {
    Modal.confirm({
      title: '操作确认',
      content: `确定要删除【${item.title}】？`,
      onOk: () => http.delete('/api/home/<USER>/', {params: {id: item.id}})
        .then(fetchRecords)
    })
  }

  return (
    <Card
      title="便捷导航"
      className={styles.nav}
      bodyStyle={{paddingBottom: 0, minHeight: 166}}
      extra={<AuthButton auth="admin" type="link"
                         onClick={() => setIsEdit(!isEdit)}>{isEdit ? '完成' : '编辑'}</AuthButton>}>
      {isEdit ? (
        <Row gutter={24}>
          <Col span={6} style={{marginBottom: 24}}>
            <div
              className={styles.add}
              onClick={() => setRecord({links: [{}]})}>
              <PlusOutlined/>
              <span>新建</span>
            </div>
          </Col>
          {records.map(item => (
            <Col key={item.id} span={6} style={{marginBottom: 24}}>
              <Card hoverable actions={[
                <LeftSquareOutlined onClick={() => handleSort(item, 'up')}/>,
                <RightSquareOutlined onClick={() => handleSort(item, 'down')}/>,
                <EditOutlined onClick={() => setRecord(item)}/>
              ]}>
                <Card.Meta
                  avatar={<Avatar src={item.logo}/>}
                  title={item.title}
                  description={item.desc}/>
                <CloseOutlined className={styles.icon} onClick={() => handleDelete(item)}/>
              </Card>
            </Col>
          ))}
        </Row>
      ) : (
        <Row gutter={24}>
          {records.map(item => {
            // 判断是否为站内链接
            const isInternalLink = (url) => {
              // 以 / 开头的绝对路径是站内链接
              if (url.startsWith('/')) {
                return true;
              }
              // 包含协议的是外部链接
              if (url.startsWith('http://') || url.startsWith('https://')) {
                return false;
              }
              // 包含域名的是外部链接（如 www.example.com, example.com）
              if (url.includes('.') && !url.startsWith('./') && !url.startsWith('../')) {
                return false;
              }
              // 其他情况视为站内链接
              return true;
            };

            // 处理卡片点击事件
            const handleCardClick = () => {
              if (item.links && item.links.length > 0) {
                const firstLink = item.links[0];
                const url = firstLink.url;

                if (isInternalLink(url)) {
                  // 站内链接，使用 history.push 进行路由跳转
                  history.push(url);
                } else {
                  // 外部链接，在新窗口打开
                  const fullUrl = url.startsWith('http://') || url.startsWith('https://')
                    ? url
                    : `http://${url}`;
                  window.open(fullUrl, '_blank', 'noopener,noreferrer');
                }
              }
            };

            return (
              <Col key={item.id} span={6} style={{marginBottom: 24}}>
                <Card
                  hoverable
                  onClick={handleCardClick}
                  style={{ cursor: 'pointer' }}
                  actions={item.links.map(x => {
                    const handleLinkClick = (e) => {
                      e.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击

                      if (isInternalLink(x.url)) {
                        // 站内链接，使用 history.push 进行路由跳转
                        history.push(x.url);
                      } else {
                        // 外部链接，在新窗口打开
                        const url = x.url.startsWith('http://') || x.url.startsWith('https://')
                          ? x.url
                          : `http://${x.url}`;
                        window.open(url, '_blank', 'noopener,noreferrer');
                      }
                    };

                    return (
                      <a
                        key={x.name}
                        href="#"
                        onClick={handleLinkClick}
                        style={{ textDecoration: 'none' }}
                      >
                        {x.name}
                      </a>
                    );
                  })}>
                  <Card.Meta
                    avatar={<Avatar size="large" src={item.logo}/>}
                    title={item.title}
                    description={item.desc}/>
                </Card>
              </Col>
            );
          })}
        </Row>
      )}
      {record ? <NavForm record={record} onCancel={() => setRecord(null)} onOk={handleSubmit}/> : null}
    </Card>
  )
}

export default NavIndex