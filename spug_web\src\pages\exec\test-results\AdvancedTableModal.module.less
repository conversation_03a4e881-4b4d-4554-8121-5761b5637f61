.container {
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.toolbar {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
  
  .info {
    color: #666;
    font-size: 14px;
  }
}

.tableContainer {
  flex: 1;
  overflow: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  
  .th {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    border-right: 1px solid #f0f0f0;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    color: #262626;
    position: sticky;
    top: 0;
    z-index: 10;
    
    &:last-child {
      border-right: none;
    }
  }
  
  .td {
    border-bottom: 1px solid #f0f0f0;
    border-right: 1px solid #f0f0f0;
    padding: 12px 8px;
    color: #595959;
    
    &:last-child {
      border-right: none;
    }
  }
  
  .tr {
    &:hover {
      background-color: #f5f5f5;
    }
    
    &:last-child .td {
      border-bottom: none;
    }
  }
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: default;
  
  &.sortable {
    cursor: pointer;
    user-select: none;
    
    &:hover {
      color: #1890ff;
    }
  }
}

.sortIcon {
  margin-left: 4px;
  font-size: 12px;
  color: #bfbfbf;
  
  .sortable:hover & {
    color: #1890ff;
  }
}

.cellContent {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    height: 60vh;
  }
  
  .table {
    font-size: 12px;
    
    .th, .td {
      padding: 8px 4px;
    }
  }
  
  .cellContent {
    max-width: 120px;
  }
}
