/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import {
  ThunderboltOutlined,
  QuestionCircleOutlined,
  UploadOutlined,
  CloudServerOutlined,
  BulbOutlined,
  FolderOpenOutlined,
  InboxOutlined,
  DockerOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { Form, Button, Tooltip, Space, Card, Table, Input, Upload, message, Switch, Select, AutoComplete, Modal, List, Popconfirm, Checkbox } from 'antd';
import { AuthDiv, Breadcrumb, DragUpload } from 'components';
import HostSelector from 'pages/host/Selector';
import FileSelector from 'pages/file/FileSelector';
import FileManager from 'pages/ssh/FileManager';
import Output from './Output';
import { http, uniqueId } from 'libs';
import moment from 'moment';
import store from './store';
import style from './index.module.less';

function TransferIndex() {
  const [loading, setLoading] = useState(false)
  const [files, setFiles] = useState([])
  const [dir, setDir] = useState('')
  const [hosts, setHosts] = useState([])
  const [percent, setPercent] = useState()
  const [token, setToken] = useState()
  const [histories, setHistories] = useState([])
  const [fileSelectorVisible, setFileSelectorVisible] = useState(false)

  // Docker相关状态
  const [useDocker, setUseDocker] = useState(false)
  const [dockerContainers, setDockerContainers] = useState({}) // {hostId: [containers]}
  const [selectedContainers, setSelectedContainers] = useState({}) // {hostId: containerName}
  const [loadingContainers, setLoadingContainers] = useState({}) // {hostId: boolean}
  const [pathSuggestions, setPathSuggestions] = useState([])
  const [loadingPathSuggestions, setLoadingPathSuggestions] = useState(false)

  // 主机文件浏览器状态
  const [hostFileBrowserVisible, setHostFileBrowserVisible] = useState(false)
  const [selectedHost, setSelectedHost] = useState(null)



  useEffect(() => {
    if (!loading) {
      http.get('/api/exec/transfer/')
        .then(res => setHistories(res))
    }
  }, [loading])

  // 获取Docker容器列表
  const fetchDockerContainers = async (hostId) => {
    if (!hostId) {
      message.error('主机ID不能为空');
      return;
    }

    if (!useDocker) {
      return;
    }

    // 防止重复请求
    if (loadingContainers[hostId]) {
      return;
    }

    try {
      setLoadingContainers(prev => ({ ...prev, [hostId]: true }));

      const response = await http.get('/api/exec/docker-containers/', {
        params: { host_id: hostId },
        timeout: 15000 // 15秒超时
      });

      // 检查响应格式
      if (response && typeof response === 'object') {
        if (response.success === true) {
          const containers = response.containers || [];

          setDockerContainers(prev => ({
            ...prev,
            [hostId]: containers
          }));

          if (containers.length === 0) {
            message.info(`主机 ${hostId} 上没有运行中的Docker容器`);
          } else {
            message.success(`主机 ${hostId} 找到 ${containers.length} 个Docker容器`);
          }
        } else {
          const errorMsg = response.error || '服务器返回未知错误';
          message.error(`获取主机 ${hostId} 的Docker容器列表失败: ${errorMsg}`);
          setDockerContainers(prev => ({ ...prev, [hostId]: [] }));
        }
      } else {
        message.error(`获取主机 ${hostId} 的Docker容器列表失败: 响应格式异常`);
        setDockerContainers(prev => ({ ...prev, [hostId]: [] }));
      }
    } catch (error) {
      let errorMsg = '网络请求失败';
      if (error.response) {
        // 服务器响应了错误状态码
        errorMsg = `HTTP ${error.response.status}: ${error.response.data?.error || error.response.statusText}`;
      } else if (error.request) {
        // 请求发出但没有收到响应
        errorMsg = '网络请求超时，请检查网络连接';
      } else {
        // 其他错误
        errorMsg = error.message;
      }

      message.error(`获取主机 ${hostId} 的Docker容器列表失败: ${errorMsg}`);
      setDockerContainers(prev => ({ ...prev, [hostId]: [] }));
    } finally {
      setLoadingContainers(prev => ({ ...prev, [hostId]: false }));
    }
  };

  // 路径建议缓存
  const pathSuggestionsCache = React.useRef(new Map());

  // 获取路径建议
  const fetchPathSuggestions = async (hostId, containerName, pathPrefix) => {
    if (!hostId || !containerName || !pathPrefix) {
      setPathSuggestions([]);
      return;
    }

    // 检查缓存
    const cacheKey = `${hostId}-${containerName}-${pathPrefix}`;
    if (pathSuggestionsCache.current.has(cacheKey)) {
      const cachedData = pathSuggestionsCache.current.get(cacheKey);
      // 缓存5分钟
      if (Date.now() - cachedData.timestamp < 5 * 60 * 1000) {
        setPathSuggestions(cachedData.suggestions);
        return;
      }
    }

    try {
      setLoadingPathSuggestions(true);
      const response = await http.get('/api/exec/docker-path-suggestions/', {
        params: {
          host_id: hostId,
          container_name: containerName,
          path_prefix: pathPrefix
        },
        timeout: 5000 // 5秒超时
      });

      if (response.success) {
        const suggestions = response.suggestions.map(item => ({
          value: item.path,
          label: (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ marginRight: 8 }}>
                {item.is_directory ? '📁' : '📄'}
              </span>
              <span>{item.name}</span>
              <span style={{ marginLeft: 'auto', color: '#999', fontSize: '12px' }}>
                {item.type}
              </span>
            </div>
          )
        }));

        // 缓存结果
        pathSuggestionsCache.current.set(cacheKey, {
          suggestions,
          timestamp: Date.now()
        });

        setPathSuggestions(suggestions);
      } else {
        setPathSuggestions([]);
      }
    } catch (error) {
      setPathSuggestions([]);
      if (!error.response || error.response.status !== 404) {
        // 只在非404错误时显示错误消息
        message.warning('获取路径建议失败，请检查容器状态');
      }
    } finally {
      setLoadingPathSuggestions(false);
    }
  };

  function _handleProgress(e) {
    const data = e.loaded / e.total * 100
    if (!percent && data === 100) return
    setPercent(String(data).replace(/(\d+\.\d).*/, '$1'))
  }

  function handleSubmit() {
    const formData = new FormData();
    if (files.length === 0) return message.error('请添加数据源')
    if (!dir) return message.error('请输入目标路径')
    if (hosts.length === 0) return message.error('请选择目标主机')

    // Docker模式验证
    if (useDocker) {
      const missingContainers = hosts.filter(host => !selectedContainers[host.id]);
      if (missingContainers.length > 0) {
        return message.error(`请为以下主机选择Docker容器: ${missingContainers.map(h => h.name).join(', ')}`);
      }
    }

    const data = {
      dst_dir: dir,
      host_ids: hosts.map(x => x.id),
      use_docker: useDocker,
      docker_containers: useDocker ? selectedContainers : {}
    }
    const managedFiles = []
    const remoteFiles = []

    for (let index in files) {
      const item = files[index]
      if (item.type === 'host') {
        data.host = JSON.stringify([item.host_id, item.path])
      } else if (item.type === 'managed') {
        managedFiles.push(item.filename)
      } else if (item.type === 'remote') {
        remoteFiles.push({
          filename: item.filename,
          path: item.path,
          credentials: item.remoteCredentials
        })
      } else {
        formData.append(`file${index}`, item.path)
      }
    }

    if (managedFiles.length > 0) {
      data.managed_files = managedFiles
    }

    if (remoteFiles.length > 0) {
      data.remote_files = remoteFiles
    }

    formData.append('data', JSON.stringify(data))
    setLoading(true)
    http.post('/api/exec/transfer/', formData, {timeout: 600000, onUploadProgress: _handleProgress})
      .then(res => {
        const tmp = {}
        for (let host of hosts) {
          const containerInfo = useDocker && selectedContainers[host.id]
            ? ` -> 容器:${selectedContainers[host.id]}`
            : '';
          tmp[host.id] = {
            title: `${host.name}(${host.hostname}:${host.port})${containerInfo}`,
            data: '\x1b[36m### WebSocket connecting ...\x1b[0m',
            status: -2
          }
        }
        store.outputs = tmp
        setToken(res)
      })
      .finally(() => {
        setLoading(false)
        setPercent()
      })
  }

  function makeFile(row) {
    setSelectedHost(row);
    setHostFileBrowserVisible(true);
  }

  // 处理主机文件选择
  function handleHostFileSelect(fileInfo) {
    // 文件分发功能需要的是文件路径，不是目录路径
    const filePath = fileInfo.path;

    setFiles([{
      id: uniqueId(),
      type: 'host',
      name: `${selectedHost.name} (${selectedHost.hostname})`,
      path: filePath, // 直接使用文件路径
      host_id: selectedHost.id
    }]);

    setHostFileBrowserVisible(false);
    setSelectedHost(null);
    message.success(`已添加主机文件: ${filePath}`);
  }

  function handleSelectManagedFiles(selectedFiles) {
    const tmp = files.length > 0 && (files[0].type === 'managed' || files[0].type === 'remote') ? [...files] : []
    
    for (let file of selectedFiles) {
      if (file.source === 'remote') {
        // 远程文件
        tmp.push({
          id: uniqueId(),
          type: 'remote',
          name: `远程: ${file.remoteFolder.name}`,
          filename: file.name,
          size: file.size_human,
          path: file.path || file.name,
          remoteFolder: file.remoteFolder,
          remoteCredentials: file.remoteCredentials
        })
      } else {
        // 本地文件管理器文件
        tmp.push({
          id: uniqueId(),
          type: 'managed',
          name: '文件管理器',
          filename: file.name,
          size: file.size_human,
          path: file.path || file.name
        })
      }
    }
    
    setFiles(tmp)
    setFileSelectorVisible(false)
    message.success(`已选择 ${selectedFiles.length} 个文件`)
  }

  function handleUpload(_, fileList) {
    const tmp = files.length > 0 && files[0].type === 'upload' ? [...files] : []
    for (let file of fileList) {
      tmp.push({id: uniqueId(), type: 'upload', name: '本地上传', path: file})
    }
    setFiles(tmp)
    return Upload.LIST_IGNORE
  }

  // 处理拖拽上传
  function handleDragUpload(fileList) {
    const tmp = files.length > 0 && files[0].type === 'upload' ? [...files] : []
    for (let file of fileList) {
      tmp.push({id: uniqueId(), type: 'upload', name: '本地上传', path: file})
    }
    setFiles(tmp)
    message.success(`已添加 ${fileList.length} 个文件`)
  }

  function handleRemove(index) {
    files.splice(index, 1)
    setFiles([...files])
  }

  function handleCloseOutput() {
    setToken()
    if (!store.counter['0'] && !store.counter['2']) {
      setFiles([])
    }
  }

  // 处理主机选择变化
  const handleHostsChange = (newHosts) => {
    setHosts(newHosts);

    if (useDocker) {
      // 为新选择的主机获取容器列表
      newHosts.forEach(host => {
        if (!dockerContainers[host.id]) {
          fetchDockerContainers(host.id);
        }
      });

      // 清理不再选择的主机的容器选择
      const newHostIds = newHosts.map(h => h.id);
      const updatedSelectedContainers = {};
      Object.keys(selectedContainers).forEach(hostId => {
        if (newHostIds.includes(parseInt(hostId))) {
          updatedSelectedContainers[hostId] = selectedContainers[hostId];
        }
      });
      setSelectedContainers(updatedSelectedContainers);
    }
  };

  // 处理Docker模式切换
  const handleDockerModeChange = (checked) => {
    setUseDocker(checked);

    if (checked) {
      // 切换到Docker模式，为已选择的主机获取容器列表
      hosts.forEach(host => {
        fetchDockerContainers(host.id);
      });
    } else {
      // 切换到普通模式，清理Docker相关状态
      setSelectedContainers({});
      setDockerContainers({});
      setPathSuggestions([]);
    }
  };

  // 处理容器选择变化
  const handleContainerChange = (hostId, containerName) => {
    setSelectedContainers(prev => ({
      ...prev,
      [hostId]: containerName
    }));
  };

  // 处理路径输入变化（用于路径建议）
  const handlePathChange = (value) => {
    setDir(value);

    // 如果是Docker模式且有选择的容器，获取路径建议
    if (useDocker && value && value.length > 1 && hosts.length > 0) {
      // 找到第一个有容器选择的主机
      const hostWithContainer = hosts.find(host => selectedContainers[host.id]);
      if (hostWithContainer) {
        const containerName = selectedContainers[hostWithContainer.id];
        // 防抖处理
        clearTimeout(window.pathSuggestionTimeout);
        window.pathSuggestionTimeout = setTimeout(() => {
          fetchPathSuggestions(hostWithContainer.id, containerName, value);
        }, 500);
      }
    } else if (!useDocker || !value) {
      // 清空建议
      setPathSuggestions([]);
    }
  };

  // 处理路径搜索（当用户在AutoComplete中搜索时）
  const handlePathSearch = (searchText) => {
    if (useDocker && searchText && searchText.length > 1 && hosts.length > 0) {
      const hostWithContainer = hosts.find(host => selectedContainers[host.id]);
      if (hostWithContainer) {
        const containerName = selectedContainers[hostWithContainer.id];
        fetchPathSuggestions(hostWithContainer.id, containerName, searchText);
      }
    }
  };









  return (<AuthDiv auth="exec.transfer.do">
    <Breadcrumb>
      <Breadcrumb.Item>首页</Breadcrumb.Item>
      <Breadcrumb.Item>批量执行</Breadcrumb.Item>
      <Breadcrumb.Item>文件分发</Breadcrumb.Item>
    </Breadcrumb>
    <div className={style.index} hidden={token}>
      <div className={style.left}>
        <Card type="inner" title={`数据源${files.length ? `（${files.length}）` : ''}`}>
          {/* 操作按钮区域 */}
          <div style={{ marginBottom: 16, display: 'flex', gap: 12, flexWrap: 'wrap' }}>
            <Upload multiple beforeUpload={handleUpload}>
              <Button icon={<UploadOutlined />}>上传本地文件</Button>
            </Upload>
            <Button
              icon={<FolderOpenOutlined />}
              onClick={() => setFileSelectorVisible(true)}
            >
              选择文件管理器文件
            </Button>
            <HostSelector onlyOne mode="rows" onChange={row => makeFile(row)}>
              <Button icon={<CloudServerOutlined />}>添加主机文件</Button>
            </HostSelector>
          </div>

          {/* 拖拽上传区域 */}
          {files.length === 0 ? (
            <DragUpload
              onUpload={handleDragUpload}
              multiple={true}
              maxSize={0}
              disabled={loading}
            >
              <div className={style.fileManagerDragArea}>
                <InboxOutlined className={style.icon} />
                <p className={style.text}>
                  拖拽文件到这里或使用上方按钮添加数据源<br/>
                  支持多文件上传，无大小限制
                </p>
              </div>
            </DragUpload>
          ) : (
            <>
              {/* 简化的拖拽上传区域 */}
              <div style={{ marginBottom: 16 }}>
                <DragUpload
                  onUpload={handleDragUpload}
                  multiple={true}
                  maxSize={0}
                  disabled={loading}
                >
                  <div className={style.transferDragArea}>
                    <InboxOutlined className={style.icon} />
                    <p className={style.text}>拖拽文件到这里快速添加</p>
                  </div>
                </DragUpload>
              </div>
              
              <Table rowKey="id" className={style.table} showHeader={false} pagination={false} size="small"
                     dataSource={files}>
                <Table.Column title="文件来源" dataIndex="name"/>
                <Table.Column title="文件名称/路径" render={info => {
                  if (info.type === 'upload') {
                    return info.path.name
                  } else if (info.type === 'managed') {
                    return `${info.filename} (${info.size})`
                  } else if (info.type === 'remote') {
                    return (
                      <div>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <CloudServerOutlined style={{ color: '#52c41a', marginRight: 4 }} />
                          <span>{info.filename} ({info.size})</span>
                        </div>
                        <div style={{ fontSize: 12, color: '#666', marginTop: 2 }}>
                          来源: {info.remoteFolder.name}
                        </div>
                      </div>
                    )
                  } else {
                    // 主机文件类型
                    return (
                      <div>
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                          <CloudServerOutlined style={{ color: '#1890ff', marginRight: 4 }} />
                          <span style={{ fontWeight: 500 }}>{info.path || '未设置路径'}</span>
                        </div>
                        <div style={{ fontSize: 12, color: '#666' }}>
                          来源: {info.name}
                        </div>
                        {!info.path && (
                          <Button
                            size="small"
                            type="link"
                            onClick={() => {
                              const newPath = prompt('请输入源目录路径:', '/');
                              if (newPath && newPath.trim()) {
                                info.path = newPath.trim();
                                setFiles([...files]); // 触发重新渲染
                              }
                            }}
                          >
                            设置路径
                          </Button>
                        )}
                      </div>
                    )
                  }
                }}/>
                <Table.Column title="操作" render={(_, __, index) => (
                  <Button danger type="link" onClick={() => handleRemove(index)}>移除</Button>)}/>
              </Table>
            </>
          )}
        </Card>
      </div>

      <div className={style.right}>
        {/* 分发目标区域 */}
        <Card
          type="inner"
          title="分发目标"
          style={{marginBottom: 16}}
          bodyStyle={{paddingBottom: 16}}
          extra={(
            <Button
              type="primary"
              icon={<ThunderboltOutlined />}
              loading={loading}
              disabled={files.length === 0 || hosts.length === 0 || !dir}
              onClick={handleSubmit}
            >
              {percent ? `上传中 ${percent}%` : useDocker ? '开始Docker分发' : '开始分发'}
            </Button>
          )}
        >
          <Form>
            <Form.Item label="分发模式">
              <Space>
                <Switch
                  checked={useDocker}
                  onChange={handleDockerModeChange}
                  checkedChildren={<DockerOutlined />}
                  unCheckedChildren="普通"
                />
                <span style={{ color: useDocker ? '#1890ff' : '#666' }}>
                  {useDocker ? 'Docker容器模式' : '普通主机模式'}
                </span>
                {useDocker && hosts.length > 0 && (
                  <Button
                    size="small"
                    onClick={() => {
                      hosts.forEach(host => fetchDockerContainers(host.id));
                    }}
                  >
                    刷新所有容器
                  </Button>
                )}
              </Space>
            </Form.Item>

            <Form.Item required label="目标路径">
              {useDocker ? (
                <AutoComplete
                  value={dir}
                  options={pathSuggestions}
                  onChange={handlePathChange}
                  onSearch={handlePathSearch}
                  placeholder="请输入容器内目标路径，支持自动补全"
                  style={{ width: '100%' }}
                  notFoundContent={loadingPathSuggestions ? '正在获取路径建议...' : '输入路径以获取建议'}
                  filterOption={false} // 禁用本地过滤，使用服务器端搜索
                />
              ) : (
                <Input
                  value={dir}
                  onChange={e => setDir(e.target.value)}
                  placeholder="请输入目标路径"
                />
              )}
            </Form.Item>

            <Form.Item required label="目标主机">
              <HostSelector
                type="button"
                mode="rows"
                value={hosts.map(x => x.id)}
                onChange={handleHostsChange}
              />
            </Form.Item>

            {useDocker && (
              <Form.Item required label="Docker容器">
                {hosts.length === 0 ? (
                  <div style={{ padding: '20px', textAlign: 'center', color: '#999', border: '1px dashed #d9d9d9', borderRadius: '6px' }}>
                    请先选择目标主机
                  </div>
                ) : (
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {hosts.map(host => {
                      const containers = dockerContainers[host.id] || [];
                      const isLoading = loadingContainers[host.id];
                      const selectedContainer = selectedContainers[host.id];

                      return (
                        <div key={host.id} style={{
                          border: '1px solid #f0f0f0',
                          borderRadius: '6px',
                          padding: '12px',
                          backgroundColor: '#fafafa'
                        }}>
                          <div style={{ marginBottom: 8, fontWeight: 500 }}>
                            <CloudServerOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                            {host.name} ({host.hostname})
                          </div>

                          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                            <Select
                              style={{ flex: 1 }}
                              placeholder={isLoading ? "正在获取容器列表..." : "请选择Docker容器"}
                              loading={isLoading}
                              value={selectedContainer}
                              onChange={(value) => handleContainerChange(host.id, value)}
                              showSearch
                              filterOption={(input, option) =>
                                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                              }
                              notFoundContent={
                                isLoading
                                  ? "正在获取容器列表..."
                                  : "未找到运行中的容器，请检查Docker服务状态"
                              }
                            >
                              {containers.map(container => (
                                <Select.Option key={container.name} value={container.name}>
                                  <Space>
                                    <DockerOutlined style={{ color: '#1890ff' }} />
                                    <span>{container.name}</span>
                                    <span style={{ color: '#999', fontSize: '12px' }}>
                                      ({container.image})
                                    </span>
                                  </Space>
                                </Select.Option>
                              ))}
                            </Select>

                            <Button
                              icon={<ReloadOutlined />}
                              onClick={() => fetchDockerContainers(host.id)}
                              loading={isLoading}
                              title="刷新容器列表"
                            >
                              刷新
                            </Button>
                          </div>

                          <div style={{ fontSize: '12px', color: '#666', marginTop: 8 }}>
                            状态: {isLoading ? '正在加载...' : `找到 ${containers.length} 个容器`}
                            {selectedContainer && ` | 已选择: ${selectedContainer}`}
                          </div>
                        </div>
                      );
                    })}
                  </Space>
                )}
              </Form.Item>
            )}
          </Form>

          {/* 小提示 */}
          <div style={{
            marginTop: 16,
            padding: 12,
            background: '#f0f8ff',
            borderRadius: 6,
            fontSize: 12,
            color: '#666',
            display: 'flex',
            alignItems: 'center'
          }}>
            <BulbOutlined style={{ color: '#1890ff', marginRight: 8 }} />
            {useDocker
              ? "Docker模式：文件将分发到指定容器内的目标路径。支持路径自动补全。"
              : "文件分发功能依赖rsync，大部分linux发行版默认都已安装，如未安装可通过「批量执行/执行任务」进行批量安装。"}
          </div>
        </Card>


        {/* 分发记录区域 */}
        <div className={style.historySection}>
          <div className={style.historyTitle}>
            分发记录
            <Tooltip title="每天自动清理，保留最近30条记录。">
              <QuestionCircleOutlined style={{color: '#999', marginLeft: 8}}/>
            </Tooltip>
          </div>
          <div className={style.historyList}>
            {histories.map((item, index) => (<div key={index} className={style.item}>
              {item.host_id ? (
                <CloudServerOutlined className={style.host}/>
              ) : (
                <UploadOutlined className={style.upload}/>
              )}
              <div className={style[item.interpreter]}>{item.interpreter}</div>
              <div className={style.number}>{item.host_ids.length}</div>
              <div className={style.command}>{item.dst_dir}</div>
              <div className={style.desc}>{moment(item.updated_at).format('MM.DD HH:mm')}</div>
            </div>))}
          </div>
        </div>
      </div>
    </div>
    
    <FileSelector
      visible={fileSelectorVisible}
      onCancel={() => setFileSelectorVisible(false)}
      onSelect={handleSelectManagedFiles}
      multiple={true}
    />
    


    {/* 主机文件浏览器 */}
    <Modal
      title={`浏览主机文件 - ${selectedHost?.name || '未知主机'}`}
      visible={hostFileBrowserVisible}
      onCancel={() => {
        setHostFileBrowserVisible(false);
        setSelectedHost(null);
      }}
      footer={null}
      width={900}
      bodyStyle={{ padding: 0, height: '70vh' }}
    >
      {selectedHost && (
        <div style={{ height: '100%', overflow: 'hidden' }}>
          <div style={{
            padding: '12px 16px',
            background: '#f0f8ff',
            borderBottom: '1px solid #d9d9d9',
            fontSize: '14px'
          }}>
            <strong>提示:</strong> 双击文件名选择文件，双击文件夹进入目录
          </div>

          <div style={{
            height: 'calc(100% - 53px)',
            overflow: 'auto'
          }}>
            <FileManager
              id={selectedHost.id}
              onFileSelect={handleHostFileSelect}
            />
          </div>
        </div>
      )}
    </Modal>

    {token ? <Output token={token} onBack={handleCloseOutput}/> : null}
  </AuthDiv>)
}

export default observer(TransferIndex)
