import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Upload, message, Modal, Space, Input, Tag, Tooltip } from 'antd';
import { UploadOutlined, EyeOutlined, DeleteOutlined, DownloadOutlined } from '@ant-design/icons';
import http from 'libs/http';

const { Search } = Input;

function ModelPerformanceManager() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [selectedModel, setSelectedModel] = useState(null);
  const [detailVisible, setDetailVisible] = useState(false);

  // 获取数据
  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await http.get('/api/model-storage/model-performance-data/', {
        params: { token: '1' } // 使用开发者后门
      });
      setData(response);
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // 导入Excel (暂时禁用，避免process错误)
  const importFromExcel = (file) => {
    message.info('Excel导入功能正在开发中，请稍后使用');
    return false;
  };

  // 查看详情
  const viewDetails = (record) => {
    setSelectedModel(record);
    setDetailVisible(true);
  };

  // 删除模型
  const deleteModel = (record) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除模型 "${record.model_name}" 的所有数据吗？此操作不可恢复。`,
      onOk: async () => {
        try {
          await http.delete(`/api/model-storage/model-performance-data/${record.id}/`, {
            params: { token: '1' } // 使用开发者后门
          });
          message.success('删除成功');
          fetchData();
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败: ' + (error.response?.data?.error || error.message));
        }
      }
    });
  };

  // 导出Excel (暂时禁用)
  const exportToExcel = (record) => {
    message.info('Excel导出功能正在开发中，请稍后使用');
  };

  // 表格列定义
  const columns = [
    {
      title: '模型名称',
      dataIndex: 'model_name',
      key: 'model_name',
      filteredValue: searchText ? [searchText] : null,
      onFilter: (value, record) => 
        record.model_name.toLowerCase().includes(value.toLowerCase()),
      render: (text) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{text}</span>
      )
    },
    {
      title: '框架',
      dataIndex: 'framework',
      key: 'framework',
      width: 100,
      render: (text) => text ? <Tag color="blue">{text}</Tag> : '-'
    },
    {
      title: '版本',
      dataIndex: 'framework_version',
      key: 'framework_version',
      width: 100
    },
    {
      title: '测试文件数',
      dataIndex: 'total_test_files',
      key: 'total_test_files',
      width: 100,
      render: (count) => (
        <Tag color={count > 0 ? 'green' : 'default'}>
          {count} 个
        </Tag>
      )
    },
    {
      title: '最后更新',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 150,
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => viewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="导出Excel">
            <Button
              type="link"
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => exportToExcel(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => deleteModel(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card 
        title="模型性能数据管理"
        extra={
          <Space>
            <Search
              placeholder="搜索模型名称..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
              allowClear
            />
            <Upload
              accept=".xlsx,.xls"
              beforeUpload={importFromExcel}
              showUploadList={false}
            >
              <Button icon={<UploadOutlined />}>
                导入Excel
              </Button>
            </Upload>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={data}
          loading={loading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个模型`
          }}
        />
      </Card>

      {/* 详情弹窗 */}
      <Modal
        title={`模型详情 - ${selectedModel?.model_name}`}
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={1200}
      >
        {selectedModel && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Tag color="blue">框架: {selectedModel.framework || '未知'}</Tag>
                <Tag color="green">版本: {selectedModel.framework_version || '未知'}</Tag>
                <Tag color="orange">测试文件: {selectedModel.total_test_files} 个</Tag>
              </Space>
            </div>
            
            <Table
              size="small"
              dataSource={selectedModel.test_results || []}
              scroll={{ x: 1000, y: 400 }}
              pagination={false}
              columns={[
                { title: '文件名', dataIndex: 'filename', width: 150, fixed: 'left' },
                { title: '成功请求数', dataIndex: 'success_requests', width: 100 },
                { title: '基准时长(s)', dataIndex: 'benchmark_duration', width: 100 },
                { title: '请求吞吐量', dataIndex: 'request_throughput', width: 100 },
                { title: '平均TTFT(ms)', dataIndex: 'avg_ttft', width: 100 },
                { title: '平均TPOT(ms)', dataIndex: 'avg_tpot', width: 100 },
                { title: '机型', dataIndex: 'machine_model', width: 100 },
                { title: '数据集', dataIndex: 'dataset', width: 100 },
                { title: '备注', dataIndex: 'remarks', width: 150 }
              ]}
            />
          </div>
        )}
      </Modal>
    </div>
  );
}

export default ModelPerformanceManager;
