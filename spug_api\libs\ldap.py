# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from ldap3 import Server, Connection, ALL_ATTRIBUTES, SUBTREE


class LDAP:
    def __init__(self, server, port, rules, admin_dn, password, base_dn):
        self.server = server
        self.port = port
        self.rules = rules
        self.admin_dn = admin_dn
        self.password = password
        self.base_dn = base_dn

    def valid_user(self, username, password):
        try:
            # 创建服务器对象
            server = Server(f"{self.server}:{self.port}")
            
            # 先用管理员账户连接，搜索用户
            admin_conn = Connection(server, user=self.admin_dn, password=self.password, auto_bind=True)
            
            # 搜索用户
            search_filter = f'({self.rules}={username})'
            admin_conn.search(self.base_dn, search_filter, search_scope=SUBTREE, attributes=ALL_ATTRIBUTES)
            
            if len(admin_conn.entries) > 0:
                # 获取用户DN
                user_dn = admin_conn.entries[0].entry_dn
                admin_conn.unbind()
                
                # 尝试用用户凭据绑定
                user_conn = Connection(server, user=user_dn, password=password, auto_bind=True)
                user_conn.unbind()
                return True, None
            else:
                admin_conn.unbind()
                return False, None
        except Exception as error:
            return False, str(error)
