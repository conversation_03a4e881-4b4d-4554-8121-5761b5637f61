# Generated by Django 2.2.28 on 2025-07-07 09:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0006_auto_20250705_1525'),
    ]

    operations = [
        migrations.AlterField(
            model_name='testtask',
            name='gpu_model',
            field=models.CharField(blank=True, choices=[('P800', 'P800'), ('P800-PCIe', 'P800-PCIe'), ('RG800', 'RG800'), ('MLU370-X8', 'MLU370-X8'), ('K100-AI', 'K100-AI'), ('S60', 'S60 G7'), ('L20', 'L20'), ('C500', 'C500'), ('C550', 'C550'), ('C500X', 'C500X'), ('MR-V100', 'MR-V100'), ('BI-V150', 'BI-V150'), ('GPU-104P', 'GPU-104P')], default='P800', max_length=32, null=True, verbose_name='GPU型号'),
        ),
        migrations.AlterField(
            model_name='testtask',
            name='model_type',
            field=models.CharField(blank=True, choices=[('inference', '推理'), ('training', '训练'), ('fine_tuning', '微调'), ('pre-training', '预训练'), ('optimization', '优化')], default='inference', max_length=32, null=True, verbose_name='模型类型'),
        ),
    ]
