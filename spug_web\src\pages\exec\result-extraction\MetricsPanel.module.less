.metricsPanel {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .panelHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  
  .dropZone {
    flex: 1;
    position: relative;
    transition: all 0.3s ease;
    border: 2px dashed transparent;
    border-radius: 6px;
    padding: 8px;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0;
    
    /* 确保滚动条可见 */
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
      
      &:hover {
        background: #a1a1a1;
      }
    }
    
    /* 确保Ant Design List组件不添加额外高度 */
    :global(.ant-list) {
      height: auto;
      max-height: none;
    }
    
    :global(.ant-list-items) {
      max-height: none;
    }
    
    &.dragOver {
      border-color: #1890ff;
      background: rgba(24, 144, 255, 0.05);
      
      .dropHint {
        display: flex;
      }
    }
    
    .dropHint {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: none;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: white;
      border: 2px solid #1890ff;
      border-radius: 8px;
      padding: 24px;
      color: #1890ff;
      font-size: 16px;
      font-weight: 500;
      z-index: 10;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
    }
  }

  .emptyState {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #999;
    text-align: center;
  }
  
  .metricsCard {
    height: 100%;
    max-height: 100%;
    display: flex;
    flex-direction: column;
    
    :global(.ant-card-body) {
      padding: 16px;
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
  }
  
  .panelTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-weight: 600;
    flex: 1;
    margin-right: 16px;
    
    .progressInfo {
      display: flex;
      align-items: center;
    }
  }
  
  .batchToolbar {
    padding: 8px 12px;
    background: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 16px;
  }
  
  .metricItem {
    border: 1px solid #e8e8e8;
    border-radius: 12px;
    margin-bottom: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    background: #fff;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: #d9d9d9;
      transition: all 0.3s ease;
    }

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 4px 20px rgba(24, 144, 255, 0.15);
      transform: translateY(-2px);

      &::before {
        background: #1890ff;
      }
    }

    &.selected {
      border-color: #1890ff;
      background: rgba(24, 144, 255, 0.05);

      &::before {
        background: #1890ff;
      }
    }

    &.confirmed {
      background: linear-gradient(135deg, #f6ffed 0%, #f0f9e8 100%);
      border-color: #52c41a;

      &::before {
        background: #52c41a;
      }

      &:hover {
        border-color: #389e0d;
        box-shadow: 0 4px 20px rgba(82, 196, 26, 0.2);
      }
    }

    &.pending {
      background: linear-gradient(135deg, #fffbe6 0%, #fff7e6 100%);
      border-color: #faad14;

      &::before {
        background: #faad14;
      }

      &:hover {
        border-color: #d48806;
        box-shadow: 0 4px 20px rgba(250, 173, 20, 0.2);
      }
    }

    &.rejected {
      background: linear-gradient(135deg, #fff2f0 0%, #ffebe8 100%);
      border-color: #ff4d4f;

      &::before {
        background: #ff4d4f;
      }

      &:hover {
        border-color: #cf1322;
        box-shadow: 0 4px 20px rgba(255, 77, 79, 0.2);
      }
    }
    
    :global(.ant-list-item-action) {
      margin-left: 16px;
    }
  }
  
  .metricHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
    
    .metricLabel {
      font-weight: 500;
      color: #262626;
    }
  }
  
  .metricContent {
    display: flex;
    align-items: flex-start;
    width: 100%;
    
    .metricInfo {
      flex: 1;
      
      .metricHeader {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        
        .metricLabel {
          font-weight: 500;
          color: #262626;
          flex: 1;
        }
      }
      
      .metricValue {
        margin-bottom: 12px;
        display: flex;
        align-items: baseline;

        .value {
          font-size: 24px;
          font-weight: 700;
          color: #1890ff;
          font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;

          .unit {
            font-size: 14px;
            color: #666;
            margin-left: 6px;
            font-weight: 500;
            margin-left: 6px;
            font-weight: normal;
          }
        }
      }
      
      .metricMeta {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;
        margin-bottom: 4px;
        
        .confidence {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: #666;

          .confidenceBar {
            width: 60px;
            height: 4px;
            background: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;

            .confidenceFill {
              height: 100%;
              border-radius: 2px;
              transition: width 0.3s ease;

              &.high {
                background: linear-gradient(90deg, #52c41a, #73d13d);
              }

              &.medium {
                background: linear-gradient(90deg, #faad14, #ffc53d);
              }

              &.low {
                background: linear-gradient(90deg, #ff4d4f, #ff7875);
              }
            }
          }

          .confidenceText {
            font-weight: 500;
          }
        }

        .lineNumber {
          font-size: 12px;
          color: #999;
          background: #f5f5f5;
          padding: 2px 6px;
          border-radius: 4px;
          font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
          font-weight: 500;
        }
      }
      
      .originalText {
        font-size: 11px;
        color: #999;
        margin-top: 4px;
        font-style: italic;
      }
    }
  }

  .editingItem {
    border: 2px solid #1890ff;
    border-radius: 6px;
    margin-bottom: 8px;
    
    .editForm {
      padding: 12px;
    }
  }
} 