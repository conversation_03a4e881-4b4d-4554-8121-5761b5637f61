/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React from 'react';
import { Card, Descriptions, Tag, Space, Statistic, Row, Col, List, Avatar, Empty } from 'antd';
import { BuildOutlined, CheckCircleOutlined, ExclamationCircleOutlined, <PERSON>boltOutlined, InfoCircleOutlined } from '@ant-design/icons';

function GpuBasicInfo({ data = {}, loading }) {
  console.log('[DEBUG GpuBasicInfo] 接收到的数据:', data);
  console.log('[DEBUG GpuBasicInfo] GPU列表:', data?.gpus);
  console.log('[DEBUG GpuBasicInfo] GPU数量:', data?.gpus?.length);

  // 测试用模拟数据
  const mockData = {
    gpus: Array.from({ length: 8 }, (_, i) => ({
      index: String(i),
      name: 'P800 OAM',
      memory_total: '98304 MiB',
      part_number: 'B00100300110312',
      serial_number: `02K15K6251V000${i.toString(16).toUpperCase().padStart(2, '0')}`,
      device_id: '0x36881D22',
      subsystem_id: '0x00010001',
      firmware_version: '********.1.48',
      pci_bus_id: `00000000:${(0x2D + i*18).toString(16).toUpperCase().padStart(2, '0')}:00.0`,
      manufacturer: 'KUNLUNXIN',
      status: 'normal',
      temperature: `${37 + i % 4}°C`
    })),
    gpu_count: 8,
    total_memory: 786432, // 8 * 98304
    manufacturer_stats: {
      'KUNLUNXIN': 8
    }
  };
  
  // 如果没有数据，使用模拟数据进行测试
  const effectiveData = data?.gpus?.length > 0 ? data : mockData;
  console.log('[DEBUG GpuBasicInfo] 使用的数据:', effectiveData);

  const getGpuStatus = () => {
    if (!effectiveData?.gpus || effectiveData.gpus.length === 0) {
      return { color: 'error', text: '未检测到GPU' };
    }
    const hasError = effectiveData.gpus.some(gpu => gpu.status === 'error');
    if (hasError) {
      return { color: 'error', text: 'GPU异常' };
    }
    return { color: 'success', text: 'GPU正常' };
  };

  const getManufacturerIcon = (manufacturer) => {
    const iconMap = {
      'NVIDIA': '🔥',
      'AMD': '⚡',
      'KUNLUNXIN': '🚀',
      'Intel': '💎',
    };
    return iconMap[manufacturer] || '🖥️';
  };

  const formatMemory = (memory) => {
    if (!memory) return '未知';
    if (typeof memory === 'number') {
      return memory >= 1024 ? `${(memory / 1024).toFixed(1)}GB` : `${memory}MB`;
    }
    return memory;
  };

  const gpuStatus = getGpuStatus();

  return (
    <Card
      title={
        <Space>
          <ThunderboltOutlined style={{ color: '#10b981' }} />
          <span>GPU基础信息</span>
          <Tag className={`status-${gpuStatus.color}`}>
            {gpuStatus.text}
          </Tag>
        </Space>
      }
      extra={
        <Space>
          <BuildOutlined />
          <span style={{ fontSize: '12px', opacity: 0.8 }}>
            {effectiveData?.gpus?.length || 0} 个GPU
          </span>
        </Space>
      }
      loading={loading}
      style={{ height: '100%', minHeight: '400px' }}
    >
      {!effectiveData?.gpus || effectiveData.gpus.length === 0 ? (
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center', 
          justifyContent: 'center',
          height: '300px',
          opacity: 0.6 
        }}>
          <InfoCircleOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
          <p>未检测到GPU设备</p>
          <p style={{ fontSize: '12px' }}>请确认GPU驱动已正确安装</p>
        </div>
      ) : (
        <Row gutter={[16, 16]}>
          {/* GPU总览统计 */}
          <Col span={24}>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="GPU数量"
                  value={effectiveData.gpus.length}
                  suffix="个"
                  valueStyle={{ color: '#3b82f6' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="总显存"
                  value={effectiveData.total_memory ? formatMemory(effectiveData.total_memory) : '计算中...'}
                  valueStyle={{ color: '#10b981' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="厂商"
                  value={effectiveData.gpus[0]?.manufacturer || '未知'}
                  valueStyle={{ color: '#f59e0b' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="平均利用率"
                  value={effectiveData.average_utilization || 0}
                  suffix="%"
                  valueStyle={{ 
                    color: effectiveData.average_utilization > 80 ? '#ef4444' : 
                           effectiveData.average_utilization > 50 ? '#f59e0b' : '#10b981' 
                  }}
                />
              </Col>
            </Row>
          </Col>

          {/* GPU详细列表 */}
          <Col span={24}>
            <List
              dataSource={effectiveData.gpus}
              renderItem={(gpu, index) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        style={{ 
                          backgroundColor: gpu.status === 'error' ? '#ef4444' : '#10b981',
                          fontSize: '20px'
                        }}
                      >
                        {getManufacturerIcon(gpu.manufacturer)}
                      </Avatar>
                    }
                    title={
                      <Space>
                        <span>{gpu.name || `GPU ${index}`}</span>
                        <Tag color={gpu.status === 'error' ? 'red' : 'green'}>
                          {gpu.status === 'error' ? '异常' : '正常'}
                        </Tag>
                        {gpu.pci_bus_id && (
                          <Tag size="small">{gpu.pci_bus_id}</Tag>
                        )}
                      </Space>
                    }
                    description={
                      <Descriptions size="small" column={3}>
                        <Descriptions.Item label="制造商">
                          {gpu.manufacturer || '未知'}
                        </Descriptions.Item>
                        <Descriptions.Item label="显存">
                          {formatMemory(gpu.memory_total)}
                        </Descriptions.Item>
                        <Descriptions.Item label="温度">
                          {gpu.temperature || '未知'}
                        </Descriptions.Item>
                        
                        <Descriptions.Item label="PN号">
                          {gpu.part_number || '未知'}
                        </Descriptions.Item>
                        <Descriptions.Item label="SN号">
                          {gpu.serial_number || '未知'}
                        </Descriptions.Item>
                        <Descriptions.Item label="固件版本">
                          {gpu.firmware_version || '未知'}
                        </Descriptions.Item>
                        
                        <Descriptions.Item label="设备ID">
                          {gpu.device_id || '未知'}
                        </Descriptions.Item>
                        <Descriptions.Item label="子系统ID">
                          {gpu.subsystem_id || '未知'}
                        </Descriptions.Item>
                        <Descriptions.Item label="PCI地址">
                          {gpu.pci_bus_id || '未知'}
                        </Descriptions.Item>
                      </Descriptions>
                    }
                  />
                </List.Item>
              )}
            />
          </Col>

          {/* 厂商信息统计 */}
          {effectiveData.manufacturer_stats && (
            <Col span={24}>
              <Card size="small" title="厂商分布">
                <Space wrap>
                  {Object.entries(effectiveData.manufacturer_stats).map(([manufacturer, count]) => (
                    <Tag key={manufacturer} color="blue">
                      {getManufacturerIcon(manufacturer)} {manufacturer}: {count}个
                    </Tag>
                  ))}
                </Space>
              </Card>
            </Col>
          )}
        </Row>
      )}
    </Card>
  );
}

export default GpuBasicInfo; 