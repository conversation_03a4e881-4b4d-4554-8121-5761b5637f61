# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from django.conf.urls import url

from apps.exec.views import *
from apps.exec.transfer import TransferView

urlpatterns = [
    url(r'template/$', TemplateView.as_view()),
    url(r'do/$', TaskView.as_view()),
    url(r'transfer/$', TransferView.as_view()),
    # 测试计划管理
    url(r'test-plans/$', TestPlanView.as_view()),
    url(r'test-plans/(\d+)/$', TestPlanView.as_view()),
    # 测试用例集管理
    url(r'test-case-sets/$', TestCaseSetView.as_view()),
    url(r'test-case-sets/(\d+)/$', TestCaseSetView.as_view()),
    # 测试用例池管理
    url(r'test-case-templates/$', TestCaseTemplateView.as_view()),
    url(r'test-case-templates/(\d+)/$', TestCaseTemplateView.as_view()),
    url(r'test-case-templates/(\d+)/usage/$', TestCaseTemplateUsageView.as_view()),

    # 测试用例集导入导出
    url(r'test-case-sets-import-export/$', TestCaseSetImportExportView.as_view()),
    # 测试计划导入导出
    url(r'test-plans-import-export/$', TestPlanImportExportView.as_view()),
    # 测试计划执行
    url(r'test-plan-executions/$', TestPlanExecutionView.as_view()),
    url(r'test-plan-executions/([a-f0-9]{32}|\d+)/$', TestPlanExecutionView.as_view()),
    # 测试计划执行历史记录
    url(r'test-plan-execution-history/$', TestPlanExecutionHistoryView.as_view()),
    url(r'test-plan-execution-history/(\d+)/$', TestPlanExecutionHistoryView.as_view()),
    # 保持兼容性 - 旧的命令卡片路由暂时保留
    url(r'command-cards/$', TestPlanView.as_view()),
    url(r'command-cards/(\d+)/$', TestPlanView.as_view()),
    # 示例数据管理 - 仅用于开发和测试
    url(r'sample-data/$', SampleDataView.as_view()),
    # SSH连接测试
    url(r'ssh-connection-test/$', SSHConnectionTestView.as_view()),
    # 测试结果收集
    url(r'test-results/$', TestResultView.as_view()),
    url(r'test-results/(\d+)/$', TestResultView.as_view()),
    # 测试结果对比
    url(r'test-results/compare/$', TestResultView.as_view()),
    # 测试结果提取模板
    url(r'test-result-templates/$', TestResultTemplateView.as_view()),
    url(r'test-result-templates/(\d+)/$', TestResultTemplateView.as_view()),
    # 远程日志获取
    url(r'remote-log-fetch/$', RemoteLogFetchView.as_view()),
    # 多文件远程日志获取
    url(r'remote-log-fetch-multiple/$', RemoteLogFetchMultipleView.as_view()),
    # SSH命令执行
    url(r'ssh/$', ExecView.as_view()),
    # Docker容器列表获取
    url(r'docker-containers/$', DockerContainerListView.as_view()),
    # Docker容器文件系统浏览
    url(r'docker-filesystem/$', DockerFileSystemView.as_view()),
    # Docker容器路径自动补全
    url(r'docker-path-autocomplete/$', DockerPathAutoCompleteView.as_view()),
    # Docker容器文件内容获取
    url(r'docker-file-content/$', DockerFileContentView.as_view()),
    # Docker容器路径建议
    url(r'docker-path-suggestions/$', DockerPathSuggestionView.as_view()),
    # 文件分发配置模板
    url(r'transfer-templates/$', TransferTemplateView.as_view()),
    url(r'transfer-templates/(\d+)/$', TransferTemplateView.as_view()),
]
