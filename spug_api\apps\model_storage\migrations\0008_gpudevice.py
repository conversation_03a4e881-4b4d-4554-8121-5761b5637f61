# Generated by Django 2.2.28 on 2025-07-09 15:56

from django.db import migrations, models
import libs.mixins


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0007_auto_20250707_0937'),
    ]

    operations = [
        migrations.CreateModel(
            name='GPUDevice',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128, unique=True, verbose_name='GPU名称')),
                ('vendor', models.CharField(max_length=128, verbose_name='厂商')),
                ('description', models.TextField(blank=True, default='', verbose_name='描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': 'GPU',
                'verbose_name_plural': 'GPU管理',
                'db_table': 'model_storage_gpu_devices',
                'ordering': ('name',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
