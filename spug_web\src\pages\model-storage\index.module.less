/* 模型存储页面样式 */
.model-storage-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundMove 20s ease-in-out infinite;
    z-index: 0;
  }
  
  > * {
    position: relative;
    z-index: 1;
  }
}

@keyframes backgroundMove {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -30px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
}

/* 文件树样式 */
:global(.custom-tree) {
  .ant-tree-node-content-wrapper {
    transition: all 0.3s ease;
    border-radius: 8px;
    
    &:hover {
      background-color: rgba(24, 144, 255, 0.1) !important;
      transform: translateX(4px);
    }
  }
  
  .ant-tree-node-selected {
    .ant-tree-node-content-wrapper {
      background-color: rgba(24, 144, 255, 0.15) !important;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }
  }
  
  .ant-tree-switcher {
    transition: all 0.3s ease;
    
    &:hover {
      background-color: rgba(24, 144, 255, 0.1);
      border-radius: 4px;
    }
  }
}

/* 现代化表格样式 */
:global(.modern-table) {
  .ant-table-thead > tr > th {
    background: linear-gradient(135deg, #f6f8fa 0%, #e9ecef 100%) !important;
    border: none !important;
    font-weight: 600;
    color: #1f2937;
    padding: 16px 12px;
    
    &:first-child {
      border-radius: 12px 0 0 12px;
    }
    
    &:last-child {
      border-radius: 0 12px 12px 0;
    }
  }
  
  .ant-table-tbody > tr {
    transition: all 0.3s ease;
    
    &:hover {
      background-color: rgba(24, 144, 255, 0.05) !important;
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
    
    > td {
      border: none !important;
      padding: 16px 12px;
      border-bottom: 1px solid #f0f0f0;
    }
    
    &:last-child > td {
      border-bottom: none;
    }
  }
  
  .ant-table-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
}

/* 卡片动画效果 */
.glass-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
  }
}

/* 按钮动画效果 */
.gradient-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  
  &:hover, &:focus {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
}

/* 搜索框样式 */
.modern-search {
  .ant-input-group-addon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 0 12px 12px 0;
    
    .ant-btn {
      background: transparent;
      border: none;
      color: white;
      
      &:hover, &:focus {
        background: rgba(255, 255, 255, 0.2);
        color: white;
      }
    }
  }
  
  .ant-input {
    border-radius: 12px 0 0 12px;
    border-right: none;
    
    &:focus {
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .model-storage-container {
    padding: 12px;
  }
  
  .glass-card {
    margin-bottom: 16px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  
  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
}

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  
  // 全屏模式样式
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    padding: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 32px;
  font-weight: bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.card {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
  }

  .ant-card-head {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 16px 16px 0 0;
  }

  .ant-card-head-title {
    font-weight: 600;
    font-size: 16px;
  }
}

.metric {
  text-align: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-2px);
  }
}

.metricLabel {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.metricValue {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
  margin-top: 8px;
}

.timestamp {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin-top: 16px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
}

// Git风格文件对比容器
.fileCompareContainer {
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  background: white;
  
  .ant-list-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s;
    
    &:hover {
      background-color: #fafafa;
    }
    
    &:last-child {
      border-bottom: none;
    }
  }
}

// 文件列表项
.fileItem {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  
  &:hover {
    background: #f5f5f5;
  }
  
  &:last-child {
    border-bottom: none;
  }
  
  .ant-list-item-meta {
    align-items: flex-start;
  }
  
  .ant-list-item-meta-avatar {
    margin-right: 12px;
    margin-top: 4px;
  }
  
  .ant-list-item-meta-content {
    flex: 1;
  }
  
  .ant-list-item-meta-title {
    margin-bottom: 4px;
    font-size: 14px;
    line-height: 1.4;
  }
  
  .ant-list-item-meta-description {
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
    line-height: 1.3;
  }
  
  .ant-list-item-action {
    margin-left: 16px;
    
    li {
      padding: 0 4px;
    }
  }
}

// 文件状态图标
.fileStatusIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: #f0f0f0;
  color: #666;
  font-size: 14px;
}

// Git状态标签样式增强
.gitStatusTag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: none;
  
  .anticon {
    margin-right: 4px;
  }
  
  // 不同状态的样式
  &.synced {
    background: linear-gradient(135deg, #b7eb8f 0%, #52c41a 100%);
    color: white;
  }
  
  &.modified {
    background: linear-gradient(135deg, #ffd666 0%, #fa8c16 100%);
    color: white;
  }
  
  &.added {
    background: linear-gradient(135deg, #91d5ff 0%, #1890ff 100%);
    color: white;
  }
  
  &.deleted {
    background: linear-gradient(135deg, #ffadd2 0%, #f5222d 100%);
    color: white;
  }
  
  &.missing {
    background: linear-gradient(135deg, #ff7875 0%, #ff4d4f 100%);
    color: white;
  }
  
  &.conflict {
    background: linear-gradient(135deg, #d3adf7 0%, #722ed1 100%);
    color: white;
  }
}

// 文件树容器保持原样（兼容性）
.fileTreeContainer {
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  background: white;
}

.fileTree {
  .ant-tree-node-content-wrapper {
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.ant-tree-node-selected {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
    }
  }
  
  .ant-tree-title {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .status-tag {
      margin-left: auto;
    }
  }
  
  .ant-tree-iconEle {
    margin-right: 8px;
  }
  
  .ant-tree-indent-unit {
    width: 20px;
  }
}

.fullscreenTree {
  .ant-tree-node-content-wrapper {
    padding: 6px 12px;
    border-radius: 6px;
    margin: 2px 0;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.ant-tree-node-selected {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
    }
  }
  
  .ant-tree-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    
    .status-tag {
      margin-left: auto;
    }
    
    .file-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
      
      .file-name {
        font-weight: 500;
      }
      
      .file-path {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }
  
  .ant-tree-iconEle {
    margin-right: 12px;
    font-size: 16px;
  }
  
  .ant-tree-indent-unit {
    width: 24px;
  }
  
  .ant-tree-switcher {
    width: 24px;
    height: 28px;
    line-height: 28px;
  }
}

.treeTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  
  .ant-tag {
    margin-left: 8px;
  }
}

.statusTag {
  margin-left: 8px;
  border-radius: 12px;
  font-size: 12px;
  padding: 2px 8px;
}

// 全屏模式样式
.fullscreenDrawer {
  .ant-drawer-body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }
}

// 进度条自定义样式
.ant-progress-line {
  .ant-progress-bg {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  }
}

// 表格样式优化
.ant-table {
  .ant-table-thead > tr > th {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-weight: 600;
  }
  
  .ant-table-tbody > tr:hover > td {
    background: #e6f7ff;
  }
}

// 按钮样式优化
.ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  
  &:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }
}

// 标签样式优化
.ant-tag {
  border-radius: 12px;
  font-weight: 500;
  border: none;
  
  &.ant-tag-green {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
  }
  
  &.ant-tag-red {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
  }
  
  &.ant-tag-orange {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
  }
  
  &.ant-tag-blue {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
  }
  
  &.ant-tag-purple {
    background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
    color: white;
  }
}

// 列表组件样式优化
.ant-list {
  .ant-list-item {
    border-bottom: 1px solid #f0f0f0;
    
    &:hover {
      background: rgba(24, 144, 255, 0.05);
    }
  }
  
  .ant-pagination {
    margin-top: 16px;
    text-align: center;
  }
}

// 差异详情模态框样式
.diffModal {
  .ant-modal-body {
    background: #fafafa;
  }
  
  .diffContent {
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 16px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    max-height: 400px;
    overflow: auto;
  }
}

// Ant Design 组件样式覆盖
.ant-card-extra {
  .ant-space {
    flex-wrap: wrap;
  }
}

.ant-drawer-wrapper-body {
  .ant-drawer-body {
    padding: 0;
  }
}

// 自定义滚动条
.fileCompareContainer::-webkit-scrollbar,
.fileTreeContainer::-webkit-scrollbar {
  width: 6px;
}

.fileCompareContainer::-webkit-scrollbar-track,
.fileTreeContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.fileCompareContainer::-webkit-scrollbar-thumb,
.fileTreeContainer::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.fileCompareContainer::-webkit-scrollbar-thumb:hover,
.fileTreeContainer::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
} 

/* ======================================== */
/* ======== 现代化甘特图样式 ======== */
/* ======================================== */

.ganttContainer {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.02"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.02"/><circle cx="10" cy="90" r="1" fill="white" opacity="0.02"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    opacity: 0.3;
  }
}

.ganttHeader {
  display: flex;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 2px solid #e2e8f0;
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 2;
}

.ganttHeaderLeft {
  width: 200px;
  padding: 20px;
  text-align: center;
  border-right: 2px solid #e2e8f0;
  flex-shrink: 0;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  font-weight: 700;
  font-size: 16px;
  color: #1e293b;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ganttHeaderRight {
  flex-grow: 1;
  background: #ffffff;
}

.monthRow {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.monthCell {
  padding: 12px 8px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #475569;
  border-right: 1px solid #e2e8f0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  
  &:last-child {
    border-right: none;
  }
}

.dateRow {
  display: flex;
  background: #ffffff;
}

.dateCell {
  padding: 10px 4px;
  text-align: center;
  font-size: 13px;
  font-weight: 500;
  color: #64748b;
  border-right: 1px solid #f1f5f9;
  transition: all 0.2s ease;
  
  &:last-child {
    border-right: none;
  }
  
  &:hover {
    background-color: #f8fafc;
    color: #1e293b;
  }
  
  &.weekend {
    background-color: #fef3c7;
    color: #92400e;
    font-weight: 600;
  }
  
  &.todayHeader {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;
    font-weight: 700;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  }
}

.ganttRow {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.ganttRowHeader {
  width: 200px;
  padding: 20px;
  text-align: center;
  border-right: 2px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(5px);
  
  strong {
    font-size: 15px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

.ganttRowContent {
  flex-grow: 1;
  position: relative;
  background: rgba(255, 255, 255, 0.03);
}

.dateGrid {
  position: absolute;
  height: 100%;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  box-sizing: border-box;
  transition: all 0.2s ease;
  
  &.today {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(37, 99, 235, 0.1) 100%);
    border-right: 2px solid rgba(59, 130, 246, 0.6);
    box-shadow: inset 0 0 10px rgba(59, 130, 246, 0.1);
  }
  
  &:last-child {
    border-right: none;
  }
}

.taskBar {
  position: absolute;
  height: 32px;
  border-radius: 12px;
  color: white;
  display: flex;
  align-items: center;
  padding: 0 14px;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);

  // 增强任务条的立体感
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 11px;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%,
      rgba(0, 0, 0, 0.05) 100%
    );
    pointer-events: none;
    z-index: 1;
  }

  &:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.2),
      0 4px 12px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    border-color: rgba(0, 0, 0, 0.25);
  }

  &:active {
    transform: translateY(-1px) scale(1.01);
  }
  
  // 原有的状态样式保持不变
  &.status-pending {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
  }
  
  &.status-in-progress {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }
  
  &.status-completed {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }
  
  &.status-cancelled {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
  }
  
  &.status-blocked {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  }
  
  &.status-delayed {
    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
  }
  
  // 新增的风险样式
  &.overdueTask {
    animation: pulse 2s infinite;
    border: 2px solid #ef4444 !important;
    
    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(45deg, transparent, rgba(239, 68, 68, 0.3), transparent);
      border-radius: 12px;
      z-index: -1;
    }
  }
  
  &.urgentTask {
    border: 2px solid #f97316 !important;
    box-shadow: 0 0 12px rgba(249, 115, 22, 0.5);
  }
}

.taskProgressBar {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  border-radius: 11px;
  background: linear-gradient(90deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.25) 50%,
    rgba(0, 0, 0, 0.15) 100%
  );
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 0;
  box-shadow:
    inset 0 1px 2px rgba(0, 0, 0, 0.2),
    inset 0 -1px 1px rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.2);

  &::before {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    height: 40%;
    background: linear-gradient(90deg,
      rgba(255, 255, 255, 0.4) 0%,
      rgba(255, 255, 255, 0.6) 25%,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(255, 255, 255, 0.1) 75%,
      transparent 100%
    );
    background-size: 200% 100%;
    border-radius: 10px 10px 0 0;
    pointer-events: none;
    animation: progressShimmer 2s ease-in-out infinite;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom,
      rgba(255, 255, 255, 0.6) 0%,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    border-radius: 0 11px 11px 0;
    box-shadow: 1px 0 2px rgba(0, 0, 0, 0.2);
  }

  // 根据进度显示不同的视觉效果
  &[data-progress="100"] {
    background: linear-gradient(90deg,
      rgba(34, 197, 94, 0.4) 0%,
      rgba(34, 197, 94, 0.25) 50%,
      rgba(34, 197, 94, 0.15) 100%
    );
    border-color: rgba(34, 197, 94, 0.3);

    &::after {
      background: linear-gradient(to bottom,
        rgba(34, 197, 94, 0.8) 0%,
        rgba(34, 197, 94, 0.5) 50%,
        rgba(34, 197, 94, 0.3) 100%
      );
    }
  }

  &[data-progress^="0"] {
    background: linear-gradient(90deg,
      rgba(239, 68, 68, 0.3) 0%,
      rgba(239, 68, 68, 0.2) 50%,
      rgba(239, 68, 68, 0.1) 100%
    );
    border-color: rgba(239, 68, 68, 0.2);

    &::after {
      background: linear-gradient(to bottom,
        rgba(239, 68, 68, 0.6) 0%,
        rgba(239, 68, 68, 0.4) 50%,
        rgba(239, 68, 68, 0.2) 100%
      );
    }
  }
}

.taskName {
  position: relative;
  z-index: 2;
  text-shadow:
    0 1px 2px rgba(0, 0, 0, 0.4),
    0 0 4px rgba(0, 0, 0, 0.2);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  color: white;

  .taskIcon {
    font-size: 12px;
    opacity: 0.9;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.3));
  }
}

/* 优先级指示器 */
.priorityIndicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  
  &.priority-p1 {
    background: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.5);
  }
  
  &.priority-p2 {
    background: #f59e0b;
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.5);
  }
  
  &.priority-p3 {
    background: #10b981;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
  }
}

/* 统计信息卡片 */
.statsCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .statsTitle {
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    text-align: center;
  }
  
  .statsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
  }
  
  .statItem {
    text-align: center;
    padding: 8px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    
    .statValue {
      display: block;
      font-size: 24px;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 4px;
    }
    
    .statLabel {
      font-size: 12px;
      color: #6b7280;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .ganttHeaderLeft,
  .ganttRowHeader {
    width: 160px;
  }
  
  .taskBar {
    font-size: 12px;
    padding: 0 10px;
  }
}

@media (max-width: 768px) {
  .ganttHeaderLeft,
  .ganttRowHeader {
    width: 120px;
    padding: 12px;
  }
  
  .taskBar {
    height: 28px;
    font-size: 11px;
    padding: 0 8px;
  }
  
  .monthCell {
    font-size: 12px;
    padding: 8px 4px;
  }
  
  .dateCell {
    font-size: 11px;
    padding: 6px 2px;
  }
}

/* 动画效果 */
@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes progressPulse {
  0% {
    box-shadow:
      inset 0 1px 2px rgba(0, 0, 0, 0.2),
      inset 0 -1px 1px rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow:
      inset 0 1px 2px rgba(0, 0, 0, 0.3),
      inset 0 -1px 1px rgba(255, 255, 255, 0.2),
      0 0 8px rgba(255, 255, 255, 0.3);
  }
  100% {
    box-shadow:
      inset 0 1px 2px rgba(0, 0, 0, 0.2),
      inset 0 -1px 1px rgba(255, 255, 255, 0.1);
  }
}

@keyframes progressShimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

.ganttRow {
  animation: slideInFromLeft 0.5s ease-out;
}

.taskBar {
  animation: fadeInUp 0.6s ease-out;
}

/* 自定义滚动条 */
.ganttContainer::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ganttContainer::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.ganttContainer::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.ganttContainer::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
} 

/* 风险提示系统 */
.riskAlert {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .riskHeader {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    
    .riskIcon {
      font-size: 20px;
    }
    
    .riskTitle {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
  }
  
  .riskContent {
    color: #4b5563;
    font-size: 14px;
    line-height: 1.5;
  }
  
  .riskItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
    
    &:hover {
      background: #f1f5f9;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .riskCount {
      font-size: 24px;
      font-weight: 700;
      color: #1f2937;
      text-shadow: none;
    }
    
    .riskLabel {
      font-size: 12px;
      color: #6b7280;
      text-align: center;
      font-weight: 500;
    }
  }
  
  &.risk-high {
    border-color: #fecaca;
    background: #fef2f2;
    
    .riskHeader .riskTitle {
      color: #dc2626;
    }
    
    .riskContent {
      color: #991b1b;
    }
    
    .riskItem {
      background: #fee2e2;
      border-color: #fca5a5;
      
      .riskCount {
        color: #dc2626;
      }
      
      .riskLabel {
        color: #b91c1c;
      }
    }
  }
  
  &.risk-medium {
    border-color: #fed7aa;
    background: #fff7ed;
    
    .riskHeader .riskTitle {
      color: #ea580c;
    }
    
    .riskContent {
      color: #c2410c;
    }
    
    .riskItem {
      background: #ffedd5;
      border-color: #fdba74;
      
      .riskCount {
        color: #ea580c;
      }
      
      .riskLabel {
        color: #c2410c;
      }
    }
  }
  
  &.risk-low {
    border-color: #fde68a;
    background: #fefce8;
    
    .riskHeader .riskTitle {
      color: #ca8a04;
    }
    
    .riskContent {
      color: #a16207;
    }
    
    .riskItem {
      background: #fef3c7;
      border-color: #fcd34d;
      
      .riskCount {
        color: #ca8a04;
      }
      
      .riskLabel {
        color: #a16207;
      }
    }
  }
}

/* 测试人员颜色指示器 */
.testerColorIndicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 风险指示器 */
.riskIndicator {
  display: inline-block;
  margin-left: 8px;
  width: 18px;
  height: 18px;
  background: #ef4444;
  color: #ffffff;
  border-radius: 50%;
  text-align: center;
  line-height: 18px;
  font-size: 10px;
  font-weight: bold;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
  vertical-align: middle;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: pulse 2s ease-in-out infinite;
  
  &:hover {
    background-color: #dc2626;
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.6);
  }
}

/* Tooltip样式优化 */
.taskTooltipOverlay {
  .ant-tooltip-inner {
    background: rgba(30, 41, 59, 0.95) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
  
  .ant-tooltip-arrow::before {
    background: rgba(30, 41, 59, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

.taskTooltip {
  .taskTitle {
    font-weight: 700;
    font-size: 16px;
    margin-bottom: 8px;
    color: #ffffff !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 4px;
    display: flex;
    align-items: center;
    gap: 6px;
  }
  
  > div {
    margin: 6px 0;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.9) !important;
    
    &:not(.taskTitle) {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      strong {
        color: rgba(255, 255, 255, 0.7) !important;
        font-weight: 500;
      }
      
      span {
        color: #ffffff !important;
        font-weight: 600;
      }
    }
  }
}

/* 图例样式优化 */
.legend {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  margin-bottom: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .legendSection {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    
    .legendSectionTitle {
      color: #1f2937;
      font-weight: 600;
      font-size: 14px;
      margin-right: 8px;
    }
  }
  
  .legendItem {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #4b5563;
    font-size: 13px;
    font-weight: 500;
    padding: 4px 8px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
    
    &:hover {
      background: #f1f5f9;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .legendColor {
      width: 14px;
      height: 14px;
      border-radius: 3px;
      border: 1px solid rgba(0, 0, 0, 0.2);
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
} 

/* 资料输出圆球脉动动画 */
@keyframes documentPulse {
  0% {
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.6);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
    transform: scale(1);
  }
} 

/* 资料输出圆球样式类 */
.documentOutputCircle {
  display: inline-block;
  margin-left: 8px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  font-size: 10px;
  font-weight: bold;
  text-align: center;
  line-height: 18px;
  vertical-align: middle;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
  animation: documentPulse 2s ease-in-out infinite;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #059669;
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.6);
  }
}