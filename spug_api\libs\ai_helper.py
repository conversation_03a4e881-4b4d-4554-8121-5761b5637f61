# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.

import logging
import json
import hashlib
import time
import re
from typing import Optional, Dict, Any, Union
from openai import OpenAI
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)


class AIClient:
    """简化的AI客户端"""

    def __init__(self):
        self.client = None
        self.failure_count = 0
        self.last_failure_time = None
        self.max_failures = 3
        self.recovery_timeout = 300  # 5分钟
        self._init_client()

    def _init_client(self):
        """初始化客户端"""
        try:
            self.client = OpenAI(
                api_key=getattr(settings, 'OPENAI_API_KEY', 'ms-4038de08-d62e-4f6c-a481-e6ef3b2b4232'),
                base_url=getattr(settings, 'OPENAI_BASE_URL', 'https://api-inference.modelscope.cn/v1/')
            )
            logger.info("AI客户端初始化成功")
        except Exception as e:
            logger.error(f"初始化AI客户端失败: {str(e)}")
            self.client = None

    def is_available(self) -> bool:
        """检查服务是否可用"""
        if not self.client:
            return False

        # 检查是否在熔断状态
        if self.failure_count >= self.max_failures:
            if self.last_failure_time and (time.time() - self.last_failure_time) > self.recovery_timeout:
                # 尝试恢复
                self.failure_count = 0
                self.last_failure_time = None
                return True
            return False

        return True

    def _record_success(self):
        """记录成功调用"""
        self.failure_count = 0
        self.last_failure_time = None

    def _record_failure(self):
        """记录失败调用"""
        self.failure_count += 1
        self.last_failure_time = time.time()

    def chat_completion(self, messages: list, **kwargs) -> Optional[str]:
        """执行聊天完成请求"""
        if not self.is_available():
            logger.warning("AI服务不可用")
            return None

        try:
            response = self.client.chat.completions.create(
                messages=messages,
                **kwargs
            )
            self._record_success()
            return response.choices[0].message.content
        except Exception as e:
            self._record_failure()
            logger.error(f"AI请求失败: {str(e)}")
            return None


class AIHelper:
    """简化的AI助手工具类"""

    def __init__(self):
        """初始化AI助手"""
        self.client = AIClient()
        self.cache_prefix = 'ai_helper:'
        self.default_cache_timeout = 3600  # 1小时缓存

    def is_available(self) -> bool:
        """检查AI服务是否可用"""
        return self.client.is_available()

    def _build_cache_key(self, prompt: str, **kwargs) -> str:
        """构建缓存键"""
        cache_data = {
            'prompt': prompt,
            'kwargs': {k: v for k, v in kwargs.items() if k not in ['use_cache', 'cache_timeout']}
        }
        cache_str = json.dumps(cache_data, sort_keys=True, ensure_ascii=False)
        return f"{self.cache_prefix}{hashlib.md5(cache_str.encode()).hexdigest()}"

    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """从缓存获取结果"""
        try:
            return cache.get(cache_key)
        except Exception as e:
            logger.warning(f"缓存读取失败: {str(e)}")
            return None

    def _save_to_cache(self, cache_key: str, value: Any, timeout: int = None) -> None:
        """保存结果到缓存"""
        try:
            timeout = timeout or self.default_cache_timeout
            cache.set(cache_key, value, timeout)
        except Exception as e:
            logger.warning(f"缓存写入失败: {str(e)}")

    def _clean_json_response(self, text: str) -> str:
        """清理JSON响应文本"""
        if not text:
            return text

        # 移除markdown代码块标记
        text = re.sub(r'^```json\s*|\s*```$', '', text.strip(), flags=re.MULTILINE)
        text = re.sub(r'^```\s*|\s*```$', '', text.strip(), flags=re.MULTILINE)

        # 提取JSON对象
        json_match = re.search(r'\{.*\}', text, re.DOTALL)
        if json_match:
            text = json_match.group(0)

        return text.strip()
    
    def chat(
        self,
        prompt: str,
        system_message: str = "You are a helpful assistant. Please respond in Chinese.",
        model: str = "Qwen/Qwen3-Coder-480B-A35B-Instruct",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        use_cache: bool = True,
        cache_timeout: Optional[int] = None
    ) -> Optional[str]:
        """
        基础聊天方法

        Args:
            prompt: 用户提示词
            system_message: 系统消息
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            use_cache: 是否使用缓存
            cache_timeout: 缓存超时时间（秒）

        Returns:
            AI回复的文本
        """
        if not self.is_available():
            logger.warning("AI服务不可用")
            return None

        # 尝试从缓存获取结果
        cache_key = None
        if use_cache:
            cache_key = self._build_cache_key(
                prompt,
                system_message=system_message,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens
            )
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.debug("使用缓存的AI响应")
                return cached_result

        # 构建请求参数
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": prompt}
        ]

        kwargs = {
            "model": model,
            "temperature": temperature
        }

        if max_tokens:
            kwargs["max_tokens"] = max_tokens

        # 执行请求
        result = self.client.chat_completion(messages, **kwargs)

        # 缓存结果
        if result and use_cache and cache_key:
            self._save_to_cache(cache_key, result, cache_timeout)

        return result
    
    def json(
        self,
        prompt: str,
        schema: Optional[Dict[str, Any]] = None,
        system_message: str = "You are a helpful assistant that responds with valid JSON. Please respond in Chinese.",
        model: str = "Qwen/Qwen3-Coder-480B-A35B-Instruct",
        temperature: float = 0.3,
        max_tokens: Optional[int] = None,
        use_cache: bool = True,
        cache_timeout: Optional[int] = None,
        max_retries: int = 2
    ) -> Optional[Dict[str, Any]]:
        """
        JSON格式输出方法

        Args:
            prompt: 用户提示词
            schema: JSON schema定义（可选）
            system_message: 系统消息
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            use_cache: 是否使用缓存
            cache_timeout: 缓存超时时间（秒）
            max_retries: 最大重试次数

        Returns:
            解析后的JSON对象或None
        """
        # 构建JSON输出的系统消息
        json_system_message = system_message + "\n\n重要：请严格按照JSON格式回复，不要添加任何解释文字。"

        # 如果提供了schema，添加到提示词中
        if schema:
            schema_text = json.dumps(schema, ensure_ascii=False, indent=2)
            json_system_message += f"\n\n请按照以下JSON结构回复：\n{schema_text}"

        # 强调JSON格式要求
        json_prompt = prompt + "\n\n请以有效的JSON格式回复。"

        # 重试逻辑
        for attempt in range(max_retries):
            result = self.chat(
                prompt=json_prompt,
                system_message=json_system_message,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                use_cache=use_cache,
                cache_timeout=cache_timeout
            )

            if not result:
                continue

            # 清理和解析JSON
            try:
                cleaned_result = self._clean_json_response(result)
                json_result = json.loads(cleaned_result)
                return json_result
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt == max_retries - 1:
                    logger.error(f"原始响应: {result}")

        return None
    
    def get_status(self) -> Dict[str, Any]:
        """获取AI服务状态"""
        return {
            'available': self.is_available(),
            'failure_count': self.client.failure_count,
            'last_failure_time': self.client.last_failure_time
        }

    def update_config(self, config: Dict[str, Any]):
        """更新AI配置"""
        try:
            # 更新客户端配置
            if 'api_base' in config:
                os.environ['AI_API_BASE'] = config['api_base']
            if 'api_key' in config:
                os.environ['AI_API_KEY'] = config['api_key']
            if 'model' in config:
                os.environ['AI_MODEL'] = config['model']
            if 'max_tokens' in config:
                os.environ['AI_MAX_TOKENS'] = str(config['max_tokens'])
            if 'temperature' in config:
                os.environ['AI_TEMPERATURE'] = str(config['temperature'])

            # 重新初始化客户端
            self.client._init_client()
            logger.info("AI配置更新成功")
        except Exception as e:
            logger.error(f"更新AI配置失败: {str(e)}")
    

# 创建全局AI助手实例
ai_helper = AIHelper()

# 便捷的AI类，提供静态方法
class AI:
    @staticmethod
    def chat(prompt: str, system_message: str = None, **kwargs) -> Optional[str]:
        """聊天接口"""
        return ai_helper.chat(prompt, system_message, **kwargs)

    @staticmethod
    def json(prompt: str, schema: Optional[Dict] = None, **kwargs) -> Optional[Dict]:
        """JSON接口"""
        return ai_helper.json(prompt, schema, **kwargs)

    @staticmethod
    def status() -> Dict[str, Any]:
        """获取AI服务状态"""
        return ai_helper.get_status()

    @staticmethod
    def is_available() -> bool:
        """检查AI服务是否可用"""
        return ai_helper.is_available()

    @staticmethod
    def analyze_log(content: str, filename: str = "log.txt") -> Optional[Dict]:
        """分析日志内容"""
        prompt = f"""
请分析以下日志内容，并提供结构化的分析结果：

文件名: {filename}
日志内容:
{content}

请按照以下JSON格式返回分析结果：
{{
    "performance_assessment": {{
        "score": 85,
        "level": "良好",
        "summary": "系统整体运行正常，但存在少量异常"
    }},
    "anomalies": [
        {{
            "type": "连接超时",
            "description": "数据库连接超时",
            "severity": "中",
            "time_range": "12:30:17"
        }}
    ],
    "recommendations": [
        {{
            "category": "性能优化",
            "description": "建议增加数据库连接池大小",
            "priority": "中",
            "impact": "提升系统稳定性"
        }}
    ],
    "insights": [
        "系统启动正常",
        "存在间歇性数据库连接问题"
    ]
}}
"""

        schema = {
            "type": "object",
            "properties": {
                "performance_assessment": {
                    "type": "object",
                    "properties": {
                        "score": {"type": "integer", "minimum": 0, "maximum": 100},
                        "level": {"type": "string"},
                        "summary": {"type": "string"}
                    }
                },
                "anomalies": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "string"},
                            "description": {"type": "string"},
                            "severity": {"type": "string"},
                            "time_range": {"type": "string"}
                        }
                    }
                },
                "recommendations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "category": {"type": "string"},
                            "description": {"type": "string"},
                            "priority": {"type": "string"},
                            "impact": {"type": "string"}
                        }
                    }
                },
                "insights": {
                    "type": "array",
                    "items": {"type": "string"}
                }
            }
        }

        return ai_helper.json(prompt, schema)

    @staticmethod
    def analyze_benchmark(content: str, filename: str = "benchmark.txt") -> Optional[Dict]:
        """分析基准测试结果"""
        prompt = f"""
请分析以下基准测试结果，并提供性能评估和优化建议：

文件名: {filename}
测试数据:
{content}

请按照以下JSON格式返回分析结果：
{{
    "performance_assessment": {{
        "score": 75,
        "level": "中等",
        "summary": "性能表现中等，有优化空间"
    }},
    "anomalies": [
        {{
            "type": "响应时间异常",
            "description": "P99响应时间过高",
            "severity": "中",
            "time_range": "整个测试期间"
        }}
    ],
    "recommendations": [
        {{
            "category": "性能调优",
            "description": "优化模型推理速度",
            "priority": "高",
            "impact": "显著提升响应速度"
        }}
    ],
    "insights": [
        "吞吐量表现良好",
        "响应时间需要优化"
    ]
}}
"""

        schema = {
            "type": "object",
            "properties": {
                "performance_assessment": {
                    "type": "object",
                    "properties": {
                        "score": {"type": "integer", "minimum": 0, "maximum": 100},
                        "level": {"type": "string"},
                        "summary": {"type": "string"}
                    }
                },
                "anomalies": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "string"},
                            "description": {"type": "string"},
                            "severity": {"type": "string"},
                            "time_range": {"type": "string"}
                        }
                    }
                },
                "recommendations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "category": {"type": "string"},
                            "description": {"type": "string"},
                            "priority": {"type": "string"},
                            "impact": {"type": "string"}
                        }
                    }
                },
                "insights": {
                    "type": "array",
                    "items": {"type": "string"}
                }
            }
        }

        return ai_helper.json(prompt, schema)

# 兼容性函数
def get_ai_response(prompt: str, **kwargs) -> Optional[str]:
    """兼容性函数：获取AI响应"""
    return AI.chat(prompt, **kwargs)

def check_ai_service() -> bool:
    """兼容性函数：检查AI服务"""
    return AI.is_available()

def get_service_status() -> Dict[str, Any]:
    """兼容性函数：获取服务状态"""
    return AI.status()