import React, { useState, useMemo } from 'react';
import { Modal, Card, Row, Col, Select, Tabs, Empty, Statistic, Progress, Tag, Space, Button, Tooltip as AntTooltip, Badge } from 'antd';
import { <PERSON><PERSON>hart, Line, <PERSON><PERSON>hart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, AreaChart, Area, RadialBarChart, RadialBar } from 'recharts';
import { ArrowUpOutlined, ArrowDownOutlined, Bar<PERSON>hartOutlined, PieChartOutlined, LineChartOutlined, DashboardOutlined, ThunderboltOutlined, RocketOutlined, BugOutlined, CheckCircleOutlined } from '@ant-design/icons';
import store from './store';
import styles from './ChartView.module.less';

const { TabPane } = Tabs;

export default function ChartView({ visible, onCancel, data }) {
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('7d');

  // 现代化颜色配置
  const COLORS = {
    primary: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#13c2c2',
    purple: '#722ed1',
    gradient: ['#1890ff', '#36cfc9', '#52c41a', '#faad14', '#ff7a45', '#f759ab'],
    chart: ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E86452', '#6DC8EC', '#945FB9', '#FF9845']
  };

  // 计算统计数据
  const statistics = useMemo(() => {
    if (!data || data.length === 0) return null;

    const totalResults = data.length;
    const avgSuccess = store.avgSuccessRate || 0;
    const totalConfirmed = store.totalConfirmedMetrics || 0;
    const planCount = store.resultsByPlan ? store.resultsByPlan.length : 0;
    const gpuCount = store.resultsByGpu ? store.resultsByGpu.length : 0;

    // 计算趋势
    const recentData = store.timeSeriesData ? store.timeSeriesData.slice(-7) : [];
    const trend = recentData.length >= 2 ?
      recentData[recentData.length - 1].avgSuccessRate - recentData[0].avgSuccessRate : 0;

    return {
      totalResults,
      avgSuccess,
      totalConfirmed,
      planCount,
      gpuCount,
      trend,
      recentData
    };
  }, [data]);

  // 趋势分析数据
  const getTrendData = () => {
    return store.timeSeriesData || [];
  };

  // 按计划分组数据
  const getPlanData = () => {
    return store.resultsByPlan ? store.resultsByPlan.slice(0, 10) : [];
  };

  // 按GPU分组数据
  const getGpuData = () => {
    return store.resultsByGpu ? store.resultsByGpu.slice(0, 10) : [];
  };

  // 指标分布数据
  const getMetricsDistribution = () => {
    if (!data || data.length === 0) return [];

    const distribution = {};
    data.forEach(result => {
      const rate = result.success_rate || 0;
      let range;
      if (rate >= 90) range = '90-100%';
      else if (rate >= 80) range = '80-90%';
      else if (rate >= 60) range = '60-80%';
      else if (rate >= 40) range = '40-60%';
      else range = '0-40%';

      distribution[range] = (distribution[range] || 0) + 1;
    });

    return Object.entries(distribution).map(([range, count]) => ({
      range,
      count,
      percentage: ((count / data.length) * 100).toFixed(1)
    }));
  };

  // 现代化概览仪表板
  const renderOverviewDashboard = () => {
    if (!statistics) {
      return <Empty description="暂无数据" />;
    }

    const { totalResults, avgSuccess, totalConfirmed, planCount, gpuCount, trend } = statistics;

    return (
      <div className={styles.dashboardContainer}>
        {/* 核心指标卡片 */}
        <Row gutter={[16, 16]} className={styles.metricsRow}>
          <Col span={8}>
            <Card className={styles.metricCard} bodyStyle={{ padding: '20px' }}>
              <div className={styles.metricContent}>
                <div className={styles.metricIcon} style={{ backgroundColor: COLORS.primary }}>
                  <BarChartOutlined style={{ fontSize: '24px', color: '#fff' }} />
                </div>
                <div className={styles.metricInfo}>
                  <div className={styles.metricValue}>{totalResults}</div>
                  <div className={styles.metricLabel}>总测试结果</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card className={styles.metricCard} bodyStyle={{ padding: '20px' }}>
              <div className={styles.metricContent}>
                <div className={styles.metricIcon} style={{ backgroundColor: COLORS.success }}>
                  <CheckCircleOutlined style={{ fontSize: '24px', color: '#fff' }} />
                </div>
                <div className={styles.metricInfo}>
                  <div className={styles.metricValue}>{avgSuccess.toFixed(1)}%</div>
                  <div className={styles.metricLabel}>
                    平均成功率
                    {trend !== 0 && (
                      <span className={styles.trendIndicator} style={{ color: trend > 0 ? COLORS.success : COLORS.error }}>
                        {trend > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                        {Math.abs(trend).toFixed(1)}%
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card className={styles.metricCard} bodyStyle={{ padding: '20px' }}>
              <div className={styles.metricContent}>
                <div className={styles.metricIcon} style={{ backgroundColor: COLORS.purple }}>
                  <RocketOutlined style={{ fontSize: '24px', color: '#fff' }} />
                </div>
                <div className={styles.metricInfo}>
                  <div className={styles.metricValue}>{totalConfirmed}</div>
                  <div className={styles.metricLabel}>已确认指标</div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 进度环形图和分布统计 */}
        <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
          <Col span={12}>
            <Card title="性能评级分布" className={styles.chartCard}>
              <div className={styles.progressContainer}>
                <div className={styles.progressItem}>
                  <div className={styles.progressLabel}>
                    <Badge color={COLORS.success} text="优秀 (≥80%)" />
                  </div>
                  <Progress
                    percent={avgSuccess >= 80 ? 100 : 0}
                    strokeColor={COLORS.success}
                    showInfo={false}
                  />
                </div>
                <div className={styles.progressItem}>
                  <div className={styles.progressLabel}>
                    <Badge color={COLORS.warning} text="良好 (60-80%)" />
                  </div>
                  <Progress
                    percent={avgSuccess >= 60 && avgSuccess < 80 ? 100 : 0}
                    strokeColor={COLORS.warning}
                    showInfo={false}
                  />
                </div>
                <div className={styles.progressItem}>
                  <div className={styles.progressLabel}>
                    <Badge color={COLORS.error} text="需改进 (<60%)" />
                  </div>
                  <Progress
                    percent={avgSuccess < 60 ? 100 : 0}
                    strokeColor={COLORS.error}
                    showInfo={false}
                  />
                </div>
              </div>
            </Card>
          </Col>
          <Col span={12}>
            <Card title="覆盖范围统计" className={styles.chartCard}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="测试计划"
                    value={planCount}
                    prefix={<PieChartOutlined style={{ color: COLORS.primary }} />}
                    valueStyle={{ color: COLORS.primary }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="GPU设备"
                    value={gpuCount}
                    prefix={<DashboardOutlined style={{ color: COLORS.info }} />}
                    valueStyle={{ color: COLORS.info }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="确认率"
                    value={totalResults > 0 ? ((totalConfirmed / totalResults) * 100).toFixed(1) : 0}
                    suffix="%"
                    prefix={<CheckCircleOutlined style={{ color: COLORS.success }} />}
                    valueStyle={{ color: COLORS.success }}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className={styles.customTooltip}>
          <p className={styles.tooltipLabel}>{label}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {entry.value}
              {entry.name.includes('率') ? '%' : ''}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // 现代化趋势图表
  const renderTrendChart = () => {
    const trendData = getTrendData();
    if (trendData.length === 0) {
      return <Empty description="暂无趋势数据" />;
    }

    return (
      <div className={styles.trendContainer}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card
              title={
                <Space>
                  <LineChartOutlined style={{ color: COLORS.primary }} />
                  <span>性能趋势分析</span>
                  <Tag color="blue">{trendData.length} 个数据点</Tag>
                </Space>
              }
              extra={
                <Select
                  value={timeRange}
                  onChange={setTimeRange}
                  style={{ width: 120 }}
                  options={[
                    { label: '最近7天', value: '7d' },
                    { label: '最近30天', value: '30d' },
                    { label: '全部', value: 'all' }
                  ]}
                />
              }
              className={styles.chartCard}
            >
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={trendData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                  <defs>
                    <linearGradient id="successGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor={COLORS.primary} stopOpacity={0.8}/>
                      <stop offset="95%" stopColor={COLORS.primary} stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis
                    dataKey="date"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#666' }}
                  />
                  <YAxis
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#666' }}
                    domain={[0, 100]}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="avgSuccessRate"
                    stroke={COLORS.primary}
                    fillOpacity={1}
                    fill="url(#successGradient)"
                    name="平均成功率"
                    strokeWidth={3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 现代化计划对比图表
  const renderPlanChart = () => {
    const planData = getPlanData();
    if (planData.length === 0) {
      return <Empty description="暂无计划数据" />;
    }

    return (
      <div className={styles.planContainer}>
        <Row gutter={[16, 16]}>
          <Col span={16}>
            <Card
              title={
                <Space>
                  <BarChartOutlined style={{ color: COLORS.primary }} />
                  <span>测试计划性能对比</span>
                  <Tag color="green">{planData.length} 个计划</Tag>
                </Space>
              }
              className={styles.chartCard}
            >
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={planData} margin={{ top: 20, right: 30, left: 20, bottom: 80 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis
                    dataKey="planName"
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    tick={{ fontSize: 12, fill: '#666' }}
                    axisLine={false}
                    tickLine={false}
                  />
                  <YAxis
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#666' }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Bar
                    dataKey="avgSuccessRate"
                    fill={COLORS.primary}
                    name="平均成功率"
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar
                    dataKey="count"
                    fill={COLORS.success}
                    name="执行次数"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </Card>
          </Col>
          <Col span={8}>
            <Card title="计划排行榜" className={styles.chartCard}>
              <div className={styles.rankingList}>
                {planData.slice(0, 5).map((plan, index) => (
                  <div key={plan.planName} className={styles.rankingItem}>
                    <div className={styles.rankingNumber}>
                      <Badge
                        count={index + 1}
                        style={{
                          backgroundColor: index < 3 ? COLORS.chart[index] : COLORS.chart[3]
                        }}
                      />
                    </div>
                    <div className={styles.rankingInfo}>
                      <div className={styles.rankingName}>{plan.planName}</div>
                      <div className={styles.rankingValue}>
                        成功率: {plan.avgSuccessRate}% | 执行: {plan.count}次
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // GPU对比图表
  const renderGpuChart = () => {
    const gpuData = getGpuData();
    if (gpuData.length === 0) {
      return <Empty description="暂无GPU数据" />;
    }

    return (
      <Card title="GPU性能对比">
        <ResponsiveContainer width="100%" height={400}>
          <BarChart data={gpuData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="gpuName" angle={-45} textAnchor="end" height={100} />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar dataKey="avgSuccessRate" fill="#0088FE" name="平均成功率" />
            <Bar dataKey="confirmedMetrics" fill="#00C49F" name="确认指标数" />
          </BarChart>
        </ResponsiveContainer>
      </Card>
    );
  };

  // 指标分布饼图
  const renderMetricsChart = () => {
    const distributionData = getMetricsDistribution();
    if (distributionData.length === 0) {
      return <Empty description="暂无分布数据" />;
    }

    return (
      <Row gutter={16}>
        <Col span={12}>
          <Card title="成功率分布">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={distributionData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ range, percentage }) => `${range} (${percentage}%)`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {distributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="成功率统计">
            <div className={styles.statsContainer}>
              {distributionData.map((item, index) => (
                <div key={index} className={styles.statItem}>
                  <div 
                    className={styles.colorIndicator}
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  />
                  <span className={styles.statLabel}>{item.range}</span>
                  <span className={styles.statValue}>{item.count} 个</span>
                  <span className={styles.statPercentage}>({item.percentage}%)</span>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>
    );
  };

  // 综合报告
  const renderSummaryReport = () => {
    const totalResults = data ? data.length : 0;
    const avgSuccess = store.avgSuccessRate || 0;
    const totalConfirmed = store.totalConfirmedMetrics || 0;
    const planCount = store.resultsByPlan ? store.resultsByPlan.length : 0;
    const gpuCount = store.resultsByGpu ? store.resultsByGpu.length : 0;

    return (
      <Card title="综合分析报告">
        <div className={styles.reportContent}>
          <Row gutter={16}>
            <Col span={8}>
              <div className={styles.reportSection}>
                <h4>数据概览</h4>
                <ul>
                  <li>总测试结果: <strong>{totalResults}</strong> 个</li>
                  <li>覆盖测试计划: <strong>{planCount}</strong> 个</li>
                  <li>涉及GPU: <strong>{gpuCount}</strong> 个</li>
                  <li>已确认指标: <strong>{totalConfirmed}</strong> 个</li>
                </ul>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles.reportSection}>
                <h4>性能指标</h4>
                <ul>
                  <li>平均成功率: <strong>{avgSuccess}%</strong></li>
                  <li>确认率: <strong>{totalResults > 0 ? ((totalConfirmed / totalResults) * 100).toFixed(1) : 0}%</strong></li>
                  <li>
                    整体评级:
                    <strong style={{
                      color: avgSuccess >= 80 ? '#52c41a' : avgSuccess >= 60 ? '#faad14' : '#ff4d4f'
                    }}>
                      {avgSuccess >= 80 ? '优秀' : avgSuccess >= 60 ? '良好' : '需改进'}
                    </strong>
                  </li>
                </ul>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles.reportSection}>
                <h4>建议</h4>
                <ul>
                  {avgSuccess < 60 && <li>建议优化测试流程，提高成功率</li>}
                  {planCount > 10 && <li>测试计划较多，建议分类管理</li>}
                  {totalResults > 0 && (totalConfirmed / totalResults) < 0.8 &&
                    <li>建议加强指标确认工作</li>}
                  {gpuCount < 3 && <li>建议增加GPU测试覆盖范围</li>}
                </ul>
              </div>
            </Col>
          </Row>
        </div>
      </Card>
    );
  };

  if (!data || data.length === 0) {
    return (
      <Modal
        title="图表分析"
        visible={visible}
        onCancel={onCancel}
        width={1200}
        footer={null}
      >
        <Empty description="暂无数据可用于分析" />
      </Modal>
    );
  }

  return (
    <Modal
      title={
        <Space>
          <DashboardOutlined style={{ color: COLORS.primary }} />
          <span>智能测试分析仪表板</span>
          <Tag color="blue">v2.0</Tag>
        </Space>
      }
      visible={visible}
      onCancel={onCancel}
      width={1400}
      footer={null}
      bodyStyle={{ padding: '16px', backgroundColor: '#f5f5f5' }}
      className={styles.modernModal}
    >
      <div className={styles.chartContainer}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          size="large"
          type="card"
          className={styles.modernTabs}
        >
          <TabPane
            tab={
              <Space>
                <DashboardOutlined />
                <span>概览仪表板</span>
              </Space>
            }
            key="overview"
          >
            {renderOverviewDashboard()}
          </TabPane>
          <TabPane
            tab={
              <Space>
                <LineChartOutlined />
                <span>趋势分析</span>
              </Space>
            }
            key="trend"
          >
            {renderTrendChart()}
          </TabPane>
          <TabPane
            tab={
              <Space>
                <BarChartOutlined />
                <span>计划对比</span>
              </Space>
            }
            key="plan"
          >
            {renderPlanChart()}
          </TabPane>
          <TabPane
            tab={
              <Space>
                <ThunderboltOutlined />
                <span>GPU对比</span>
              </Space>
            }
            key="gpu"
          >
            {renderGpuChart()}
          </TabPane>
          <TabPane
            tab={
              <Space>
                <PieChartOutlined />
                <span>分布统计</span>
              </Space>
            }
            key="metrics"
          >
            {renderMetricsChart()}
          </TabPane>
        </Tabs>
      </div>
    </Modal>
  );
} 