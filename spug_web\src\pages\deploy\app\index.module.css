.steps {
    width: 520px;
    margin: 0 auto 30px;
}

.delIcon {
    font-size: 24px;
    position: relative;
    top: 4px
}

.delIcon:hover {
    color: #f5222d;
}

.deployBlock {
    height: 100px;
    margin-top: 63px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.cardBlock {
    display: flex;
    justify-content: space-around;
    background-color: rgba(240, 242, 245, 1);
    padding: 50px 0;
}

.cardTitle {
    margin-bottom: 12px;
    font-weight: 500;
    font-size: 16px;
    color: rgba(0, 0, 0, .85);
}

.cardDesc {
    height: 64px;
    overflow: hidden;
    color: rgba(0, 0, 0, .65);
}

.ext2Form :global(.ant-form-item) {
    margin-bottom: 10px;
}

.delAction {
    cursor: pointer;
    position: absolute;
    width: 35px;
    padding: 5px 10px;
    text-align: center;
    top: 32px;
    right: 60px;
    border: 1px dashed #d9d9d9;
    border-radius: 5px;
}

.delAction:hover {
    border-color: rgb(255, 96, 59);
    color: rgb(255, 96, 59);
}

.upAction {
    position: absolute;
    width: 35px;
    height: 26px;
    top: 0;
    right: 60px;
    border-radius: 5px;
    color: #d9d9d9;
}

.downAction {
    position: absolute;
    width: 35px;
    height: 26px;
    bottom: 0;
    right: 60px;
    border-radius: 5px;
    color: #d9d9d9;
}

.fullScreen {
    background-color: #fff;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
}

.webhook {
    cursor: pointer;
    color: #1890ff;
}