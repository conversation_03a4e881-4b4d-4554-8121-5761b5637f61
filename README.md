# Spug 运维管理平台

一个面向中小型企业的轻量级 DevOps 运维管理平台，基于 Django + React 构建，整合了主机管理、模型存储、GPU 管理、测试计划、文件同步、监控报警等功能。

## ✨ 核心特性

- 🖥️ **主机管理** - SSH 连接、批量执行、在线终端
- 🤖 **模型存储** - AI/ML 模型版本管理和 GPU 资源分配
- 🧪 **测试计划** - 自动化测试用例管理和执行
- 📁 **文件管理** - 远程文件同步和 Git 风格差异对比
- 📊 **监控报警** - 实时系统指标监控和告警通知
- 🚀 **应用部署** - 自动化部署流水线管理
- ⚙️ **配置中心** - 集中化配置管理
- 👥 **权限管理** - 基于角色的访问控制

## 🏗️ 技术架构

### 后端技术栈

- **框架**: Django 2.2.28 + Python 3.11+
- **数据库**: MySQL (生产环境) / SQLite (开发环境)
- **缓存**: Redis (支持多数据库配置)
- **任务队列**: Funboost + APScheduler
- **WebSocket**: Django Channels + Redis
- **认证**: 支持 LDAP 集成
- **文件处理**: openpyxl, paramiko SSH

### 前端技术栈

- **框架**: React 16.13.1
- **UI 库**: Ant Design 4.21.5
- **状态管理**: MobX 5.15.6
- **图表**: BizCharts, Recharts
- **代码编辑器**: React Ace (Monaco)
- **终端**: xterm.js
- **构建工具**: Create React App + 自定义配置

### 核心应用模块

- **model_storage** - 模型存储和 GPU 管理
- **exec** - 批量执行和测试计划
- **host** - 主机管理和 SSH 操作
- **deploy** - 应用部署管理
- **monitor** - 系统监控和报警
- **file** - 文件管理和同步
- **config** - 配置中心
- **account** - 用户权限管理

## 📁 项目结构

```
spug/
├── spug_api/           # 后端API服务 (Django)
├── spug_web/           # 前端界面 (React)
├── docs/               # 📚 文档目录
│   ├── api/           # API接口文档
│   ├── user-manual/   # 用户使用手册
│   └── development/   # 开发相关文档
├── scripts/           # 🔧 脚本文件
├── config/            # ⚙️ 配置文件
├── assets/            # 🖼️ 图片和静态资源
├── temp/              # 🗂️ 临时文件和测试文件
└── logs/              # 📝 日志文件
```

## 🚀 快速开始

### 环境要求

- Python 3.11+
- Node.js 12.14+
- Redis 服务器
- MySQL 5.7+ (生产环境推荐)

### 一键启动

```powershell
# Windows环境 - 自动配置环境并启动所有服务
.\start.ps1
```

### 手动部署

#### 1. 后端服务部署

```bash
cd spug_api

# 创建虚拟环境
python -m venv venv
.\venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -r requirements.txt

# 数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 启动开发服务器
python manage.py runserver
```

#### 2. 前端服务部署

```bash
cd spug_web

# 安装依赖
npm install --legacy-peer-deps

# 启动开发服务器
npm start

# 生产构建
npm run build
```

#### 3. Redis 配置

```bash
# 启动Redis服务器
redis-server

# 验证连接
redis-cli ping
```

### 访问地址

- 前端界面: http://localhost:3000
- 后端 API: http://localhost:8000
- 管理后台: http://localhost:8000/admin

## 🎯 功能详解

### 🤖 模型存储管理

- **GPU 设备管理**: 支持多种 GPU 型号 (P800, P800-PCIe, RG800 等)
- **模型版本控制**: 完整的模型生命周期管理
- **发布计划**: 支持模型发布计划制定和跟踪
- **状态监控**: 实时监控模型状态 (完成/部分缺失/缺失/进行中等)

### 🧪 测试计划系统

- **测试用例管理**: 支持测试用例集的创建和管理
- **任务分配**: 灵活的测试任务分配和进度跟踪
- **执行记录**: 详细的测试执行历史和结果分析
- **甘特图视图**: 可视化的项目进度管理
- **Excel 导入**: 支持批量导入测试任务

### 📁 文件管理系统

- **远程文件同步**: 支持远程服务器文件同步
- **Git 风格对比**: 详细的文件差异对比功能
- **文件状态跟踪**: 实时监控文件同步状态
- **批量操作**: 支持文件的批量上传和下载

### 📊 监控报警系统

- **实时指标监控**: CPU、内存、磁盘、网络使用率
- **远程服务器监控**: 支持多台服务器的集中监控
- **告警通知**: 支持多种通知方式 (邮件、微信等)
- **历史数据**: 完整的监控数据历史记录

### 🖥️ 主机管理

- **SSH 连接管理**: 安全的 SSH 连接和密钥管理
- **批量执行**: 支持多主机批量命令执行
- **在线终端**: 基于 xterm.js 的 Web 终端
- **文件传输**: 支持 SFTP 文件上传下载

### 🚀 应用部署

- **部署流水线**: 自动化的应用部署流程
- **版本管理**: Git 集成的版本控制
- **回滚机制**: 快速回滚到历史版本
- **环境管理**: 支持多环境部署 (开发/测试/生产)

## 📚 文档说明

### API 文档

- `docs/api/API_DOCUMENTATION.md` - 完整 API 接口文档
- `docs/api/发布计划API对接文档.md` - 发布计划相关 API

### 用户手册

- `docs/user-manual/model-storage-user-manual.html` - 模型存储功能使用手册
- `docs/user-manual/test-plan-user-manual.html` - 测试计划功能使用手册
- `docs/user-manual/文件详细对比功能说明.md` - 文件对比功能说明

### 开发文档

- `docs/development/BACKEND_LIBRARIES.md` - 后端依赖库说明
- `docs/development/COMPONENT_REFERENCE.md` - 前端组件参考
- `docs/development/QUICK_START_GUIDE.md` - 快速开发指南
- `docs/development/README_DOCUMENTATION.md` - 项目说明文档
- `docs/development/项目结构.md` - 详细项目结构说明
- `docs/development/Tread.md` - 线程相关文档

## 🔧 高级配置

### 数据库配置

```python
# spug_api/spug/settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'spug',
        'USER': 'root',
        'PASSWORD': 'your_password',
        'HOST': '**********',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'sql_mode': 'STRICT_TRANS_TABLES',
        }
    }
}
```

### Redis 配置

```python
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/1",
    },
    "remote_file_cache": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/2",
        "TIMEOUT": 86400,  # 缓存1天
    }
}
```

### Funboost 任务队列配置

```python
# config/funboost_config.py
from funboost import BrokerEnum

# 配置消息队列
BROKER_KIND = BrokerEnum.REDIS
REDIS_URL = 'redis://127.0.0.1:6379/0'

# 配置日志
LOG_LEVEL = 20  # INFO级别
```

## 💡 使用示例

### API 调用示例

#### 创建测试计划

```bash
curl -X POST http://localhost:8000/api/model-storage/new-release-plans/ \
  -H "Content-Type: application/json" \
  -d '{
    "name": "AI模型v2.0测试计划",
    "description": "新版本AI模型的全面测试",
    "start_date": "2025-01-20",
    "end_date": "2025-02-20",
    "status": "planning",
    "risk_level": "medium",
    "created_by": "测试工程师"
  }'
```

#### 获取服务器监控指标

```bash
curl -X GET "http://localhost:8000/api/model-storage/server-metrics/?host=**********"
```

#### 批量导入测试任务

```bash
curl -X POST http://localhost:8000/api/model-storage/test-tasks/import/ \
  -F "file=@测试任务导入模板.xlsx" \
  -F "release_plan_id=1"
```

### Python SDK 示例

```python
import requests

class SpugClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()

    def create_test_plan(self, plan_data):
        """创建测试计划"""
        url = f"{self.base_url}/api/model-storage/new-release-plans/"
        return self.session.post(url, json=plan_data)

    def get_server_metrics(self, host):
        """获取服务器指标"""
        url = f"{self.base_url}/api/model-storage/server-metrics/"
        return self.session.get(url, params={"host": host})

# 使用示例
client = SpugClient()
metrics = client.get_server_metrics("**********")
print(metrics.json())
```

## 🚨 故障排除

### 常见问题

#### 1. Redis 连接失败

```bash
# 检查Redis服务状态
redis-cli ping

# 如果返回PONG则正常，否则启动Redis
redis-server
```

#### 2. 数据库连接错误

```python
# 检查数据库配置
python manage.py dbshell

# 重新执行迁移
python manage.py migrate --run-syncdb
```

#### 3. 前端编译错误

```bash
# 清理node_modules重新安装
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps

# 如果仍有问题，使用Node.js 14.x版本
nvm use 14
```

#### 4. SSH 连接超时

```python
# 检查SSH配置
from libs.ssh import SSH

ssh = SSH('target_host', username='root', password='password')
if ssh.ping():
    print("SSH连接正常")
else:
    print("SSH连接失败，请检查网络和认证信息")
```

#### 5. 文件上传失败

```bash
# 检查文件权限和磁盘空间
df -h
ls -la spug_api/storage/

# 调整Django文件上传限制
# settings.py
FILE_UPLOAD_MAX_MEMORY_SIZE = 1000 * 1024 * 1024  # 1GB
```

### 日志调试

```bash
# 查看实时日志
tail -f logs/$(date +%Y-%m-%d).0001.root.log

# 查看特定模块日志
tail -f logs/$(date +%Y-%m-%d).0001.funboost.log

# 查看Django调试信息
cd spug_api
python manage.py runserver --verbosity=2
```

### 性能优化建议

#### 数据库优化

```sql
-- 为常用查询添加索引
CREATE INDEX idx_test_task_status ON model_storage_test_tasks_new(test_status);
CREATE INDEX idx_server_metrics_timestamp ON model_storage_server_metrics(timestamp);

-- 定期清理过期数据
DELETE FROM model_storage_server_metrics WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

#### Redis 优化

```bash
# redis.conf 优化配置
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
```

## ⚙️ 配置文件

- `config/funboost_config.py` - Funboost 分布式任务配置
- `config/nb_log_config.py` - 结构化日志配置
- `config/funboost_cli_user.py` - Funboost CLI 用户配置

## 🖼️ 资源文件

`assets/` 目录包含项目相关的截图和图片资源：

- 任务执行.png
- 引入命令.png
- 执行记录.png
- 测试计划相关截图等

## 🗂️ 临时文件

`temp/` 目录包含测试文件和临时文件：

- `quick_test.py` - 快速测试脚本
- `导入模板测试.xlsx` - 导入功能测试文件

## 📝 日志

项目运行日志存储在 `logs/` 目录中，按日期和模块分类。

## 🔧 维护

- 定期清理 `temp/` 目录中的临时文件
- 日志文件会自动轮转，旧日志可定期清理
- 配置文件修改后需要重启相应服务

## 📄 许可证

详见 `LICENSE` 文件。
