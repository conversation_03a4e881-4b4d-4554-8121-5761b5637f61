/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import { observable } from "mobx";
import { http, includes } from 'libs';

class Store {
  ParameterTypes = {
    'string': '文本框',
    'password': '密码框',
    'select': '下拉选择'
  }
  @observable records = [];
  @observable types = [];
  @observable record = {parameters: []};
  @observable isFetching = false;
  @observable formVisible = false;

  @observable f_name;
  @observable f_type;

  get dataSource() {
    // 确保records始终是数组
    let data = Array.isArray(this.records) ? this.records : []
    if (this.f_name) data = data.filter(x => includes(x.name, this.f_name))
    if (this.f_type) data = data.filter(x => includes(x.type, this.f_type))
    return data
  }

  fetchRecords = () => {
    this.isFetching = true;
    http.get('/api/exec/template/')
      .then(response => {
        // 处理响应数据，确保数据类型正确
        const types = response?.types || [];
        const templates = response?.templates || [];

        this.records = Array.isArray(templates) ? templates : [];
        this.types = Array.isArray(types) ? types : [];
      })
      .catch(error => {
        console.error('获取模板列表失败:', error);
        this.records = [];
        this.types = [];
      })
      .finally(() => {
        this.isFetching = false;
      })
  };

  showForm = (info = {interpreter: 'sh', host_ids: [], parameters: []}) => {
    this.formVisible = true;

    // 确保parameters字段始终是数组
    if (info && typeof info.parameters === 'string') {
      try {
        info.parameters = JSON.parse(info.parameters);
      } catch (e) {
        console.warn('Failed to parse parameters JSON:', e);
        info.parameters = [];
      }
    }

    if (!Array.isArray(info.parameters)) {
      info.parameters = [];
    }

    this.record = info;
  }
}

export default new Store()
