#!/usr/bin/env python3
"""
开发环境启动脚本
"""

import os
import sys
import subprocess

def main():
    print("🔧 启动开发环境...")
    
    # 切换到开发环境配置
    print("1. 切换到开发环境配置...")
    result = subprocess.run([sys.executable, "switch_env.py", "dev"], capture_output=True, text=True)
    if result.returncode != 0:
        print(f"❌ 环境切换失败: {result.stderr}")
        sys.exit(1)
    print(result.stdout)
    
    # 设置Django配置文件环境变量
    os.environ['DJANGO_SETTINGS_MODULE'] = 'spug.settings_current'
    
    print("2. 启动后端服务...")
    print("💡 请在另一个终端中运行以下命令启动后端:")
    print("   cd spug_api")
    print("   python manage.py runserver 0.0.0.0:9999 --settings=spug.settings_dev")
    
    print("\n3. 启动前端服务...")
    print("💡 请在另一个终端中运行以下命令启动前端:")
    print("   cd spug_web")
    print("   npm start")
    
    print("\n🎉 开发环境配置完成！")
    print("📱 前端访问地址: http://localhost:3000")
    print("🔗 后端API地址: http://localhost:9999")

if __name__ == "__main__":
    main()
