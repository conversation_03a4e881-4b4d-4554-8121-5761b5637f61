<!DOCTYPE html>
<html>
<head>
    <title>测试React警告抑制</title>
</head>
<body>
    <h1>测试React警告抑制功能</h1>
    <p>打开浏览器控制台查看是否还有React警告</p>
    
    <script>
        // 模拟React警告
        console.warn('Instance created by `useForm` is not connected to any Form element. Forget to pass `form` prop?');
        console.warn("Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application.");
        console.warn('这是一个普通的警告，应该显示');
        
        console.error('Instance created by `useForm` is not connected to any Form element. Forget to pass `form` prop?');
        console.error("Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application.");
        console.error('这是一个普通的错误，应该显示');
    </script>
</body>
</html>
