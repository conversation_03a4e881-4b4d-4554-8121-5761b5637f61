/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React from 'react';
import { observer } from 'mobx-react';
import { Table, Modal, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { http, hasPermission } from 'libs';
import { Action, TableCard, AuthButton } from "components";
import store from './store';

@observer
class ComTable extends React.Component {
  componentDidMount() {
    store.fetchRecords()
  }

  handleDelete = (text) => {
    Modal.confirm({
      title: '删除确认',
      content: `确定要删除【${text['name']}】?`,
      onOk: () => {
        return http.delete('/api/exec/template/', {params: {id: text.id}})
          .then(() => {
            message.success('删除成功');
            store.fetchRecords()
          })
      }
    })
  };

  render() {
    return (
      <TableCard
        tKey="et"
        title="模板列表"
        rowKey="id"
        loading={store.isFetching}
        dataSource={store.dataSource}
        onReload={store.fetchRecords}
        actions={[
          <AuthButton
            auth="exec.template.add"
            type="primary"
            icon={<PlusOutlined/>}
            onClick={() => store.showForm()}>新建</AuthButton>
        ]}
        pagination={{
          showSizeChanger: true,
          showLessItems: true,
          showTotal: total => `共 ${total} 条`,
          pageSizeOptions: ['10', '20', '50', '100']
        }}>
        <Table.Column title="模版名称" dataIndex="name"/>
        <Table.Column title="模版类型" dataIndex="type"/>
        <Table.Column title="脚本语言" dataIndex="interpreter" 
          render={v => v === 'python' ? '🐍 Python' : '🐚 Shell'}
        />
        <Table.Column title="参数化" dataIndex="parameters" 
          render={params => params && params.length > 0 ? 
            <span style={{ color: '#1890ff' }}>✅ {params.length} 个参数</span> : 
            <span style={{ color: '#999' }}>无参数</span>
          }
        />
        <Table.Column ellipsis title="模版内容" dataIndex="body"/>
        <Table.Column ellipsis title="描述信息" dataIndex="desc"/>
        {hasPermission('exec.template.edit|exec.template.del') && (
          <Table.Column title="操作" render={info => (
            <Action>
              <Action.Button auth="exec.template.edit" onClick={() => store.showForm(info)}>编辑</Action.Button>
              <Action.Button danger auth="exec.template.del" onClick={() => this.handleDelete(info)}>删除</Action.Button>
            </Action>
          )}/>
        )}
      </TableCard>
    )
  }
}

export default ComTable
