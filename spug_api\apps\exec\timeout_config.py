# -*- coding: utf-8 -*-
"""
执行任务超时配置
集中管理所有执行相关的超时设置，便于统一调整和维护
"""

# 任务计划执行超时配置（秒）
SCHEDULE_TIMEOUT = {
    'LOCAL_EXECUTOR': 14400,  # 本地执行器超时：4小时（原1小时）
    'HOST_EXECUTOR': 14400,   # 主机执行器超时：4小时
}

# 测试计划执行超时配置（秒）
TEST_PLAN_TIMEOUT = {
    'COMMAND_EXECUTION': 3600,    # 命令执行超时：10分钟（原2分钟）
    'ASSERTION_EXECUTION': 300,  # 断言执行超时：5分钟（原1分钟）
    'FILE_TRANSFER': 600,        # 文件传输超时：10分钟
}

# SSH连接相关超时配置（秒）
SSH_TIMEOUT = {
    'CONNECT_TIMEOUT': 10,       # 连接超时：10秒
    'EXEC_TIMEOUT': 3600,         # 执行超时：15分钟（原5分钟）
    'BANNER_TIMEOUT': 30,        # Banner超时：30秒
}

# 监控检测超时配置（秒）
MONITOR_TIMEOUT = {
    'SITE_CHECK': 30,           # 站点检测超时：30秒
    'PORT_CHECK': 5,            # 端口检测超时：5秒
    'COMMAND_CHECK': 60,        # 命令检测超时：60秒
}

# 文件操作超时配置（秒）
FILE_TIMEOUT = {
    'UPLOAD_TIMEOUT': 600,      # 文件上传超时：10分钟
    'DOWNLOAD_TIMEOUT': 600,    # 文件下载超时：10分钟
    'CACHE_REFRESH': 30,        # 缓存刷新超时：30秒
}

# 部署相关超时配置（秒）
DEPLOY_TIMEOUT = {
    'BUILD_TIMEOUT': 1800,      # 构建超时：30分钟
    'DEPLOY_TIMEOUT': 1800,     # 部署超时：30分钟
}

# 获取配置的便捷函数
def get_timeout(category, key):
    """
    获取指定类别和键的超时配置
    
    Args:
        category (str): 配置类别 (SCHEDULE, TEST_PLAN, SSH, MONITOR, FILE, DEPLOY)
        key (str): 配置键名
    
    Returns:
        int: 超时时间（秒）
    """
    timeout_configs = {
        'SCHEDULE': SCHEDULE_TIMEOUT,
        'TEST_PLAN': TEST_PLAN_TIMEOUT,
        'SSH': SSH_TIMEOUT,
        'MONITOR': MONITOR_TIMEOUT,
        'FILE': FILE_TIMEOUT,
        'DEPLOY': DEPLOY_TIMEOUT,
    }
    
    config = timeout_configs.get(category, {})
    return config.get(key, 300)  # 默认5分钟超时

# 超时配置说明
TIMEOUT_DESCRIPTIONS = {
    'SCHEDULE_TIMEOUT': {
        'LOCAL_EXECUTOR': '本地任务执行超时时间，适用于调度任务中的本地脚本执行',
        'HOST_EXECUTOR': '远程主机任务执行超时时间，适用于调度任务中的远程命令执行',
    },
    'TEST_PLAN_TIMEOUT': {
        'COMMAND_EXECUTION': '测试计划中单个命令执行的超时时间',
        'ASSERTION_EXECUTION': '测试计划中断言命令执行的超时时间',
        'FILE_TRANSFER': '测试计划中文件传输的超时时间',
    },
    'SSH_TIMEOUT': {
        'CONNECT_TIMEOUT': 'SSH连接建立的超时时间',
        'EXEC_TIMEOUT': 'SSH命令执行的默认超时时间',
        'BANNER_TIMEOUT': 'SSH Banner获取的超时时间',
    }
} 