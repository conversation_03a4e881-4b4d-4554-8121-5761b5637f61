# Generated manually to create new release plan models

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='NewReleasePlan',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=256, verbose_name='计划名称')),
                ('description', models.TextField(blank=True, default='', verbose_name='计划描述')),
                ('start_date', models.DateField(verbose_name='开始时间')),
                ('end_date', models.DateField(verbose_name='结束时间')),
                ('status', models.CharField(choices=[('planning', '计划中'), ('running', '进行中'), ('completed', '已完成'), ('cancelled', '已取消'), ('paused', '已暂停')], default='planning', max_length=32, verbose_name='状态')),
                ('risk_level', models.CharField(choices=[('low', '低风险'), ('medium', '中风险'), ('high', '高风险')], default='low', max_length=32, verbose_name='风险等级')),
                ('created_by', models.CharField(max_length=128, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '新版发布计划',
                'verbose_name_plural': '新版发布计划',
                'db_table': 'model_storage_new_release_plans',
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='TestTask',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_name', models.CharField(max_length=256, verbose_name='模型名称')),
                ('model_type', models.CharField(choices=[('inference', '推理'), ('training', '训练'), ('fine_tuning', '微调'), ('evaluation', '评估'), ('optimization', '优化')], max_length=32, verbose_name='模型类型')),
                ('framework', models.CharField(choices=[('pytorch', 'PyTorch'), ('tensorflow', 'TensorFlow'), ('onnx', 'ONNX'), ('keras', 'Keras'), ('paddle', 'PaddlePaddle'), ('mindspore', 'MindSpore'), ('other', '其他')], max_length=32, verbose_name='框架')),
                ('start_date', models.DateField(verbose_name='开始时间')),
                ('end_date', models.DateField(verbose_name='结束时间')),
                ('priority', models.CharField(choices=[('low', '低'), ('medium', '中'), ('high', '高'), ('urgent', '紧急')], default='medium', max_length=32, verbose_name='优先级')),
                ('test_status', models.CharField(choices=[('pending', '待测试'), ('running', '测试中'), ('completed', '已完成'), ('failed', '测试失败'), ('blocked', '阻塞'), ('cancelled', '已取消')], default='pending', max_length=32, verbose_name='测试状态')),
                ('tester', models.CharField(max_length=128, verbose_name='测试人员')),
                ('provider_status', models.CharField(choices=[('waiting', '等待中'), ('provided', '已提供'), ('partial', '部分提供'), ('rejected', '已拒绝')], default='waiting', max_length=32, verbose_name='模型提供状态')),
                ('progress', models.IntegerField(default=0, verbose_name='进度百分比')),
                ('notes', models.TextField(blank=True, default='', verbose_name='备注')),
                ('model_version', models.CharField(blank=True, default='', max_length=64, verbose_name='模型版本')),
                ('model_size', models.CharField(blank=True, default='', max_length=64, verbose_name='模型大小')),
                ('gpu_requirements', models.CharField(blank=True, default='', max_length=128, verbose_name='GPU需求')),
                ('memory_requirements', models.CharField(blank=True, default='', max_length=64, verbose_name='内存需求')),
                ('test_dataset', models.CharField(blank=True, default='', max_length=256, verbose_name='测试数据集')),
                ('accuracy_target', models.FloatField(blank=True, null=True, verbose_name='目标准确率')),
                ('actual_accuracy', models.FloatField(blank=True, null=True, verbose_name='实际准确率')),
                ('inference_speed', models.CharField(blank=True, default='', max_length=64, verbose_name='推理速度')),
                ('actual_start_date', models.DateField(blank=True, null=True, verbose_name='实际开始时间')),
                ('actual_end_date', models.DateField(blank=True, null=True, verbose_name='实际结束时间')),
                ('estimated_hours', models.IntegerField(default=0, verbose_name='预计工时')),
                ('actual_hours', models.IntegerField(default=0, verbose_name='实际工时')),
                ('color', models.CharField(default='#1890ff', max_length=32, verbose_name='显示颜色')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('release_plan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='test_tasks', to='model_storage.NewReleasePlan', verbose_name='发布计划')),
            ],
            options={
                'verbose_name': '测试任务',
                'verbose_name_plural': '测试任务',
                'db_table': 'model_storage_test_tasks',
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='RiskAlert',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=256, verbose_name='风险标题')),
                ('description', models.TextField(verbose_name='风险描述')),
                ('risk_type', models.CharField(choices=[('resource', '资源风险'), ('schedule', '进度风险'), ('technical', '技术风险'), ('quality', '质量风险'), ('dependency', '依赖风险'), ('other', '其他风险')], max_length=32, verbose_name='风险类型')),
                ('severity', models.CharField(choices=[('low', '低'), ('medium', '中'), ('high', '高'), ('critical', '严重')], max_length=32, verbose_name='严重程度')),
                ('status', models.CharField(choices=[('active', '活跃'), ('resolved', '已解决'), ('ignored', '已忽略'), ('monitoring', '监控中')], default='active', max_length=32, verbose_name='状态')),
                ('affected_tasks', models.TextField(blank=True, default='[]', help_text='JSON格式的任务ID数组', verbose_name='影响的任务ID列表')),
                ('mitigation_plan', models.TextField(blank=True, default='', verbose_name='缓解计划')),
                ('reporter', models.CharField(max_length=128, verbose_name='报告人')),
                ('assignee', models.CharField(blank=True, default='', max_length=128, verbose_name='负责人')),
                ('due_date', models.DateField(blank=True, null=True, verbose_name='截止日期')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='解决时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('release_plan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='risk_alerts', to='model_storage.NewReleasePlan', verbose_name='发布计划')),
            ],
            options={
                'verbose_name': '风险预警',
                'verbose_name_plural': '风险预警',
                'db_table': 'model_storage_risk_alerts',
                'ordering': ('-created_at',),
            },
        ),
    ] 