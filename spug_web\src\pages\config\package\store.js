/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import { observable, computed } from 'mobx';
import http from 'libs/http';

class Store {
  @observable records = [];
  @observable record = {};
  @observable loading = false;
  @observable formVisible = false;

  @computed get dataSource() {
    return this.records;
  }

  fetchRecords = () => {
    this.loading = true;
    return http.get('/api/config/package/')
      .then(res => this.records = res)
      .finally(() => this.loading = false)
  };

  showForm = (info = {}) => {
    this.formVisible = true;
    this.record = info
  };

  updateRecord = (id, info) => {
    info.id = id;
    return http.post('/api/config/package/', info)
      .then(() => {
        this.formVisible = false;
        this.fetchRecords()
      })
  };

  deleteRecord = (id) => {
    return http.delete('/api/config/package/', {params: {id}})
      .then(() => this.fetchRecords())
  }
}

export default new Store() 