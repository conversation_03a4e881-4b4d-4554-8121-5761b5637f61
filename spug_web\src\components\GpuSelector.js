import React, { useState, useEffect, useCallback, useRef } from 'react';
import { AutoComplete, Input } from 'antd';
import { ThunderboltOutlined } from '@ant-design/icons';
import http from '../libs/http';

/**
 * GPU选择器组件
 * 支持模糊搜索GPU名称和厂商
 */
export default function GpuSelector({
  value,
  onChange,
  placeholder = "请输入GPU型号",
  style,
  disabled = false,
  allowClear = true
}) {
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const timeoutRef = useRef(null);

  // 简单的防抖函数
  const debounce = (func, delay) => {
    return (...args) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => func(...args), delay);
    };
  };

  // 搜索函数
  const searchGpus = async (searchText) => {
    if (!searchText || searchText.trim().length === 0) {
      setOptions([]);
      return;
    }

    setLoading(true);
    try {
      const response = await http.get('/api/model-storage/gpus/', {
        params: {
          name: searchText.trim(),  // 使用name参数进行模糊搜索
          page_size: 20  // 限制返回数量
        }
      });

      // 处理分页和非分页数据格式
      const gpus = response.results || response || [];
      const gpuOptions = gpus.map(gpu => ({
        value: gpu.name,
        label: (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <ThunderboltOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              <span style={{ fontWeight: 500 }}>{gpu.name}</span>
            </div>
            <span style={{ color: '#999', fontSize: '12px' }}>
              {gpu.vendor}
            </span>
          </div>
        ),
        key: gpu.id,
        gpu: gpu  // 保存完整的GPU信息
      }));

      setOptions(gpuOptions);
    } catch (error) {
      console.error('搜索GPU失败:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索函数
  const debouncedSearch = useCallback(debounce(searchGpus, 300), []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // 处理搜索
  const handleSearch = (searchText) => {
    debouncedSearch(searchText);
  };

  // 处理选择
  const handleSelect = (selectedValue, option) => {
    onChange && onChange(selectedValue, option.gpu);
    // 选择后清空选项列表
    setOptions([]);
  };

  // 处理输入变化
  const handleChange = (inputValue) => {
    onChange && onChange(inputValue);
    // 如果输入为空，清空选项
    if (!inputValue) {
      setOptions([]);
    }
  };

  return (
    <AutoComplete
      value={value}
      options={options}
      onSearch={handleSearch}
      onSelect={handleSelect}
      onChange={handleChange}
      placeholder={placeholder}
      style={style}
      disabled={disabled}
      allowClear={allowClear}
      notFoundContent={loading ? '搜索中...' : '暂无匹配GPU'}
      filterOption={false} // 禁用本地过滤，使用服务端搜索
    >
      <Input 
        prefix={<ThunderboltOutlined style={{ color: '#bfbfbf' }} />}
        placeholder={placeholder}
      />
    </AutoComplete>
  );
}
