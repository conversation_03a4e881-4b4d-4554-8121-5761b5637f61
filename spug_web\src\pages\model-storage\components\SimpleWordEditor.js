import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Space, message, Input, Divider, Tooltip } from 'antd';
import {
  FileWordOutlined,
  CopyOutlined,
  ClearOutlined,
  EyeOutlined,
  EditOutlined,
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined
} from '@ant-design/icons';

const { TextArea } = Input;

/**
 * 简单的Word风格编辑器
 * 基于textarea + 工具栏，提供基本的富文本功能
 */
export default function SimpleWordEditor({ 
  value = '', 
  onChange, 
  placeholder = '请输入内容...', 
  height = 300,
  disabled = false 
}) {
  const [content, setContent] = useState(value);
  const [isPreview, setIsPreview] = useState(false);
  const textAreaRef = useRef(null);

  useEffect(() => {
    // 只有当外部value真正改变时才更新内部状态
    if (value !== content) {
      setContent(value);
    }
  }, [value, content]);

  // 处理内容变化
  const handleChange = (e) => {
    const newContent = e.target.value;
    
    // 避免无限循环：只有当内容真正改变时才更新
    if (newContent !== content) {
      setContent(newContent);
      if (onChange) {
        onChange(newContent);
      }
    }
  };

  // 复制内容到剪贴板
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      message.success('内容已复制到剪贴板');
    } catch (err) {
      message.error('复制失败');
    }
  };

  // 清空内容
  const handleClear = () => {
    const newContent = '';
    setContent(newContent);
    if (onChange) {
      onChange(newContent);
    }
    message.success('内容已清空');
  };

  // 插入文本格式
  const insertFormat = (prefix, suffix = '') => {
    const textarea = textAreaRef.current?.resizableTextArea?.textArea;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);
    
    const newText = content.substring(0, start) + 
                   prefix + selectedText + suffix + 
                   content.substring(end);
    
    setContent(newText);
    if (onChange) {
      onChange(newText);
    }

    // 恢复焦点和选择
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(
        start + prefix.length, 
        end + prefix.length
      );
    }, 0);
  };

  // 插入标题
  const insertHeading = (level) => {
    const prefix = '#'.repeat(level) + ' ';
    insertFormat('\n' + prefix);
  };

  // 工具栏按钮
  const toolbarButtons = [
    {
      icon: <BoldOutlined />,
      title: '粗体 (**文字**)',
      onClick: () => insertFormat('**', '**')
    },
    {
      icon: <ItalicOutlined />,
      title: '斜体 (*文字*)',
      onClick: () => insertFormat('*', '*')
    },
    {
      icon: <UnderlineOutlined />,
      title: '下划线',
      onClick: () => insertFormat('<u>', '</u>')
    },
    {
      icon: <OrderedListOutlined />,
      title: '有序列表',
      onClick: () => insertFormat('\n1. ')
    },
    {
      icon: <UnorderedListOutlined />,
      title: '无序列表',
      onClick: () => insertFormat('\n- ')
    },
    {
      icon: <AlignLeftOutlined />,
      title: '标题1',
      onClick: () => insertHeading(1)
    },
    {
      icon: <AlignCenterOutlined />,
      title: '标题2',
      onClick: () => insertHeading(2)
    },
    {
      icon: <AlignRightOutlined />,
      title: '标题3',
      onClick: () => insertHeading(3)
    }
  ];

  // 渲染HTML内容（增强的markdown转换）
  const renderHTML = (text) => {
    return text
      // 标题
      .replace(/\n### (.*?)(?=\n|$)/g, '<h3>$1</h3>')
      .replace(/\n## (.*?)(?=\n|$)/g, '<h2>$1</h2>')
      .replace(/\n# (.*?)(?=\n|$)/g, '<h1>$1</h1>')
      // 格式
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/<u>(.*?)<\/u>/g, '<u>$1</u>')
      // 列表
      .replace(/\n- (.*?)(?=\n|$)/g, '<li style="list-style-type: disc; margin-left: 20px;">$1</li>')
      .replace(/\n\d+\. (.*?)(?=\n|$)/g, '<li style="list-style-type: decimal; margin-left: 20px;">$1</li>')
      // 换行
      .replace(/\n/g, '<br/>');
  };

  return (
    <Card
      size="small"
      extra={
        <Space>
          <Button
            size="small"
            icon={<CopyOutlined />}
            onClick={handleCopy}
            disabled={!content}
          >
            复制
          </Button>
          <Button
            size="small"
            icon={<ClearOutlined />}
            onClick={handleClear}
            disabled={!content}
          >
            清空
          </Button>
          <Button
            size="small"
            icon={isPreview ? <EditOutlined /> : <EyeOutlined />}
            onClick={() => setIsPreview(!isPreview)}
          >
            {isPreview ? '编辑' : '预览'}
          </Button>
        </Space>
      }
      style={{ marginBottom: 16 }}
    >
      {/* 工具栏 */}
      {!isPreview && (
        <div style={{ 
          marginBottom: 8, 
          padding: '8px 12px', 
          background: '#fafafa', 
          border: '1px solid #d9d9d9',
          borderRadius: '6px 6px 0 0',
          borderBottom: 'none'
        }}>
          <Space>
            {toolbarButtons.map((btn, index) => (
              <Tooltip key={index} title={btn.title}>
                <Button
                  size="small"
                  icon={btn.icon}
                  onClick={btn.onClick}
                  disabled={disabled}
                />
              </Tooltip>
            ))}
          </Space>
        </div>
      )}

      {isPreview ? (
        // 预览模式
        <div 
          style={{ 
            minHeight: height,
            padding: 16,
            border: '1px solid #d9d9d9',
            borderRadius: 6,
            backgroundColor: '#fff',
            overflow: 'auto',
            fontFamily: 'Microsoft YaHei, SimSun, Arial, sans-serif',
            fontSize: '14px',
            lineHeight: '1.6'
          }}
          dangerouslySetInnerHTML={{ __html: renderHTML(content) }}
        />
      ) : (
        // 编辑模式
        <TextArea
          ref={textAreaRef}
          value={content}
          onChange={handleChange}
          placeholder={placeholder}
          disabled={disabled}
          style={{
            height: height,
            fontFamily: 'Microsoft YaHei, SimSun, Arial, sans-serif',
            fontSize: '14px',
            lineHeight: '1.6',
            borderRadius: isPreview ? '6px' : '0 0 6px 6px',
            borderTop: isPreview ? '1px solid #d9d9d9' : 'none'
          }}
          autoSize={false}
        />
      )}
      
      {/* 提示信息 */}
      <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
        💡 支持格式：**粗体** *斜体* <u>下划线</u> # 标题 - 列表 1. 有序列表
      </div>
    </Card>
  );
}
