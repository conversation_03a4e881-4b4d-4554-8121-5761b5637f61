# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from django.db import models
from libs import ModelMixin, human_datetime
from apps.account.models import User
import json


class Task(models.Model, ModelMixin):
    """执行任务模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='exec_task')
    host_id = models.IntegerField(null=True)
    command = models.TextField()
    is_public = models.BooleanField(default=False)
    created_at = models.CharField(max_length=20, default=human_datetime)
    created_by = models.ForeignKey(User, models.PROTECT, related_name='+', null=True)

    def __repr__(self):
        return f'<Task {self.id}>'

    @property
    def host(self):
        """获取关联的主机对象"""
        if self.host_id:
            from apps.host.models import Host
            return Host.objects.filter(pk=self.host_id).first()
        return None

    class Meta:
        db_table = 'exec_tasks'
        ordering = ('-id',)



class ExecTemplate(models.Model, ModelMixin):
    name = models.CharField(max_length=50)
    type = models.CharField(max_length=50)
    body = models.TextField()
    interpreter = models.CharField(max_length=20, default='sh')
    host_ids = models.TextField(default='[]')
    desc = models.CharField(max_length=255, null=True)
    parameters = models.TextField(default='[]')
    created_at = models.CharField(max_length=20, default=human_datetime)
    created_by = models.ForeignKey(User, models.PROTECT, related_name='+')
    updated_at = models.CharField(max_length=20, null=True)
    updated_by = models.ForeignKey(User, models.PROTECT, related_name='+', null=True)

    def __repr__(self):
        return '<ExecTemplate %r>' % self.name

    def to_view(self):
        tmp = self.to_dict()
        try:
            tmp['host_ids'] = json.loads(self.host_ids) if self.host_ids else []
        except (json.JSONDecodeError, TypeError):
            tmp['host_ids'] = []
        try:
            tmp['parameters'] = json.loads(self.parameters) if self.parameters else []
        except (json.JSONDecodeError, TypeError):
            tmp['parameters'] = []
        return tmp

    class Meta:
        db_table = 'exec_templates'
        ordering = ('-id',)


class ExecHistory(models.Model, ModelMixin):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    template = models.ForeignKey(ExecTemplate, on_delete=models.SET_NULL, null=True)
    digest = models.CharField(max_length=32, db_index=True)
    interpreter = models.CharField(max_length=20)
    command = models.TextField()
    params = models.TextField(default='{}')
    host_ids = models.TextField()
    updated_at = models.CharField(max_length=20, default=human_datetime)

    def to_view(self):
        tmp = self.to_dict()
        tmp['host_ids'] = json.loads(self.host_ids)
        if self.template:
            tmp['template_name'] = self.template.name
            tmp['interpreter'] = self.template.interpreter
            tmp['parameters'] = json.loads(self.template.parameters)
            tmp['command'] = self.template.body
        return tmp

    class Meta:
        db_table = 'exec_histories'
        ordering = ('-updated_at',)


class Transfer(models.Model, ModelMixin):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    digest = models.CharField(max_length=32, db_index=True)
    host_id = models.IntegerField(null=True)
    src_dir = models.CharField(max_length=255)
    dst_dir = models.CharField(max_length=255)
    host_ids = models.TextField()
    extra = models.TextField(null=True, blank=True)  # 存储Docker配置等扩展信息
    updated_at = models.CharField(max_length=20, default=human_datetime)

    def to_view(self):
        tmp = self.to_dict()
        tmp['host_ids'] = json.loads(self.host_ids)
        return tmp

    class Meta:
        db_table = 'exec_transfer'
        ordering = ('-id',)


class TransferTemplate(models.Model, ModelMixin):
    """文件分发配置模板"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=100, help_text='模板名称')
    description = models.TextField(blank=True, help_text='模板描述')
    config = models.TextField(help_text='配置JSON')  # 存储分发配置
    is_public = models.BooleanField(default=False, help_text='是否公开')
    created_at = models.CharField(max_length=20, default=human_datetime)
    updated_at = models.CharField(max_length=20, default=human_datetime)

    def to_view(self):
        tmp = self.to_dict()
        tmp['config'] = json.loads(self.config)
        return tmp

    class Meta:
        db_table = 'exec_transfer_template'
        ordering = ('-updated_at',)


class TestPlan(models.Model, ModelMixin):
    """测试计划模型"""
    name = models.CharField(max_length=100, verbose_name='计划名称')
    description = models.TextField(null=True, blank=True, verbose_name='计划描述')
    category = models.CharField(max_length=50, null=True, blank=True, verbose_name='计划分类')
    commands = models.TextField(default='[]', verbose_name='执行步骤JSON')
    files = models.TextField(default='[]', verbose_name='关联文件JSON')
    steps = models.TextField(default='[]', verbose_name='步骤配置JSON')
    step_interval = models.IntegerField(default=0, verbose_name='步骤间隔(秒)')
    # 文件路径策略配置
    file_path_strict = models.BooleanField(default=False, verbose_name='严格文件路径模式')
    # 变量系统
    variables = models.TextField(default='[]', verbose_name='测试计划变量JSON')
    created_at = models.CharField(max_length=20, default=human_datetime, verbose_name='创建时间')
    created_by_id = models.IntegerField(verbose_name='创建人ID', default=1)
    updated_at = models.CharField(max_length=20, null=True, verbose_name='更新时间')
    updated_by_id = models.IntegerField(verbose_name='更新人ID', null=True)

    def __repr__(self):
        return f'<TestPlan {self.name}>'

    def _replace_variables_in_text(self, text, variables):
        """在文本中替换变量"""
        import re
        if not text or not variables:
            return text
            
        result = text
        for var in variables:
            var_name = var.get('name', '')
            var_value = var.get('value', '')
            if var_name and var_value is not None:
                # 替换${variable_name}格式的变量
                pattern = r'\$\{' + re.escape(var_name) + r'\}'
                result = re.sub(pattern, str(var_value), result)
        return result

    def to_dict(self, replace_variables=False):
        """转换为字典格式
        
        Args:
            replace_variables (bool): 是否在返回的命令中替换变量
        """
        try:
            variables = json.loads(self.variables) if self.variables else []
        except (json.JSONDecodeError, TypeError):
            print(f"[WARNING] TestPlan {self.name} variables字段解析失败，使用空列表")
            variables = []
            
        try:
            commands = json.loads(self.commands) if self.commands else []
        except (json.JSONDecodeError, TypeError):
            print(f"[WARNING] TestPlan {self.name} commands字段解析失败，使用空列表")
            commands = []
            
        try:
            files = json.loads(self.files) if self.files else []
        except (json.JSONDecodeError, TypeError):
            print(f"[WARNING] TestPlan {self.name} files字段解析失败，使用空列表")
            files = []
            
        try:
            steps = json.loads(self.steps) if self.steps else []
        except (json.JSONDecodeError, TypeError):
            print(f"[WARNING] TestPlan {self.name} steps字段解析失败，使用空列表")
            steps = []
        
        # 如果需要替换变量，则处理命令和步骤中的变量
        if replace_variables and variables:
            # 替换commands中的变量
            processed_commands = []
            for cmd in commands:
                processed_cmd = dict(cmd)
                if 'command' in processed_cmd:
                    processed_cmd['command'] = self._replace_variables_in_text(processed_cmd['command'], variables)
                processed_commands.append(processed_cmd)
            commands = processed_commands
            
            # 替换steps中的变量
            processed_steps = []
            for step in steps:
                processed_step = dict(step)
                if 'command' in processed_step:
                    processed_step['command'] = self._replace_variables_in_text(processed_step['command'], variables)
                processed_steps.append(processed_step)
            steps = processed_steps
        
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'commands': commands,
            'files': files,
            'steps': steps,
            'step_interval': self.step_interval,
            'file_path_strict': self.file_path_strict,
            'variables': variables,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'created_by_id': self.created_by_id,
            'updated_by_id': self.updated_by_id,
        }

    class Meta:
        db_table = 'exec_test_plans'
        ordering = ('-id',)
        verbose_name = '测试计划'
        verbose_name_plural = '测试计划'


class TestCaseSet(models.Model, ModelMixin):
    """测试用例集模型"""
    name = models.CharField(max_length=100, verbose_name='用例集名称')
    description = models.TextField(null=True, blank=True, verbose_name='用例集描述')
    category = models.CharField(max_length=50, null=True, blank=True, verbose_name='用例集分类')
    test_cases = models.TextField(default='[]', verbose_name='测试用例JSON')
    # 新增：模板引用字段，保存引用的模板ID列表
    template_references = models.TextField(default='[]', verbose_name='引用的模板ID列表JSON')
    created_at = models.CharField(max_length=20, default=human_datetime, verbose_name='创建时间')
    created_by_id = models.IntegerField(verbose_name='创建人ID', default=1)
    updated_at = models.CharField(max_length=20, null=True, verbose_name='更新时间')
    updated_by_id = models.IntegerField(verbose_name='更新人ID', null=True)

    def __repr__(self):
        return f'<TestCaseSet {self.name}>'

    def to_dict(self):
        """转换为字典格式，实时获取引用的模板内容"""
        try:
            test_cases = json.loads(self.test_cases) if self.test_cases else []
        except (json.JSONDecodeError, TypeError):
            print(f"[WARNING] TestCaseSet {self.name} test_cases字段解析失败，使用空列表")
            test_cases = []

        try:
            template_references = json.loads(self.template_references) if self.template_references else []
        except (json.JSONDecodeError, TypeError):
            print(f"[WARNING] TestCaseSet {self.name} template_references字段解析失败，使用空列表")
            template_references = []

        # 实时获取引用的模板内容
        referenced_cases = []
        if template_references:
            try:
                # 获取引用的模板
                referenced_templates = TestCaseTemplate.objects.filter(id__in=template_references)
                for template in referenced_templates:
                    referenced_case = {
                        'id': f'template_{template.id}',  # 使用特殊ID标识这是模板引用
                        'name': template.name,
                        'precondition': template.precondition or '',
                        'test_steps': template.test_steps or '',
                        'expected_result': template.expected_result or '',
                        'enabled': template.enabled,
                        'sort_order': template.sort_order,
                        'is_template_reference': True,  # 标记为模板引用
                        'template_id': template.id,  # 保存原始模板ID
                        'template_category': template.category,
                        'template_description': template.description
                    }
                    referenced_cases.append(referenced_case)
            except Exception as e:
                print(f"[WARNING] 获取模板引用失败: {str(e)}")

        # 合并自定义用例和引用的模板用例
        all_test_cases = test_cases + referenced_cases

        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'test_cases': all_test_cases,
            'template_references': template_references,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'created_by_id': self.created_by_id,
            'updated_by_id': self.updated_by_id,
        }



    class Meta:
        db_table = 'exec_test_case_sets'
        ordering = ('-id',)
        verbose_name = '测试用例集'
        verbose_name_plural = '测试用例集'


class TestPlanExecution(models.Model, ModelMixin):
    """测试计划执行记录模型"""
    test_plan = models.ForeignKey(TestPlan, models.CASCADE, verbose_name='测试计划', null=True, blank=True)
    plan_id = models.IntegerField(verbose_name='测试计划ID', default=0)
    plan_name = models.CharField(max_length=100, verbose_name='测试计划名称', default='未知计划')
    token = models.CharField(max_length=32, unique=True, verbose_name='执行令牌')
    host_ids = models.TextField(verbose_name='目标主机JSON')
    user_id = models.IntegerField(verbose_name='执行用户ID', null=True)
    executor_id = models.IntegerField(verbose_name='执行用户ID', default=1)
    executor_name = models.CharField(max_length=50, verbose_name='执行用户名', default='系统管理员')
    status = models.CharField(max_length=20, default='running', verbose_name='执行状态')
    start_time = models.CharField(max_length=20, default=human_datetime, verbose_name='开始时间')
    end_time = models.CharField(max_length=20, null=True, verbose_name='结束时间')
    total_steps = models.IntegerField(default=0, verbose_name='总步骤数')
    completed_steps = models.IntegerField(default=0, verbose_name='已完成步骤')
    log_file = models.CharField(max_length=255, null=True, blank=True, verbose_name='日志文件路径')
    step_logs_folder = models.CharField(max_length=255, null=True, blank=True, verbose_name='步骤日志文件夹路径')

    def __repr__(self):
        return f'<TestPlanExecution {self.test_plan.name}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'token': self.token,
            'plan_id': self.plan_id,
            'plan_name': self.plan_name,
            'host_ids': json.loads(self.host_ids),
            'user_id': self.executor_id,
            'user_name': self.executor_name,
            'status': self.status,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'total_steps': self.total_steps,
            'completed_steps': self.completed_steps,
            'log_file': self.log_file,
            'step_logs_folder': self.step_logs_folder,
            'plan': self.test_plan.to_dict() if self.test_plan else None,
        }

    class Meta:
        db_table = 'exec_test_plan_executions'
        ordering = ('-id',)
        verbose_name = '测试计划执行记录'
        verbose_name_plural = '测试计划执行记录'


class TestPlanStepResult(models.Model, ModelMixin):
    """测试计划步骤执行结果模型"""
    execution = models.ForeignKey(TestPlanExecution, models.CASCADE, verbose_name='执行记录')
    step_type = models.CharField(max_length=20, verbose_name='步骤类型')
    step_name = models.CharField(max_length=200, verbose_name='步骤名称')
    command = models.TextField(null=True, blank=True, verbose_name='执行命令')
    file_path = models.CharField(max_length=500, null=True, blank=True, verbose_name='文件路径')
    start_time = models.CharField(max_length=20, default=human_datetime, verbose_name='开始时间')
    end_time = models.CharField(max_length=20, null=True, verbose_name='结束时间')
    exit_code = models.IntegerField(null=True, verbose_name='退出码')
    output = models.TextField(default='', verbose_name='输出内容')
    status = models.CharField(max_length=20, default='running', verbose_name='执行状态')
    
    # 断言相关字段
    assertion_enabled = models.BooleanField(default=False, verbose_name='是否启用断言')
    assertion_type = models.CharField(max_length=20, null=True, blank=True, verbose_name='断言类型')
    assertion_config = models.TextField(default='{}', verbose_name='断言配置JSON')
    assertion_status = models.CharField(max_length=20, null=True, blank=True, verbose_name='断言状态')
    assertion_result = models.TextField(default='', verbose_name='断言结果详情')
    assertion_message = models.TextField(default='', verbose_name='断言消息')

    def __repr__(self):
        return f'<TestPlanStepResult {self.step_name}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'execution_id': self.execution.id,
            'step_type': self.step_type,
            'step_name': self.step_name,
            'command': self.command,
            'file_path': self.file_path,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'exit_code': self.exit_code,
            'output': self.output,
            'status': self.status,
            'assertion_enabled': self.assertion_enabled,
            'assertion_type': self.assertion_type,
            'assertion_config': json.loads(self.assertion_config) if self.assertion_config else {},
            'assertion_status': self.assertion_status,
            'assertion_result': self.assertion_result,
            'assertion_message': self.assertion_message,
        }

    class Meta:
        db_table = 'exec_test_plan_step_results'
        ordering = ('id',)
        verbose_name = '测试计划步骤结果'
        verbose_name_plural = '测试计划步骤结果'


class TestResult(models.Model):
    """测试结果模型 - 支持测试计划绑定和独立日志提取两种模式"""
    # 可选的测试计划执行关联 - 为None时表示独立的日志提取结果
    execution = models.ForeignKey(TestPlanExecution, on_delete=models.CASCADE,
                                related_name='test_results', null=True, blank=True)

    # 基本信息
    plan_name = models.CharField(max_length=200, null=True, blank=True, help_text='测试计划名称')
    gpu_name = models.CharField(max_length=200, null=True, blank=True, help_text='关联GPU名称')
    model_name = models.CharField(max_length=200, null=True, blank=True, help_text='关联模型名称')
    
    # 数据来源类型
    source_type = models.CharField(max_length=20, choices=[
        ('execution', '测试计划执行'),
        ('log_extraction', '日志提取'),
        ('manual', '手动录入')
    ], default='execution', help_text='数据来源类型')
    
    # 日志来源信息（用于日志提取模式）
    log_source_host = models.CharField(max_length=100, null=True, blank=True, help_text='日志来源主机')
    log_source_path = models.CharField(max_length=500, null=True, blank=True, help_text='日志文件路径')
    log_extraction_method = models.CharField(max_length=20, choices=[
        ('remote', '远程提取'),
        ('upload', '文件上传'),
        ('paste', '粘贴输入')
    ], null=True, blank=True, help_text='日志提取方式')
    
    # 结果数据(JSON格式存储)
    metrics = models.TextField(default='{}', help_text='性能指标数据JSON')
    raw_log = models.TextField(null=True, blank=True, help_text='原始日志内容')
    
    # 提取信息
    extraction_time = models.DateTimeField(auto_now_add=True, help_text='结果提取时间') 
    total_metrics = models.IntegerField(default=0, help_text='总指标数量')
    confirmed_metrics = models.IntegerField(default=0, help_text='已确认指标数量')
    ai_confidence = models.FloatField(default=0.0, help_text='AI识别平均置信度')
    
    # 元数据
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by_id = models.IntegerField(default=1, help_text='创建者ID')
    
    class Meta:
        db_table = 'exec_test_results'
        verbose_name = '测试结果'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        
    def __str__(self):
        source_info = f"[{self.get_source_type_display()}]" if self.source_type != 'execution' else ""
        return f'{source_info}{self.plan_name} - {self.created_at.strftime("%Y-%m-%d %H:%M")}'
    
    @property
    def success_rate(self):
        """计算成功率"""
        if self.total_metrics == 0:
            return 0
        return round((self.confirmed_metrics / self.total_metrics) * 100, 2)
    
    @property
    def is_standalone(self):
        """判断是否为独立的日志提取结果"""
        return self.execution is None
    
    def get_metrics(self):
        """获取指标数据"""
        import json
        try:
            result = json.loads(self.metrics)
            return result
        except Exception:
            return {}
    
    def set_metrics(self, data):
        """设置指标数据"""
        import json
        self.metrics = json.dumps(data, ensure_ascii=False)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'execution_id': self.execution.id if self.execution else None,
            'plan_name': self.plan_name,
            'gpu_name': self.gpu_name,
            'model_name': self.model_name,
            'source_type': self.source_type,
            'log_source_host': self.log_source_host,
            'log_source_path': self.log_source_path,
            'log_extraction_method': self.log_extraction_method,
            'metrics': self.get_metrics(),
            'raw_log': self.raw_log,
            'total_metrics': self.total_metrics,
            'confirmed_metrics': self.confirmed_metrics,
            'ai_confidence': self.ai_confidence,
            'success_rate': self.success_rate,
            'is_standalone': self.is_standalone,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
            'created_by_id': self.created_by_id
        }


class TestCaseTemplate(models.Model, ModelMixin):
    """测试用例池模型"""
    name = models.CharField(max_length=100, verbose_name='用例模板名称')
    description = models.TextField(null=True, blank=True, verbose_name='用例模板描述')
    category = models.CharField(max_length=50, null=True, blank=True, verbose_name='用例模板分类')

    # 用例字段 - 与测试用例保持一致
    precondition = models.TextField(null=True, blank=True, verbose_name='预置条件')
    test_steps = models.TextField(null=True, blank=True, verbose_name='测试步骤')
    expected_result = models.TextField(null=True, blank=True, verbose_name='预期结果')

    # 元数据
    enabled = models.BooleanField(default=True, verbose_name='是否启用')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    created_at = models.CharField(max_length=20, default=human_datetime, verbose_name='创建时间')
    created_by_id = models.IntegerField(verbose_name='创建人ID', default=1)
    updated_at = models.CharField(max_length=20, null=True, verbose_name='更新时间')
    updated_by_id = models.IntegerField(verbose_name='更新人ID', null=True)

    def __repr__(self):
        return f'<TestCaseTemplate {self.name}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'precondition': self.precondition,
            'test_steps': self.test_steps,
            'expected_result': self.expected_result,
            'enabled': self.enabled,
            'sort_order': self.sort_order,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'created_by_id': self.created_by_id,
            'updated_by_id': self.updated_by_id
        }

    class Meta:
        db_table = 'exec_test_case_templates'
        ordering = ('sort_order', '-id')
        verbose_name = '测试用例模板'
        verbose_name_plural = verbose_name


class TestResultTemplate(models.Model):
    """测试结果模板"""
    name = models.CharField(max_length=100, help_text='模板名称')
    description = models.TextField(null=True, blank=True, help_text='模板描述')
    category = models.CharField(max_length=50, choices=[
        ('gpu', 'GPU性能测试'),
        ('cpu', 'CPU性能测试'),
        ('memory', '内存测试'),
        ('network', '网络测试'),
        ('storage', '存储测试'),
        ('custom', '自定义测试')
    ], default='custom')

    # 模板配置
    expected_metrics = models.TextField(default='[]', help_text='期望的指标列表JSON')
    extraction_patterns = models.TextField(default='[]', help_text='提取模式配置JSON')
    validation_rules = models.TextField(default='{}', help_text='验证规则JSON')

    # 元数据
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by_id = models.IntegerField(default=1, help_text='创建者ID')

    class Meta:
        db_table = 'exec_test_result_templates'
        verbose_name = '测试结果模板'
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name

    def get_expected_metrics(self):
        """获取期望指标"""
        import json
        try:
            return json.loads(self.expected_metrics)
        except:
            return []
    
    def get_extraction_patterns(self):
        """获取提取模式"""
        import json
        try:
            return json.loads(self.extraction_patterns)
        except:
            return []
    
    def get_validation_rules(self):
        """获取验证规则"""
        import json
        try:
            return json.loads(self.validation_rules)
        except:
            return {}
