/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React, { useEffect } from 'react';
import { observer } from 'mobx-react';
import { Row, Col, Typography, Space } from 'antd';
import { CodeOutlined, CloudServerOutlined } from '@ant-design/icons';
import { AuthDiv, AuthButton } from 'components';
import Group from './Group';
import ComTable from './Table';
import ComForm from './Form';
import ComImport from './Import';
import CloudImport from './CloudImport';
import BatchSync from './BatchSync';
import Detail from './Detail';
import Selector from './Selector';
import store from './store';
import styles from './index.module.less';

const { Title } = Typography;

export default observer(function () {

  useEffect(() => {
    store.initial()
  }, [])

  function openTerminal() {
    window.open('/ssh')
  }

  return (
    <AuthDiv auth="host.host.view">
      <div className={styles.modernContainer}>
        {/* Header Section */}
        <div className={styles.headerSection}>
          <div className={styles.headerContent}>
            <Space align="center" size={16}>
              <div className={styles.headerIcon}>
                <CloudServerOutlined />
              </div>
              <div>
                <Title level={3} className={styles.headerTitle}>主机管理</Title>
                <div className={styles.headerSubtitle}>管理和监控您的服务器资源</div>
              </div>
            </Space>
            <AuthButton 
              auth="host.console.view|host.console.list" 
              type="primary" 
              icon={<CodeOutlined/>}
              size="large"
              className={styles.terminalButton}
              onClick={openTerminal}
            >
              Web 终端
            </AuthButton>
          </div>
        </div>

        {/* Main Content */}
        <div className={styles.mainContent}>
          <Row gutter={[16, 24]} style={{ margin: 0 }}>
            <Col xs={24} sm={24} md={6} lg={5} xl={4}>
              <div className={styles.sidebarCard}>
                <Group/>
              </div>
            </Col>
            <Col xs={24} sm={24} md={18} lg={19} xl={20}>
              <div className={styles.tableCard}>
                <ComTable/>
              </div>
            </Col>
          </Row>
        </div>
      </div>

      <Detail/>
      {store.formVisible && <ComForm/>}
      {store.importVisible && <ComImport/>}
      {store.cloudImport && <CloudImport/>}
      {store.syncVisible && <BatchSync/>}
      {store.selectorVisible &&
        <Selector
          mode="group"
          onlySelf={!store.addByCopy}
          onCancel={() => store.selectorVisible = false}
          onChange={store.updateGroup}
        />}
    </AuthDiv>
  );
})
