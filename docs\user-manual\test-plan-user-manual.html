<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试计划与任务执行 - 用户手册</title>
    <!-- Font Awesome for icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* --- 基础重置 --- */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #2c3e50; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }

        /* --- 头部 --- */
        .header { background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%); color: white; padding: 60px 0; text-align: center; position: relative; overflow: hidden; }
        .header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>'); opacity: 0.3; }
        .header-content { position: relative; z-index: 1; }
        .header h1 { font-size: 3.5rem; margin-bottom: 20px; font-weight: 300; }
        .header p { font-size: 1.3rem; opacity: 0.9; max-width: 600px; margin: 0 auto; }
        .version-badge { display: inline-block; background: rgba(255, 255, 255, 0.2); padding: 8px 16px; border-radius: 20px; font-size: 0.9rem; margin-top: 20px; }

        /* --- 导航栏 --- */
        .nav { background: white; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); position: sticky; top: 0; z-index: 100; }
        .nav-content { display: flex; justify-content: space-between; align-items: center; padding: 15px 0; }
        .nav-links { display: flex; list-style: none; gap: 30px; }
        .nav-links a { text-decoration: none; color: #2c3e50; font-weight: 500; transition: color 0.3s; }
        .nav-links a:hover { color: #06b6d4; }

        /* --- 主要内容 --- */
        .main-content { padding: 60px 0; }
        .section { margin-bottom: 80px; }
        .section-title { font-size: 2.5rem; margin-bottom: 20px; color: #2c3e50; text-align: center; }
        .section-subtitle { font-size: 1.2rem; color: #7f8c8d; text-align: center; margin-bottom: 50px; }

        /* --- 步骤卡片 --- */
        .steps { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin-top: 40px; }
        .step { background: white; border-radius: 15px; padding: 30px; text-align: center; position: relative; border: 2px solid #e9ecef; transition: transform 0.3s, box-shadow 0.3s; }
        .step:hover { transform: translateY(-5px); box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); }
        .step-number { position: absolute; top: -15px; left: 50%; transform: translateX(-50%); background: #06b6d4; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; }
        .step-title { font-size: 1.3rem; margin-bottom: 15px; color: #2c3e50; }
        .step-description { color: #7f8c8d; }

        /* --- 截图样式 --- */
        .screenshot { background: #f8f9fa; border-radius: 10px; padding: 20px; margin: 30px 0; text-align: center; border: 2px dashed #dee2e6; }
        .screenshot img { max-width: 100%; border-radius: 8px; }
        .screenshot-caption { color: #6c757d; font-style: italic; margin-top: 8px; }

        /* --- 代码块 --- */
        .code-block { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 8px; font-family: 'Courier New', monospace; margin: 20px 0; overflow-x: auto; }

        /* --- 提示框 --- */
        .tip, .warning, .info { padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid; }
        .tip { background: #f0f9ff; border-color: #0ea5e9; color: #0c4a6e; }
        .warning { background: #fef3c7; border-color: #f59e0b; color: #92400e; }
        .info { background: #ecfdf5; border-color: #10b981; color: #065f46; }

        /* --- 响应式 --- */
        @media (max-width: 768px) {
            .header h1 { font-size: 2.5rem; }
            .nav-links { display: none; }
            .steps { grid-template-columns: 1fr; }
        }
        
        /* --- 底部 --- */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0;
            text-align: center;
        }
        
        /* --- FAQ样式 --- */
        .faq-item {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .faq-question {
            color: #06b6d4;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .faq-answer {
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1><i class="fas fa-calendar-check"></i> 测试计划与任务执行</h1>
                <p>完整的从计划创建到结果展示全流程指南</p>
                <div class="version-badge">v2.0 用户手册</div>
            </div>
        </div>
    </header>

    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container">
            <div class="nav-content">
                <div class="logo"><strong>目录</strong></div>
                <ul class="nav-links">
                    <li><a href="#plan-create">创建测试计划</a></li>
                    <li><a href="#plan-add-steps">新增步骤</a></li>
                    <li><a href="#plan-import-cmd">引入命令</a></li>
                    <li><a href="#plan-order">调整顺序</a></li>
                    <li><a href="#plan-vars">变量配置</a></li>
                    <li><a href="#task-exec">任务执行</a></li>
                    <li><a href="#exec-records">查看记录</a></li>
                    <li><a href="#smart-extract">智能提取</a></li>
                    <li><a href="#result-display">结果展示</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 创建测试计划 -->
            <section id="plan-create" class="section">
                <h2 class="section-title">1. 创建测试计划</h2>
                <p class="section-subtitle">在开始之前，需要先创建一个新的测试计划。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">进入测试计划页面</h3>
                        <p class="step-description">点击系统菜单中的 <strong>模型存储 &gt; 发布计划</strong>，进入列表页。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">点击"新建发布计划"</h3>
                        <p class="step-description">右上角 <strong>新建发布计划</strong> 按钮，用于创建新的发布计划。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">填写计划信息</h3>
                        <p class="step-description">输入计划名称、描述、起止时间等基本信息。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <h3 class="step-title">保存</h3>
                        <p class="step-description">点击 <strong>确定</strong>，发布计划即创建完成。</p>
                    </div>
                </div>

                <div class="tip"><strong>💡 小贴士：</strong> 建议使用统一命名规范，如 <code>模型名称-版本-发布时间</code>，便于后续管理和查找。</div>
            </section>

            <!-- 新增步骤 -->
            <section id="plan-add-steps" class="section">
                <h2 class="section-title">2. 新增测试任务</h2>
                <p class="section-subtitle">在发布计划中添加具体的测试任务。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">进入甘特图页面</h3>
                        <p class="step-description">在发布计划列表中点击计划行的 <strong>甘特图</strong> 按钮。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">点击"新增任务"</h3>
                        <p class="step-description">点击页面右上角的 <strong>新增任务</strong> 按钮。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">填写任务信息</h3>
                        <p class="step-description">输入模型名称、测试人员、模型类型、GPU型号、时间范围、优先级等。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <h3 class="step-title">保存任务</h3>
                        <p class="step-description">点击 <strong>确定</strong> 将任务添加到计划中。</p>
                    </div>
                </div>
            </section>

            <!-- 引入命令 -->
            <section id="plan-import-cmd" class="section">
                <h2 class="section-title">3. Excel批量导入</h2>
                <p class="section-subtitle">通过Excel模板，快速批量导入测试任务。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">下载模板</h3>
                        <p class="step-description">点击页面右上角的 <strong>下载模板</strong> 按钮。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">填写模板</h3>
                        <p class="step-description">按照Excel模板格式填写测试任务信息。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">上传文件</h3>
                        <p class="step-description">点击 <strong>从Excel导入</strong> 按钮，选择填写好的Excel文件。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <h3 class="step-title">确认导入</h3>
                        <p class="step-description">系统会自动验证数据并导入，如有错误会提示。</p>
                    </div>
                </div>

                <div class="info"><strong>✅ 提示：</strong> Excel导入时，模型名称和人员名称为必填项，其他字段可选。如果存在相同的模型名称+人员名称组合，系统会更新现有任务。</div>
            </section>

            <!-- 调整顺序 -->
            <section id="plan-order" class="section">
                <h2 class="section-title">4. 调整任务顺序</h2>
                <p class="section-subtitle">通过拖拽调整任务执行顺序和时间安排。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">拖拽任务</h3>
                        <p class="step-description">在甘特图中按住任务条，将其移动到目标时间位置。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">调整时间范围</h3>
                        <p class="step-description">拖拽任务条的左右边缘，调整任务的起止时间。</p>
                    </div>
                </div>
            </section>

            <!-- 变量配置 -->
            <section id="plan-vars" class="section">
                <h2 class="section-title">5. 任务状态管理</h2>
                <p class="section-subtitle">更新任务状态和进度。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">编辑任务</h3>
                        <p class="step-description">点击任务行的 <strong>编辑</strong> 按钮进入编辑界面。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">更新状态</h3>
                        <p class="step-description">修改任务状态（待开始、进行中、已完成等）和进度百分比。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">保存更改</h3>
                        <p class="step-description">点击 <strong>确定</strong> 保存任务状态和进度更新。</p>
                    </div>
                </div>
            </section>

            <!-- 任务执行 -->
            <section id="task-exec" class="section">
                <h2 class="section-title">6. 关联测试用例</h2>
                <p class="section-subtitle">为测试任务关联测试用例集进行执行。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">进入任务详情</h3>
                        <p class="step-description">在任务列表中点击任务行的 <strong>测试用例</strong> 按钮。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">关联用例集</h3>
                        <p class="step-description">选择合适的测试用例集并与任务关联。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">执行测试</h3>
                        <p class="step-description">在测试用例界面中执行测试步骤并跟踪完成状态。</p>
                    </div>
                </div>
            </section>

            <!-- 查看记录 -->
            <section id="exec-records" class="section">
                <h2 class="section-title">7. 查看统计信息</h2>
                <p class="section-subtitle">查看测试计划执行统计和资源使用情况。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">返回模型存储首页</h3>
                        <p class="step-description">导航回到 <strong>模型存储</strong> 首页。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">查看统计概览</h3>
                        <p class="step-description">在首页查看总体概况、人员效率、GPU资源和风险预警等统计信息。</p>
                    </div>
                </div>
            </section>

            <!-- 智能提取结果 -->
            <section id="smart-extract" class="section">
                <h2 class="section-title">8. 周报导出</h2>
                <p class="section-subtitle">导出HTML格式的测试周报。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">进入甘特图页面</h3>
                        <p class="step-description">在发布计划列表中点击计划行的 <strong>甘特图</strong> 按钮。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">点击导出周报</h3>
                        <p class="step-description">点击页面右上角的 <strong>导出周报</strong> 按钮。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">查看报告</h3>
                        <p class="step-description">系统会自动生成HTML格式的周报文件供下载和查看。</p>
                    </div>
                </div>
            </section>

            <!-- 结果展示 -->
            <section id="result-display" class="section">
                <h2 class="section-title">9. 常见问题</h2>
                <p class="section-subtitle">解答使用过程中的常见问题</p>

                <div style="max-width: 800px; margin: 0 auto;">
                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何修改已创建的测试任务？</h4>
                        <p class="faq-answer">A: 在甘特图页面的任务列表中，点击对应任务行的"编辑"按钮，即可修改任务信息。修改后点击确定保存即可。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: Excel导入失败怎么办？</h4>
                        <p class="faq-answer">A: 请检查Excel文件格式是否正确，确保模型名称和人员名称不为空。如果仍有问题，请下载最新的导入模板重新填写。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 周报显示的时间范围是什么？</h4>
                        <p class="faq-answer">A: 周报显示的是当前周（周一到周日）的数据，包括在此时间范围内有活动的所有测试任务。</p>
                    </div>

                    <div class="faq-item">
                        <h4 class="faq-question">Q: 如何删除不需要的发布计划？</h4>
                        <p class="faq-answer">A: 在发布计划列表页面，点击对应计划行的"删除"按钮。注意：删除计划会同时删除该计划下的所有测试任务，请谨慎操作。</p>
                    </div>
                </div>

                <div class="tip"><strong>🎉 恭喜：</strong> 通过以上步骤，您已完成测试计划的全流程操作！</div>
            </section>

        </div>
    </main>

    <!-- 底部 -->
    <footer class="footer">
        <div class="container">
            &copy; 2024 模型存储系统
        </div>
    </footer>
</body>
</html>