# 开发环境配置
from .settings import *

# 开发环境特定配置
DEBUG = True
ALLOWED_HOSTS = ['*']

# 开发者后门配置
ENABLE_DEVELOPER_BACKDOOR = True
DEVELOPER_BACKDOOR_TOKEN = "1"

# 开发环境数据库配置（如果需要不同的数据库）
# DATABASES = {
#     'default': {
#         'ATOMIC_REQUESTS': True,
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'spug_dev',  # 开发环境数据库
#         'USER': 'root',
#         'PASSWORD': 'spug.cc',
#         'HOST': '127.0.0.1',  # 本地数据库
#         'PORT': '3306',
#         'OPTIONS': {
#             'charset': 'utf8mb4',
#             'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
#         }
#     }
# }

# 开发环境日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'DEBUG',
    },
}

print("🔧 使用开发环境配置")
