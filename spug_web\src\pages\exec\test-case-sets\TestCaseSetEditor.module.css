/* 测试用例集编辑器样式 */

.dragHandle {
  cursor: move;
  color: #1890ff;
  font-size: 16px;
  transition: all 0.3s ease;
}

.dragHandle:hover {
  color: #40a9ff;
  transform: scale(1.1);
}

/* 表格样式优化 */
.ant-table-tbody > tr.ant-table-row:hover > td {
  background: #f5f5f5;
}

.ant-table-tbody > tr > td {
  padding: 12px 8px;
  vertical-align: top;
}

/* 输入框样式 */
.ant-input,
.ant-input:focus,
.ant-input-focused {
  border-color: #d9d9d9;
  box-shadow: none;
}

.ant-input:hover {
  border-color: #40a9ff;
}

.ant-input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 文本域样式 */
.ant-input {
  resize: vertical;
  min-height: 32px;
}

/* 可编辑单元格样式 */
.editableCell {
  position: relative;
  cursor: text;
  min-height: 32px;
  padding: 4px 8px;
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all 0.2s ease;
  background-color: transparent;
  word-break: break-word;
  white-space: pre-wrap;
}

.editableCell:hover {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
}

.editableCell.editing {
  padding: 0;
  border: none;
  background-color: transparent;
}

.editableCell .placeholder {
  color: #bfbfbf;
  font-style: italic;
}

/* 开关样式 */
.ant-switch {
  background-color: #00000040;
}

.ant-switch-checked {
  background-color: #1890ff;
}

/* 按钮样式 */
.ant-btn-text.ant-btn-dangerous:hover {
  background-color: #fff2f0;
  border-color: #ffccc7;
}

/* 标签页样式 */
.ant-tabs-tab {
  font-weight: 500;
}

.ant-tabs-tab-active {
  font-weight: 600;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-form-item-required::before {
  color: #ff4d4f;
}

/* 警告框样式 */
.ant-alert-info {
  border: 1px solid #91d5ff;
  background-color: #e6f7ff;
}

.ant-alert-info .ant-alert-icon {
  color: #1890ff;
}

/* 面包屑样式 */
.ant-breadcrumb {
  font-size: 14px;
}

.ant-breadcrumb a {
  color: #1890ff;
  text-decoration: none;
}

.ant-breadcrumb a:hover {
  color: #40a9ff;
}

/* 卡片样式 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.ant-card-head-title {
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-table-scroll {
    overflow-x: auto;
  }
  
  .ant-col-12 {
    width: 100%;
  }
  
  .ant-space {
    flex-wrap: wrap;
  }
}

/* 拖拽时的样式 */
.sortable-helper {
  background: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
}

.sortable-helper td {
  background: #fff !important;
}

/* 排序占位符样式 */
.sortable-placeholder {
  background: #f0f9ff;
  border: 2px dashed #1890ff;
  opacity: 0.5;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  background-color: rgba(0, 0, 0, 0.85);
  border-radius: 4px;
}

/* 确认框样式 */
.ant-popconfirm-inner-content {
  padding: 12px 16px;
}

.ant-popconfirm-buttons {
  text-align: right;
  margin-top: 8px;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

.ant-empty-description {
  color: #999;
  font-size: 14px;
}

/* 加载状态样式 */
.ant-spin-container {
  position: relative;
}

.ant-spin-blur {
  opacity: 0.5;
  pointer-events: none;
}

/* 标题样式 */
.ant-typography h3 {
  color: #262626;
  font-weight: 600;
}

/* 间距调整 */
.ant-space-item {
  display: flex;
  align-items: center;
}

/* 表格滚动条样式 */
.ant-table-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ant-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.ant-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
