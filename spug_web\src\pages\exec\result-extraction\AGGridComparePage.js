import React, { useState, useEffect } from 'react';
import { Card, Button, message, Space, Spin, Breadcrumb, Tag, Statistic, Row, Col, Table, Progress } from 'antd';
import { ArrowLeftOutlined, DownloadOutlined, ReloadOutlined, BarChartOutlined, SwapOutlined, TrophyOutlined } from '@ant-design/icons';
import { useHistory } from 'react-router-dom';
import { http } from 'libs';
import * as XLSX from 'xlsx';

// Polyfill for process (needed for XLSX in browser environment)
if (typeof window !== 'undefined' && !window.process) {
  window.process = { env: {} };
}

// 自定义样式
const styles = {
  tableRowLight: {
    backgroundColor: '#ffffff',
  },
  tableRowDark: {
    backgroundColor: '#fafafa',
  },
  baselineCell: {
    backgroundColor: '#f0f5ff',
  },
  positiveChange: {
    color: '#52c41a',
    fontWeight: 'bold',
  },
  negativeChange: {
    color: '#ff4d4f',
    fontWeight: 'bold',
  }
};

// 差异值渲染器
const DifferenceRenderer = ({ value, baselineValue, isBaseline }) => {
  if (isBaseline) {
    return (
      <span style={{
        fontWeight: 'bold',
        color: '#1890ff',
        backgroundColor: '#f0f5ff',
        padding: '2px 6px',
        borderRadius: '4px'
      }}>
        {value || '-'} <TrophyOutlined style={{ color: '#faad14' }} />
      </span>
    );
  }

  if (!value || !baselineValue || value === baselineValue) {
    return <span>{value || '-'}</span>;
  }

  // 计算相对差异百分比
  const numValue = parseFloat(value);
  const numBaseline = parseFloat(baselineValue);

  if (isNaN(numValue) || isNaN(numBaseline) || numBaseline === 0) {
    return <span>{value}</span>;
  }

  const diffPercent = ((numValue - numBaseline) / numBaseline * 100).toFixed(1);
  const isPositive = diffPercent > 0;
  const color = isPositive ? '#52c41a' : '#ff4d4f'; // 绿色为正，红色为负

  return (
    <span>
      {value}
      <span style={{
        color,
        fontSize: '12px',
        marginLeft: '4px',
        fontWeight: 'bold'
      }}>
        ({isPositive ? '+' : ''}{diffPercent}%)
      </span>
    </span>
  );
};

const AGGridComparePage = () => {
  const [loading, setLoading] = useState(false);
  const [compareData, setCompareData] = useState(null);
  const [tableData, setTableData] = useState([]);
  const [columns, setColumns] = useState([]);
  const history = useHistory();

  // 页面加载时获取对比数据
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const resultIds = urlParams.get('result_ids');
    
    if (resultIds) {
      loadCompareData(resultIds);
    } else {
      message.error('缺少对比参数');
      history.goBack();
    }
  }, [history]);

  // 加载对比数据
  const loadCompareData = async (resultIds) => {
    setLoading(true);
    try {
      const response = await http.get(`/api/exec/test-results/compare/?result_ids=${resultIds}&token=1`);

      if (response && response.success) {
        setCompareData(response.data);
        generateTableData(response.data);
        message.success('对比数据加载成功');
      } else {
        message.error(response.message || '对比数据加载失败');
      }
    } catch (error) {
      console.error('加载对比数据失败:', error);
      message.error('加载对比数据失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 生成表格数据和列配置
  const generateTableData = (data) => {
    if (!data || !data.results || !data.file_comparisons) {
      return;
    }

    // 生成列配置
    const newColumns = [
      {
        title: '文件名',
        dataIndex: 'filename',
        key: 'filename',
        fixed: 'left',
        width: 200,
        render: (text) => <strong>{text}</strong>
      },
      {
        title: '指标名称',
        dataIndex: 'metricName',
        key: 'metricName',
        fixed: 'left',
        width: 150,
        render: (text) => <span style={{ color: '#666' }}>{text}</span>
      }
    ];

    // 为每个测试结果添加列
    data.results.forEach((result, index) => {
      newColumns.push({
        title: (
          <Space>
            {result.plan_name}
            {result.is_baseline && <TrophyOutlined style={{ color: '#faad14' }} />}
          </Space>
        ),
        dataIndex: `result_${result.id}`,
        key: `result_${result.id}`,
        width: 180,
        render: (value, record) => {
          const baselineValue = record.baseline;
          return (
            <DifferenceRenderer
              value={value}
              baselineValue={baselineValue}
              isBaseline={result.is_baseline}
            />
          );
        }
      });
    });

    setColumns(newColumns);

    // 生成行数据
    const newTableData = [];

    data.file_comparisons.forEach(fileComp => {
      const baselineMetrics = fileComp.baseline_metrics || {};

      // 获取所有指标名称
      const allMetrics = new Set();
      fileComp.data.forEach(d => {
        Object.keys(d.metrics || {}).forEach(metric => allMetrics.add(metric));
      });

      // 为每个指标创建一行
      Array.from(allMetrics).forEach(metricName => {
        const row = {
          key: `${fileComp.filename}_${metricName}`,
          filename: fileComp.filename,
          metricName: getFieldDisplayName(metricName),
          baseline: baselineMetrics[metricName]
        };

        // 为每个结果添加数据
        data.results.forEach(result => {
          const resultData = fileComp.data.find(d => d.result_id === result.id);
          row[`result_${result.id}`] = (resultData && resultData.metrics && resultData.metrics[metricName]) || '-';
        });

        newTableData.push(row);
      });
    });

    setTableData(newTableData);
  };

  // 获取字段显示名称
  const getFieldDisplayName = (field) => {
    const fieldNames = {
      'successful_requests': '成功请求数',
      'benchmark_duration': '基准时长(s)',
      'total_input_tokens': '输入Token总数',
      'total_generated_tokens': '生成Token总数',
      'request_throughput': '请求吞吐量',
      'output_token_throughput': '输出Token吞吐量',
      'total_token_throughput': '总Token吞吐量',
      'mean_ttft': '平均TTFT(ms)',
      'median_ttft': '中位TTFT(ms)',
      'p99_ttft': 'P99 TTFT(ms)',
      'mean_tpot': '平均TPOT(ms)',
      'median_tpot': '中位TPOT(ms)',
      'p99_tpot': 'P99 TPOT(ms)'
    };
    return fieldNames[field] || field;
  };

  // 导出数据到Excel
  const exportData = () => {
    if (tableData.length === 0) {
      message.warning('没有数据可导出');
      return;
    }

    try {
      // 创建工作簿
      const wb = XLSX.utils.book_new();

      // 准备导出数据
      const exportData = tableData.map(row => {
        const newRow = {
          '文件名': row.filename,
          '指标名称': row.metricName
        };

        // 添加每个结果的数据
        if (compareData && compareData.results) {
          compareData.results.forEach(result => {
            newRow[result.plan_name] = row[`result_${result.id}`];
          });
        }

        return newRow;
      });

      // 创建工作表
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '测试结果对比');

      // 导出文件
      const fileName = `测试结果对比_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);

      message.success('数据导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败: ' + error.message);
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Breadcrumb style={{ marginBottom: 16 }}>
        <Breadcrumb.Item>
          <a onClick={() => history.goBack()}>测试结果</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>AG-Grid对比分析</Breadcrumb.Item>
      </Breadcrumb>

      <Spin spinning={loading}>
        {compareData ? (
          <>
            {/* 对比概览 */}
            <Card 
              title={
                <Space>
                  <SwapOutlined />
                  <span>测试结果对比分析</span>
                  <Tag color="blue">{(compareData.results && compareData.results.length) || 0} 个结果</Tag>
                  <Tag color="green">{(compareData.file_comparisons && compareData.file_comparisons.length) || 0} 个文件</Tag>
                </Space>
              }
              style={{ marginBottom: 16 }}
            >
              <Row gutter={16}>
                {compareData.results && compareData.results.map((result, index) => (
                  <Col span={12} key={result.id}>
                    <Card size="small" style={{ backgroundColor: result.is_baseline ? '#f0f5ff' : '#fafafa' }}>
                      <Statistic
                        title={
                          <Space>
                            {result.plan_name}
                            {result.is_baseline && <Tag color="blue">基准</Tag>}
                          </Space>
                        }
                        value={result.total_metrics}
                        suffix="个指标"
                        prefix={<BarChartOutlined />}
                      />
                      <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                        <div>任务: {result.task_name || '未关联'}</div>
                        <div>创建时间: {result.created_at}</div>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card>

            {/* 对比数据表格 */}
            <Card
              title="详细对比数据"
              extra={
                <Space>
                  <Button
                    icon={<ArrowLeftOutlined />}
                    onClick={() => history.goBack()}
                  >
                    返回
                  </Button>
                  <Button
                    icon={<DownloadOutlined />}
                    onClick={exportData}
                    disabled={tableData.length === 0}
                  >
                    导出Excel
                  </Button>
                </Space>
              }
            >
              <Table
                columns={columns}
                dataSource={tableData}
                pagination={{
                  pageSize: 20,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 个指标`,
                  pageSizeOptions: ['10', '20', '50', '100']
                }}
                scroll={{ x: 1200, y: 600 }}
                size="small"
                bordered
                rowClassName={(record, index) => {
                  return index % 2 === 0 ? 'table-row-light' : 'table-row-dark';
                }}
              />
            </Card>
          </>
        ) : (
          <Card>
            <div style={{ textAlign: 'center', padding: '50px' }}>
              <p>正在加载对比数据...</p>
            </div>
          </Card>
        )}
      </Spin>
    </div>
  );
};

export default AGGridComparePage;
