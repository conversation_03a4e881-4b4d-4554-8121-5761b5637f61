{"name": "h3c_heterogeneous_ops", "version": "1.0.0", "private": true, "dependencies": {"@ant-design/icons": "^4.3.0", "@tanstack/react-table": "^8.10.7", "ace-builds": "^1.42.0", "ag-grid-community": "^34.0.2", "ag-grid-react": "^34.0.2", "antd": "4.21.5", "axios": "^0.21.0", "bizcharts": "^3.5.9", "history": "^4.10.1", "lodash": "^4.17.19", "mobx": "^5.15.6", "mobx-react": "^6.3.0", "moment": "^2.24.0", "react": "^16.13.1", "react-ace": "^9.5.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^16.13.1", "react-file-manager": "^1.2.4", "react-router-dom": "^5.2.0", "react-scripts": "3.4.3", "react-sortable-hoc": "^2.0.0", "recharts": "^2.15.3", "xlsx": "^0.18.5", "xterm": "^4.6.0", "xterm-addon-fit": "^0.5.0"}, "scripts": {"start": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-app-rewired start", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider GENERATE_SOURCEMAP=false react-app-rewired build", "test": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.10.5", "@stagewise/toolbar": "^0.4.8", "buffer": "^6.0.3", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.1", "customize-cra": "^1.0.0", "http-proxy-middleware": "0.19.2", "less": "^3.12.2", "less-loader": "^7.1.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "react-app-rewired": "^2.1.6", "stream-browserify": "^3.0.0", "util": "^0.12.5"}}