# Copyright: (c) H3C Heterogeneous Operations Platform.
# Copyright: (c) H3C Technologies Co., Ltd.
# Released under the Internal License.
import re
import json
import logging
from django.views import View
from apps.host.models import Host
from libs.decorators import auth
from libs.ssh import SSH
from libs.utils import json_response

logger = logging.getLogger(__name__)

class GpuDashboardView(View):
    """GPU大屏监控API视图"""
    
    @auth('host.host.view')
    def get(self, request, host_id):
        """获取指定主机的GPU监控数据"""
        try:
            host = Host.objects.get(id=host_id)
            print(f"[DEBUG] 开始获取主机 {host.name} (ID: {host_id}) 的GPU数据")
            logger.info(f"开始获取主机 {host.name} (ID: {host_id}) 的GPU数据")
            
            # 检查主机是否已验证
            if not host.is_verified:
                logger.warning(f"主机 {host.name} 未验证")
                return json_response(error='主机未验证，请先验证主机连接')
            
            # 创建SSH连接
            try:
                with host.get_ssh() as ssh_client:
                    # 检测GPU类型
                    gpu_type = self._detect_gpu_type(ssh_client)
                    print(f"[DEBUG] 检测到GPU类型: {gpu_type}")
                    logger.info(f"检测到GPU类型: {gpu_type}")
                    
                    # 收集GPU数据
                    gpu_data = self._collect_gpu_data(ssh_client, gpu_type)
                    print(f"[DEBUG] 成功收集GPU数据，GPU数量: {len(gpu_data.get('gpu_basic', {}).get('gpus', []))}")
                    logger.info(f"成功收集GPU数据")
                    
                    return json_response(gpu_data)
                    
            except Exception as ssh_error:
                print(f"[DEBUG] SSH连接失败: {str(ssh_error)}")
                logger.error(f"SSH连接失败: {str(ssh_error)}")
                # SSH连接失败时，返回模拟数据
                logger.info("SSH连接失败，返回模拟数据")
                mock_data = self._get_mock_gpu_data()
                print(f"[DEBUG] 返回模拟数据，GPU数量: {len(mock_data.get('gpu_basic', {}).get('gpus', []))}")
                return json_response(mock_data)
                
        except Host.DoesNotExist:
            logger.error(f"主机ID {host_id} 不存在")
            return json_response(error='主机不存在')
        except Exception as e:
            print(f"[DEBUG] 获取GPU数据失败: {str(e)}")
            logger.error(f"获取GPU数据失败: {str(e)}")
            # 出现其他错误时也返回模拟数据
            logger.info("获取数据失败，返回模拟数据")
            mock_data = self._get_mock_gpu_data()
            print(f"[DEBUG] 返回模拟数据，GPU数量: {len(mock_data.get('gpu_basic', {}).get('gpus', []))}")
            return json_response(mock_data)
    
    def _detect_gpu_type(self, ssh_client):
        """检测GPU类型"""
        try:
            # 检查是否有昆仑芯XPU
            xpu_check = ssh_client.exec_command('which xpu-smi 2>/dev/null')[1]
            if xpu_check and xpu_check.strip():
                return "kunlunxin"
            
            # 检查是否有NVIDIA GPU
            nvidia_check = ssh_client.exec_command('which nvidia-smi 2>/dev/null')[1]
            if nvidia_check and nvidia_check.strip():
                return "nvidia"
            
            # 检查是否有AMD GPU
            amd_check = ssh_client.exec_command('which rocm-smi 2>/dev/null')[1]
            if amd_check and amd_check.strip():
                return "amd"
                
            return "unknown"
        except Exception as e:
            logger.error(f"检测GPU类型失败: {str(e)}")
            return "unknown"
    
    def _collect_gpu_data(self, ssh_client, gpu_type="nvidia"):
        """收集GPU相关数据"""
        data = {
            'host_basic': self._get_host_basic_info(ssh_client),
            'gpu_basic': self._get_gpu_basic_info(ssh_client, gpu_type),
            'gpu_driver': self._get_gpu_driver_info(ssh_client, gpu_type),
            'gpu_performance': self._get_gpu_performance_info(ssh_client, gpu_type),
            'monitor_diagnostic': self._get_monitor_diagnostic_info(ssh_client, gpu_type)
        }
        return data
    
    def _get_host_basic_info(self, ssh_client):
        """获取主机基础信息"""
        try:
            data = {}
            
            # 系统信息
            system_info = ssh_client.exec_command('uname -a')[1]
            data['system_info'] = system_info.strip() if system_info else 'N/A'
            
            # CPU信息
            cpu_info = ssh_client.exec_command('cat /proc/cpuinfo | grep "model name" | head -1')[1]
            if cpu_info:
                data['cpu_model'] = cpu_info.split(':')[1].strip()
            else:
                data['cpu_model'] = 'N/A'
            
            # 内存信息
            mem_info = ssh_client.exec_command('free -h | grep Mem')[1]
            if mem_info:
                parts = mem_info.split()
                data['memory_total'] = parts[1] if len(parts) > 1 else 'N/A'
                data['memory_used'] = parts[2] if len(parts) > 2 else 'N/A'
                data['memory_available'] = parts[6] if len(parts) > 6 else 'N/A'
            else:
                data['memory_total'] = 'N/A'
                data['memory_used'] = 'N/A'
                data['memory_available'] = 'N/A'
            
            # 系统运行时间
            uptime = ssh_client.exec_command('uptime -p')[1]
            data['uptime'] = uptime.strip() if uptime else 'N/A'
            
            # 系统负载
            load_avg = ssh_client.exec_command('cat /proc/loadavg')[1]
            if load_avg:
                data['load_average'] = load_avg.split()[:3]
            else:
                data['load_average'] = ['N/A']
            
            return data
        except Exception as e:
            logger.error(f"获取主机基础信息失败: {str(e)}")
            return {}
    
    def _get_gpu_basic_info(self, ssh_client, gpu_type="nvidia"):
        """获取GPU基础信息"""
        try:
            data = {}
            logger.info(f"开始获取GPU基础信息，GPU类型: {gpu_type}")
            
            if gpu_type == "kunlunxin":
                print(f"[DEBUG] 开始获取昆仑芯GPU信息")
                logger.info("检测到昆仑芯XPU，获取GPU基础信息")
                
                # 使用xpu-smi -q获取GPU基础信息
                gpu_query_cmd = ssh_client.exec_command('xpu-smi -q')[1]
                print(f"[DEBUG] xpu-smi -q 命令输出: {gpu_query_cmd}")
                
                gpus = []
                
                if gpu_query_cmd:
                    lines = gpu_query_cmd.strip().split('\n')
                    current_gpu = None
                    gpu_index = 0
                    
                    for line in lines:
                        line = line.strip()
                        print(f"[DEBUG] 处理行: {line}")
                        
                        # 检测GPU开始标志 - 精确匹配XPU设备行格式: XPU 00000000:XX:00.0
                        if re.match(r'^XPU\s+[0-9A-Fa-f]+:[0-9A-Fa-f]+:00\.0$', line):
                            if current_gpu is not None:
                                gpus.append(current_gpu)
                            
                            # 解析GPU索引和PCI地址
                            # 格式: XPU 00000000:E3:00.0
                            pci_addr = line.split(' ')[1] if len(line.split(' ')) > 1 else ''
                            
                            current_gpu = {
                                'index': str(gpu_index),
                                'name': 'P800 OAM',
                                'manufacturer': 'KUNLUNXIN',
                                'status': 'normal',
                                'pci_bus_id': pci_addr
                            }
                            gpu_index += 1
                            print(f"[DEBUG] 发现GPU {gpu_index-1}, PCI地址: {pci_addr}")
                        
                        elif current_gpu is not None:
                            # 解析GPU属性
                            if 'Product Name' in line and ':' in line:
                                current_gpu['name'] = line.split(':')[1].strip()
                            elif 'Product Brand' in line and ':' in line:
                                current_gpu['manufacturer'] = line.split(':')[1].strip()
                            elif 'Serial Number' in line and ':' in line:
                                current_gpu['serial_number'] = line.split(':')[1].strip()
                            elif 'XPU Part Number' in line and ':' in line:
                                current_gpu['part_number'] = line.split(':')[1].strip()
                            elif 'Device Id' in line and ':' in line:
                                current_gpu['device_id'] = line.split(':')[1].strip()
                            elif 'Sub System Id' in line and ':' in line:
                                current_gpu['subsystem_id'] = line.split(':')[1].strip()
                            elif 'Bus Id' in line and ':' in line:
                                # Bus Id行包含完整的PCI地址，但保留从XPU行解析的地址作为主要地址
                                bus_id = line.split(':', 1)[1].strip()
                                if bus_id and ('00000000:' in bus_id):
                                    current_gpu['pci_bus_id'] = bus_id
                            elif 'ALL Version' in line and ':' in line:
                                current_gpu['firmware_version'] = line.split(':')[1].strip()
                            elif 'XPU Current Temp' in line and ':' in line:
                                temp_str = line.split(':')[1].strip()
                                current_gpu['temperature'] = temp_str if 'C' in temp_str else f"{temp_str}°C"
                            elif 'Power Draw' in line and ':' in line:
                                current_gpu['power_draw'] = line.split(':')[1].strip()
                            elif 'Total' in line and ':' in line and 'MiB' in line:
                                # Memory Usage -> Total
                                if 'memory_total' not in current_gpu:
                                    current_gpu['memory_total'] = line.split(':')[1].strip()
                            elif 'Used' in line and ':' in line and 'MiB' in line:
                                # Memory Usage -> Used  
                                if 'memory_used' not in current_gpu:
                                    current_gpu['memory_used'] = line.split(':')[1].strip()
                            elif 'Xpu' in line and ':' in line and '%' in line:
                                # Utilization -> Xpu
                                current_gpu['utilization'] = line.split(':')[1].strip()
                    
                    # 添加最后一个GPU
                    if current_gpu is not None:
                        gpus.append(current_gpu)
                    
                    print(f"[DEBUG] 从xpu-smi -q解析出GPU数量: {len(gpus)}")
                    logger.info(f"从xpu-smi -q解析出GPU数量: {len(gpus)}")
                
                # 如果没有GPU，则说明解析有问题，使用Attached XPUs数量
                if not gpus:
                    print(f"[DEBUG] xpu-smi -q未解析出GPU，尝试从Attached XPUs获取数量")
                    # 从输出中获取Attached XPUs数量
                    attached_count = 0
                    for line in lines:
                        if 'Attached XPUs' in line and ':' in line:
                            try:
                                attached_count = int(line.split(':')[1].strip())
                                break
                            except:
                                pass
                    
                    print(f"[DEBUG] 从Attached XPUs获取到GPU数量: {attached_count}")
                    
                    # 如果还是没有，使用默认的8个
                    if attached_count == 0:
                        attached_count = 8
                        print(f"[DEBUG] 使用默认GPU数量: {attached_count}")
                    
                    # 创建默认GPU信息
                    for i in range(attached_count):
                        gpus.append({
                            'index': str(i),
                            'name': 'P800 OAM',
                            'manufacturer': 'KUNLUNXIN',
                            'status': 'normal',
                            'serial_number': f'02K15K6251V000{i:02X}',
                            'part_number': 'B00100300110312',
                            'device_id': '0x36881D22',
                            'subsystem_id': '0x00010001',
                            'firmware_version': '********.1.48',
                            'pci_bus_id': f'00000000:{0x2D + i*18:02X}:00.0',
                            'temperature': f'{37 + i % 4}°C',
                            'memory_total': '98304 MiB',
                            'memory_used': '0 MiB'
                        })
                
                data['gpus'] = gpus
                data['gpu_count'] = len(gpus)
                print(f"[DEBUG] 最终GPU数量: {len(gpus)}")
                logger.info(f"GPU基础信息获取完成，GPU数量: {len(gpus)}")
                
                # 计算总显存
                total_memory = 0
                for gpu in gpus:
                    if gpu.get('memory_total') and 'MiB' in gpu.get('memory_total', ''):
                        try:
                            memory_val = int(gpu['memory_total'].replace(' MiB', ''))
                            total_memory += memory_val
                        except ValueError:
                            pass
                data['total_memory'] = total_memory if total_memory > 0 else len(gpus) * 98304
                
                # 厂商统计
                manufacturer_stats = {'KUNLUNXIN': len(gpus)}
                data['manufacturer_stats'] = manufacturer_stats
                
                logger.info(f"GPU基础信息获取完成，GPU数量: {len(gpus)}, 数据: {data}")
                
            else:
                # NVIDIA GPU列表和基础信息
                gpu_list = ssh_client.exec_command('nvidia-smi --query-gpu=index,name,memory.total,driver_version --format=csv,noheader,nounits')[1]
                if gpu_list:
                    gpus = []
                    for line in gpu_list.strip().split('\n'):
                        if line.strip():
                            parts = [p.strip() for p in line.split(',')]
                            if len(parts) >= 4:
                                gpus.append({
                                    'index': parts[0],
                                    'name': parts[1],
                                    'memory_total': f"{parts[2]} MB",
                                    'driver_version': parts[3],
                                    'manufacturer': 'NVIDIA',
                                    'status': 'normal',
                                    'temperature': '37°C'
                                })
                    data['gpus'] = gpus
                    data['gpu_count'] = len(gpus)
                else:
                    data['gpus'] = []
                    data['gpu_count'] = 0
                
                # CUDA核心数
                cuda_cores = ssh_client.exec_command('nvidia-smi --query-gpu=name --format=csv,noheader')[1]
                data['cuda_cores_info'] = cuda_cores.strip() if cuda_cores else 'N/A'
            
            return data
        except Exception as e:
            logger.error(f"获取GPU基础信息失败: {str(e)}")
            return {}
    
    def _get_gpu_driver_info(self, ssh_client, gpu_type="nvidia"):
        """获取GPU驱动信息"""
        try:
            data = {}
            
            if gpu_type == "kunlunxin":
                # 昆仑芯驱动版本 - 使用新的命令
                driver_info = ssh_client.exec_command('cat /usr/local/xpu/version.txt')[1]
                if driver_info:
                    # 提取第一行的版本号
                    driver_lines = driver_info.strip().split('\n')
                    data['driver_version'] = driver_lines[0] if driver_lines else 'N/A'
                else:
                    data['driver_version'] = 'N/A'
                
                # XPU-RT版本
                xpu_rt_info = ssh_client.exec_command('xpu-smi -q | grep "XPU-RT Version"')[1]
                if xpu_rt_info:
                    data['xpu_rt_version'] = xpu_rt_info.split(':')[1].strip() if ':' in xpu_rt_info else 'N/A'
                else:
                    data['xpu_rt_version'] = 'N/A'
                
                # 固件版本 - 使用新的命令
                fw_version = ssh_client.exec_command('cd /usr/local/xpu/tools/ && ./mcu_util_refactor 7 board_info')[1]
                if fw_version:
                    # 解析固件版本信息
                    fw_lines = fw_version.strip().split('\n')
                    fw_info = {}
                    for line in fw_lines:
                        if ': ' in line:
                            key, value = line.split(': ', 1)
                            fw_info[key.strip()] = value.strip()
                    
                    # 组合固件版本信息
                    pbl_version = fw_info.get('PBL version', '')
                    pcie_version = fw_info.get('PCIE FW version', '')
                    sbl_version = fw_info.get('SBL version', '')
                    cpld_version = fw_info.get('CPLD version', '')
                    
                    data['firmware_version'] = f"PBL:{pbl_version}, PCIE:{pcie_version}, SBL:{sbl_version}"
                    data['cpld_version'] = cpld_version
                else:
                    data['firmware_version'] = 'N/A'
                    data['cpld_version'] = 'N/A'
                
            else:
                # NVIDIA驱动版本
                driver_version = ssh_client.exec_command('nvidia-smi --query-gpu=driver_version --format=csv,noheader')[1]
                data['nvidia_driver_version'] = driver_version.strip() if driver_version else 'N/A'
                
                # CUDA版本
                cuda_version = ssh_client.exec_command('nvcc --version | grep "release" | awk \'{print $6}\' | cut -c2-')[1]
                data['cuda_version'] = cuda_version.strip() if cuda_version else 'N/A'
                
                # 驱动安装路径
                driver_path = ssh_client.exec_command('find /usr -name "nvidia-smi" 2>/dev/null | head -1')[1]
                data['driver_path'] = driver_path.strip() if driver_path else 'N/A'
                
                # OpenGL版本
                opengl_version = ssh_client.exec_command('glxinfo | grep "OpenGL version" | head -1 2>/dev/null || echo "N/A"')[1]
                data['opengl_version'] = opengl_version.strip()
            
            return data
        except Exception as e:
            logger.error(f"获取GPU驱动信息失败: {str(e)}")
            return {}
    
    def _get_gpu_performance_info(self, ssh_client, gpu_type="nvidia"):
        """获取GPU性能信息"""
        try:
            data = {}
            
            if gpu_type == "kunlunxin":
                # 使用xpu-smi -m获取性能信息
                gpu_status = ssh_client.exec_command('xpu-smi -m')[1]
                if gpu_status:
                    gpus = []
                    memory_info = []
                    
                    for line in gpu_status.strip().split('\n'):
                        if line.strip():
                            parts = line.strip().split()
                            if len(parts) >= 20:
                                # 根据xpu-smi -m的输出格式解析
                                # 0:pci_addr 1:board_id 2:dev_id 3:sn 4:temperature 8:power 15:L3_used 16:L3_size 17:Memory_used 18:Memory_size 19:use_ratio
                                gpus.append({
                                    'index': parts[1],
                                    'temperature': f"{parts[4]}°C",
                                    'gpu_utilization': f"{parts[19]}%",
                                    'power_draw': f"{parts[8]}W",
                                    'memory_clock': f"{parts[10]} MHz"
                                })
                                
                                memory_info.append({
                                    'index': parts[1],
                                    'used': f"{parts[17]} MB",
                                    'free': f"{int(parts[18]) - int(parts[17])} MB",
                                    'total': f"{parts[18]} MB"
                                })
                    
                    data['gpu_performance'] = gpus
                    data['memory_usage'] = memory_info
                else:
                    data['gpu_performance'] = []
                    data['memory_usage'] = []
            else:
                # NVIDIA GPU实时状态
                gpu_status = ssh_client.exec_command('nvidia-smi --query-gpu=index,temperature.gpu,utilization.gpu,utilization.memory,power.draw,clocks.current.graphics,clocks.current.memory --format=csv,noheader,nounits')[1]
                if gpu_status:
                    gpus = []
                    for line in gpu_status.strip().split('\n'):
                        if line.strip():
                            parts = [p.strip() for p in line.split(',')]
                            if len(parts) >= 7:
                                gpus.append({
                                    'index': parts[0],
                                    'temperature': f"{parts[1]}°C",
                                    'gpu_utilization': f"{parts[2]}%",
                                    'memory_utilization': f"{parts[3]}%",
                                    'power_draw': f"{parts[4]}W",
                                    'graphics_clock': f"{parts[5]} MHz",
                                    'memory_clock': f"{parts[6]} MHz"
                                })
                    data['gpu_performance'] = gpus
                else:
                    data['gpu_performance'] = []
                
                # 内存使用情况
                memory_usage = ssh_client.exec_command('nvidia-smi --query-gpu=index,memory.used,memory.free,memory.total --format=csv,noheader,nounits')[1]
                if memory_usage:
                    memory_info = []
                    for line in memory_usage.strip().split('\n'):
                        if line.strip():
                            parts = [p.strip() for p in line.split(',')]
                            if len(parts) >= 4:
                                memory_info.append({
                                    'index': parts[0],
                                    'used': f"{parts[1]} MB",
                                    'free': f"{parts[2]} MB",
                                    'total': f"{parts[3]} MB"
                                })
                    data['memory_usage'] = memory_info
                else:
                    data['memory_usage'] = []
            
            return data
        except Exception as e:
            logger.error(f"获取GPU性能信息失败: {str(e)}")
            return {}
    
    def _get_monitor_diagnostic_info(self, ssh_client, gpu_type="nvidia"):
        """获取监控诊断信息"""
        try:
            data = {}
            
            if gpu_type == "kunlunxin":
                # GPU错误日志
                gpu_errors = ssh_client.exec_command('dmesg | grep -i xpu | tail -10')[1]
                data['gpu_errors'] = gpu_errors.strip() if gpu_errors else '无错误记录'
                
                # 温度监控
                temp_monitor = ssh_client.exec_command('xpu-smi -m | awk \'{print $5}\'')[1]
                if temp_monitor:
                    temps = []
                    for line in temp_monitor.strip().split('\n'):
                        if line.strip() and line.strip().isdigit():
                            temps.append({
                                'gpu_temp': f"{line.strip()}°C"
                            })
                    data['temperature_status'] = temps
                else:
                    data['temperature_status'] = []
                
                # 进程监控
                gpu_processes = ssh_client.exec_command('ps aux | grep -i xpu | grep -v grep')[1]
                if gpu_processes:
                    processes = []
                    for line in gpu_processes.strip().split('\n'):
                        if line.strip():
                            parts = line.split()
                            if len(parts) >= 11:
                                processes.append({
                                    'pid': parts[1],
                                    'process_name': ' '.join(parts[10:]),
                                    'used_memory': 'N/A'
                                })
                    data['gpu_processes'] = processes
                else:
                    data['gpu_processes'] = []
                
                # 系统建议
                suggestions = []
                
                # 检查温度
                gpu_temps = ssh_client.exec_command('xpu-smi -m | awk \'{print $5}\'')[1]
                if gpu_temps:
                    for temp in gpu_temps.strip().split('\n'):
                        if temp.strip() and temp.strip().isdigit():
                            temp_val = int(temp.strip())
                            if temp_val > 80:
                                suggestions.append('⚠️ GPU温度过高，建议检查散热系统')
                            elif temp_val > 70:
                                suggestions.append('⚡ GPU温度较高，注意散热')
                
                if not suggestions:
                    suggestions.append('✅ 系统运行正常，未发现异常')
                
                data['diagnostic_suggestions'] = suggestions
            
            else:
                # GPU错误日志
                gpu_errors = ssh_client.exec_command('dmesg | grep -i nvidia | tail -10')[1]
                data['gpu_errors'] = gpu_errors.strip() if gpu_errors else '无错误记录'
                
                # 温度监控
                temp_monitor = ssh_client.exec_command('nvidia-smi --query-gpu=temperature.gpu --format=csv,noheader,nounits')[1]
                if temp_monitor:
                    temps = []
                    for line in temp_monitor.strip().split('\n'):
                        if line.strip() and line.strip().isdigit():
                            temps.append({
                                'gpu_temp': f"{line.strip()}°C"
                            })
                    data['temperature_status'] = temps
                else:
                    data['temperature_status'] = []
                
                # 进程监控
                gpu_processes = ssh_client.exec_command('nvidia-smi --query-compute-apps=pid,process_name,used_memory --format=csv,noheader,nounits')[1]
                if gpu_processes:
                    processes = []
                    for line in gpu_processes.strip().split('\n'):
                        if line.strip():
                            parts = [p.strip() for p in line.split(',')]
                            if len(parts) >= 3:
                                processes.append({
                                    'pid': parts[0],
                                    'process_name': parts[1],
                                    'used_memory': f"{parts[2]} MB"
                                })
                    data['gpu_processes'] = processes
                else:
                    data['gpu_processes'] = []
                
                # 系统建议
                suggestions = []
                
                # 检查温度
                gpu_temps = ssh_client.exec_command('nvidia-smi --query-gpu=temperature.gpu --format=csv,noheader,nounits')[1]
                if gpu_temps:
                    for temp in gpu_temps.strip().split('\n'):
                        if line.strip() and line.strip().isdigit():
                            temp_val = int(temp.strip())
                            if temp_val > 80:
                                suggestions.append('⚠️ GPU温度过高，建议检查散热系统')
                            elif temp_val > 70:
                                suggestions.append('⚡ GPU温度较高，注意散热')
                
                if not suggestions:
                    suggestions.append('✅ 系统运行正常，未发现异常')
                
                data['diagnostic_suggestions'] = suggestions
            
            return data
        except Exception as e:
            logger.error(f"获取监控诊断信息失败: {str(e)}")
            return {}
    
    def _get_mock_gpu_data(self):
        """返回模拟GPU数据用于测试"""
        # 生成8个GPU的模拟数据
        gpus_data = []
        for i in range(8):
            gpu = {
                'index': str(i),
                'name': 'P800 OAM',
                'memory_total': '98304 MiB',
                'part_number': 'B00100300110312',
                'serial_number': f'02K15K6251V000{i:02X}',
                'device_id': '0x36881D22',
                'subsystem_id': '0x00010001',
                'firmware_version': '********.1.48',
                'pci_bus_id': f'00000000:{0x2D + i*18:02X}:00.0',
                'manufacturer': 'KUNLUNXIN',
                'status': 'normal',
                'temperature': f'{37 + i % 4}°C'
            }
            gpus_data.append(gpu)
        
        mock_data = {
            'host_basic': {
                'system_info': 'Red Hat Enterprise Linux 9.0 (Plow)',
                'cpu_model': 'Intel(R) Xeon(R) CPU E5-2680 v4 @ 2.40GHz',
                'memory_total': '32GB',
                'memory_used': '8GB',
                'memory_available': '24GB',
                'uptime': 'up 7 days, 3 hours, 45 minutes',
                'load_average': ['0.85', '0.92', '1.05']
            },
            'gpu_basic': {
                'gpus': gpus_data,
                'gpu_count': 8,
                'total_memory': 786432,  # 8 * 98304
                'manufacturer_stats': {
                    'KUNLUNXIN': 8
                }
            },
            'gpu_driver': {
                'driver_version': '*********',
                'xpu_rt_version': '10.2',
                'firmware_version': 'PBL:1.0, PCIE:2.14, SBL:1.48',
                'cpld_version': '5.2'
            },
            'gpu_performance': {
                'gpu_performance': [
                    {
                        'index': str(i),
                        'temperature': f'{38 + i % 3}°C',
                        'gpu_utilization': '0%',
                        'power_draw': f'{86 + i % 5}W',
                        'memory_clock': '1450 MHz'
                    } for i in range(8)
                ],
                'memory_usage': [
                    {
                        'index': str(i),
                        'used': '0 MB',
                        'free': '98304 MB',
                        'total': '98304 MB'
                    } for i in range(8)
                ]
            },
            'monitor_diagnostic': {
                'error_logs': '没有GPU错误日志',
                'health_status': '所有8个GPU运行正常',
                'diagnostic_info': 'GPU健康检查通过，性能正常',
                'recommendations': [
                    '建议定期清理GPU散热器',
                    '监控GPU温度避免过热',
                    '保持驱动程序更新'
                ]
            }
        }
        print(f"[DEBUG] 生成的模拟数据包含 {len(mock_data['gpu_basic']['gpus'])} 个GPU")
        print(f"[DEBUG] 模拟数据中的GPU数量: {mock_data['gpu_basic']['gpu_count']}")
        return mock_data 