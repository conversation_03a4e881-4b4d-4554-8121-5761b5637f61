# Spug服务器部署方案（官方方式）

## 1. 服务器信息

| 项目 | 信息 |
|------|------|
| 服务器IP | ************ |
| 操作系统 | Ubuntu 22.04.5 LTS |
| 部署目录 | /opt/spug |
| 访问地址 | http://************ |

## 2. 架构说明

采用官方推荐的生产环境架构：

- **API服务**：运行在9001端口，使用gunicorn
- **WebSocket服务**：运行在9002端口，使用daphne
- **Worker服务**：后台任务处理
- **Monitor服务**：系统监控
- **Scheduler服务**：定时任务调度
- **前端**：React构建的静态文件，由Nginx提供服务
- **反向代理**：Nginx处理静态文件和API代理
- **数据库**：MySQL（**********，共享数据库）
- **缓存**：本地Redis服务
- **进程管理**：Supervisor管理所有服务进程

## 3. 服务端口

| 服务 | 端口 | 说明 |
|------|------|------|
| Nginx | 80 | 对外提供服务 |
| API服务 | 9001 | 内网API服务 |
| WebSocket服务 | 9002 | 内网WebSocket服务 |
| Redis | 6379 | 本地缓存服务 |

## 4. 管理脚本

### 4.1 简单重启脚本（推荐）⭐

**脚本路径**：`/opt/spug/simple_restart.sh`

**功能**：快速重启所有Spug服务

**使用场景**：日常重启

```bash
cd /opt/spug && ./simple_restart.sh
```

### 4.2 完整重启脚本

**脚本路径**：`/opt/spug/restart_spug.sh`

**功能**：代码更新 + 数据库迁移 + 前端重新构建 + 重启服务

**使用场景**：大版本更新或代码变更

```bash
cd /opt/spug && ./restart_spug.sh
```

### 4.3 服务管理脚本

**脚本路径**：`/opt/spug/manage_spug.sh`

**功能**：全面的服务管理

```bash
./manage_spug.sh start    # 启动服务
./manage_spug.sh stop     # 停止服务
./manage_spug.sh restart  # 重启服务
./manage_spug.sh status   # 查看状态
./manage_spug.sh logs     # 查看日志
```

## 5. 重要文件路径

| 类型 | 路径 |
|------|------|
| 项目根目录 | `/opt/spug/` |
| 前端源码 | `/opt/spug/spug_web/` |
| 后端源码 | `/opt/spug/spug_api/` |
| 配置覆盖文件 | `/opt/spug/spug_api/spug/overrides.py` |
| 日志目录 | `/opt/spug/logs/` |
| Nginx配置 | `/etc/nginx/sites-available/spug` |
| Supervisor配置 | `/etc/supervisor/conf.d/spug.conf` |

## 6. 日志文件

| 日志类型 | 路径 |
|----------|------|
| API服务日志 | `/opt/spug/logs/api.log` |
| WebSocket日志 | `/opt/spug/logs/ws.log` |
| Worker日志 | `/opt/spug/logs/worker.log` |
| Monitor日志 | `/opt/spug/logs/monitor.log` |
| Scheduler日志 | `/opt/spug/logs/scheduler.log` |
| Nginx日志 | `/var/log/nginx/` |

## 7. 常用操作

### 7.1 代码更新重启

```bash
# 日常更新（推荐）
cd /opt/spug && ./simple_restart.sh

# 大版本更新
cd /opt/spug && ./restart_spug.sh
```

### 7.2 查看服务状态

```bash
# 查看所有服务状态
./manage_spug.sh status

# 查看supervisor状态
supervisorctl status

# 查看特定服务日志
supervisorctl tail -f spug-api
```

### 7.3 服务管理

```bash
# 重启特定服务
supervisorctl restart spug-api

# 停止所有Spug服务
supervisorctl stop spug-*

# 启动所有Spug服务
supervisorctl start spug-*
```

### 7.4 数据库操作

```bash
# 进入API目录
cd /opt/spug/spug_api

# 创建迁移文件
python3 manage.py makemigrations

# 执行数据库迁移
python3 manage.py migrate

# 创建超级用户
python3 manage.py user add -u admin -p spug.cc -s -n 管理员
```

## 8. 故障排查

| 问题 | 解决方案 |
|------|----------|
| 无法访问网站 | 检查Nginx状态：`systemctl status nginx` |
| API请求失败 | 检查API服务：`supervisorctl status spug-api` |
| WebSocket连接失败 | 检查WS服务：`supervisorctl status spug-ws` |
| 数据库连接失败 | 检查数据库配置和网络连通性 |
| 服务启动失败 | 查看对应服务日志：`supervisorctl tail spug-api` |

## 9. 配置文件说明

### 9.1 数据库配置
文件：`/opt/spug/spug_api/spug/overrides.py`
```python
DEBUG = False
DATABASES = {
    'default': {
        'ATOMIC_REQUESTS': True,
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'spug',
        'USER': 'root',
        'PASSWORD': 'Aa123',
        'HOST': '**********',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'sql_mode': 'STRICT_TRANS_TABLES',
        }
    }
}
```

### 9.2 Nginx配置
文件：`/etc/nginx/sites-available/spug`
- API代理：`/api/` -> `http://127.0.0.1:9001`
- WebSocket代理：`/api/ws/` -> `http://127.0.0.1:9002`

## 10. 注意事项

1. **服务管理**：
   - 使用supervisor管理所有后台服务
   - 服务会自动重启，无需手动干预

2. **代码同步**：
   - 使用`overrides.py`覆盖配置，避免修改`settings.py`
   - 可以安全地进行git pull，不会有配置冲突

3. **性能优化**：
   - 使用了官方推荐的多进程架构
   - 前端启用了gzip压缩

4. **监控**：
   - 所有服务日志都有记录
   - 可以通过supervisor查看实时状态

## 11. 优势

1. **官方标准**：完全按照官方文档部署
2. **稳定可靠**：使用supervisor进程管理，自动重启
3. **易于维护**：提供了完整的管理脚本
4. **日志完整**：所有服务都有独立的日志文件
5. **扩展性好**：可以轻松添加新的服务组件
