import React, { useState, useRef, useEffect } from 'react';
import { 
  Button, 
  Space, 
  Divider, 
  Upload, 
  message, 
  Modal,
  Input,
  Select,
  Row,
  Col
} from 'antd';
import { 
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  PictureOutlined,
  UndoOutlined,
  RedoOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;

export default function SimpleRichTextEditor({ 
  value, 
  onChange, 
  placeholder = '请输入内容...', 
  height = 300 
}) {
  const [content, setContent] = useState(value || '');
  const editorRef = useRef(null);

  useEffect(() => {
    if (value !== content) {
      setContent(value || '');
    }
  }, [value]);

  const handleContentChange = (e) => {
    const newContent = e.target.value;
    setContent(newContent);
    if (onChange) {
      onChange(newContent);
    }
  };

  const insertText = (before, after = '') => {
    const textarea = editorRef.current?.resizableTextArea?.textArea;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);
    
    const newText = content.substring(0, start) + 
                   before + selectedText + after + 
                   content.substring(end);
    
    setContent(newText);
    if (onChange) {
      onChange(newText);
    }

    // 重新设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(
        start + before.length, 
        start + before.length + selectedText.length
      );
    }, 0);
  };

  const handleBold = () => insertText('**', '**');
  const handleItalic = () => insertText('*', '*');
  const handleUnderline = () => insertText('<u>', '</u>');
  
  const handleHeading = (level) => {
    const prefix = '#'.repeat(level) + ' ';
    insertText(prefix);
  };

  const handleList = (ordered = false) => {
    const prefix = ordered ? '1. ' : '- ';
    insertText('\n' + prefix);
  };

  const handleAlign = (alignment) => {
    const alignMap = {
      left: '<div style="text-align: left;">',
      center: '<div style="text-align: center;">',
      right: '<div style="text-align: right;">'
    };
    insertText(alignMap[alignment], '</div>');
  };

  const handleImageUpload = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageMarkdown = `\n![图片](${e.target.result})\n`;
      insertText(imageMarkdown);
    };
    reader.readAsDataURL(file);
    return false;
  };

  const insertTable = () => {
    const tableMarkdown = `
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 内容1 | 内容2 | 内容3 |
| 内容4 | 内容5 | 内容6 |
`;
    insertText(tableMarkdown);
  };

  const insertLink = () => {
    insertText('[链接文本](http://example.com)');
  };

  const toolbarStyle = {
    padding: '8px 12px',
    background: '#fafafa',
    borderBottom: '1px solid #f0f0f0',
    borderRadius: '6px 6px 0 0'
  };

  const editorStyle = {
    border: 'none',
    outline: 'none',
    resize: 'none',
    fontFamily: 'inherit',
    fontSize: '14px',
    lineHeight: '1.6',
    padding: '12px',
    minHeight: height - 50,
    borderRadius: '0 0 6px 6px'
  };

  const containerStyle = {
    border: '1px solid #d9d9d9',
    borderRadius: '6px',
    overflow: 'hidden',
    background: 'white'
  };

  return (
    <div style={containerStyle}>
      {/* 工具栏 */}
      <div style={toolbarStyle}>
        <Space split={<Divider type="vertical" />} wrap>
          {/* 撤销重做 */}
          <Space size="small">
            <Button 
              size="small" 
              icon={<UndoOutlined />} 
              title="撤销"
              onClick={() => document.execCommand('undo')}
            />
            <Button 
              size="small" 
              icon={<RedoOutlined />} 
              title="重做"
              onClick={() => document.execCommand('redo')}
            />
          </Space>

          {/* 文本格式 */}
          <Space size="small">
            <Button 
              size="small" 
              icon={<BoldOutlined />} 
              onClick={handleBold}
              title="粗体"
            />
            <Button 
              size="small" 
              icon={<ItalicOutlined />} 
              onClick={handleItalic}
              title="斜体"
            />
            <Button 
              size="small" 
              icon={<UnderlineOutlined />} 
              onClick={handleUnderline}
              title="下划线"
            />
          </Space>

          {/* 标题 */}
          <Space size="small">
            <Select
              size="small"
              defaultValue="normal"
              style={{ width: 80 }}
              onChange={(value) => {
                if (value !== 'normal') {
                  handleHeading(parseInt(value));
                }
              }}
            >
              <Option value="normal">正文</Option>
              <Option value="1">标题1</Option>
              <Option value="2">标题2</Option>
              <Option value="3">标题3</Option>
            </Select>
          </Space>

          {/* 对齐 */}
          <Space size="small">
            <Button 
              size="small" 
              icon={<AlignLeftOutlined />} 
              onClick={() => handleAlign('left')}
              title="左对齐"
            />
            <Button 
              size="small" 
              icon={<AlignCenterOutlined />} 
              onClick={() => handleAlign('center')}
              title="居中"
            />
            <Button 
              size="small" 
              icon={<AlignRightOutlined />} 
              onClick={() => handleAlign('right')}
              title="右对齐"
            />
          </Space>

          {/* 列表 */}
          <Space size="small">
            <Button 
              size="small" 
              icon={<OrderedListOutlined />} 
              onClick={() => handleList(true)}
              title="有序列表"
            />
            <Button 
              size="small" 
              icon={<UnorderedListOutlined />} 
              onClick={() => handleList(false)}
              title="无序列表"
            />
          </Space>

          {/* 插入功能 */}
          <Space size="small">
            <Button 
              size="small" 
              onClick={insertTable}
              title="插入表格"
            >
              表格
            </Button>
            <Button 
              size="small" 
              onClick={insertLink}
              title="插入链接"
            >
              链接
            </Button>
            <Upload
              accept="image/*"
              showUploadList={false}
              beforeUpload={handleImageUpload}
            >
              <Button 
                size="small" 
                icon={<PictureOutlined />}
                title="插入图片"
              />
            </Upload>
          </Space>
        </Space>
      </div>

      {/* 编辑区域 */}
      <TextArea
        ref={editorRef}
        value={content}
        onChange={handleContentChange}
        placeholder={placeholder}
        style={editorStyle}
        bordered={false}
      />
    </div>
  );
}
